name: Build Apps

on:
  workflow_call:
    inputs:
      matrix:
        description: 'The matrix configuration to run the build jobs'
        type: string
      base-branch-name:
        default: 'develop'
        description: 'The branch that should be used as BASE when looking for last successful workflow run, develop or main'
        type: string

jobs:
  build-app:
    name: ${{ matrix.app }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        app: ${{ fromJSON(inputs.matrix) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # Activates npm caching
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
        run: npx nx run ${{ matrix.app }}:build

      - name: Save build output
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.app }}
          if-no-files-found: ignore
          path: ./dist/apps/${{ matrix.app }}
