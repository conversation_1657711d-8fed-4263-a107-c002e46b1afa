name: Bump version and create release notes

on:
  workflow_call:

jobs:
  changelog:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Create changelog
        id: changelog
        uses: TriPSs/conventional-changelog-action@v5
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          output-file: 'false'

      - name: Create release
        uses: softprops/action-gh-release@v2
        if: ${{ steps.changelog.outputs.skipped == 'false' }}
        with:
          tag_name: ${{ steps.changelog.outputs.tag }}
          name: ${{ steps.changelog.outputs.tag }}
          body: ${{ steps.changelog.outputs.clean_changelog }}
