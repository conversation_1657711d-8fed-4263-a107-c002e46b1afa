name: Manual Deployment - stage

on:
  workflow_dispatch:

jobs:
  derive-affected:
    uses: ./.github/workflows/derive-affected-projects.yml
    with:
      only-affected: false

  build:
    needs: [derive-affected]
    if: needs.derive-affected.outputs.build-changes == 'true'
    uses: ./.github/workflows/build-matrix.yml
    with:
      matrix: ${{ needs.derive-affected.outputs.build-matrix }}

  deploy:
    needs: [derive-affected, build]
    uses: ./.github/workflows/deploy-eks.yml
    secrets: inherit
    with:
      tag-name: 'stage'

  # This allows us to have a branch protection rule for tests with matrix
  ci-result:
    name: CI Result
    runs-on: ubuntu-latest
    needs: [derive-affected, build, deploy]
    if: always()
    steps:
      - name: Run successful!
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: exit 0
      - name: Run failed!
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1
