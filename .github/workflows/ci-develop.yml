name: Continuous Integration (CI)

on:
  pull_request:
    branches:
      - develop
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  code-style:
    uses: ./.github/workflows/code-style.yml

  derive-affected:
    uses: ./.github/workflows/derive-affected-projects.yml

  test:
    needs: [derive-affected]
    if: needs.derive-affected.outputs.test-changes == 'true'
    uses: ./.github/workflows/test-matrix.yml
    with:
      matrix: ${{ needs.derive-affected.outputs.test-matrix }}
      save-coverage-report: true

  sonar:
    needs: [test, derive-affected]
    if: needs.derive-affected.outputs.test-changes == 'true'
    secrets: inherit
    uses: ./.github/workflows/sonar-matrix.yml
    with:
      matrix: ${{ needs.derive-affected.outputs.test-matrix }}

  # This allows us to have a branch protection rule for tests with matrix
  ci-result:
    name: CI Result
    runs-on: ubuntu-latest
    needs: [code-style, derive-affected, test, sonar]
    if: always()
    steps:
      - name: Run successful!
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: exit 0
      - name: Run failed!
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1
