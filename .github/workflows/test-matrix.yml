name: Test Matrix

on:
  workflow_call:
    inputs:
      matrix:
        description: 'The matrix configuration to run the test jobs'
        type: string
      base-branch-name:
        default: 'develop'
        description: 'The branch that should be used as BASE when looking for last successful workflow run, develop or main'
        type: string
      save-coverage-report:
        default: false
        description: 'Whether or not to save tests coverage report summary artifact'
        type: boolean

jobs:
  run-tests:
    name: ${{ matrix.app }}
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        app: ${{ fromJSON(inputs.matrix) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Execute tests
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
        run: npx nx run ${{ matrix.app }}:test --configuration=ci

      - name: Save tests coverage report summary
        if: ${{ inputs.save-coverage-report == true }}
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.app }}-coverage-summary
          if-no-files-found: ignore
          include-hidden-files: true
          path: ./coverage/${{ matrix.app }}
