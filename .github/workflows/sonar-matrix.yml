name: Quality Analysis

on:
  workflow_call:
    inputs:
      matrix:
        description: 'The matrix configuration to run the sonar jobs'
        type: string

jobs:
  sonar:
    name: ${{ matrix.app }}
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      actions: 'read'
    env:
      SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
    strategy:
      max-parallel: 2
      fail-fast: false
      matrix:
        app: ${{ fromJSON(inputs.matrix) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          # Activates npm caching
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download coverage report
        uses: actions/download-artifact@v4
        with:
          name: ${{ matrix.app }}-coverage-summary
          path: coverage/${{ matrix.app }}

      - name: Display structure of downloaded files
        run: |
          cd ./coverage
          ls -R

      - name: Check
        env:
          NODE_OPTIONS: '--max-old-space-size=4096'
        run: npx nx run ${{ matrix.app }}:sonar
