name: Code Style

on:
  workflow_call:
    inputs:
      base-branch-name:
        default: 'develop'
        description: 'The branch that should be used as BASE when looking for last successful workflow run, develop or main'
        type: string

jobs:
  style-check:
    runs-on: ubuntu-latest
    permissions:
      contents: 'read'
      actions: 'read'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Derive appropriate SHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: ${{ inputs.base-branch-name }}

      - name: Project Lint
        run: npx nx affected --base=$NX_BASE --head=$NX_HEAD --target=lint

      - name: Format Check
        run: npx nx format:check --base=$NX_BASE --head=$NX_HEAD
