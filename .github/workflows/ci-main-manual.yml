name: Manual Deploy - production

on:
  workflow_dispatch:

jobs:
  derive-affected:
    uses: ./.github/workflows/derive-affected-projects.yml
    with:
      base-branch-name: 'main'
      only-affected: false

  test:
    needs: [derive-affected]
    if: needs.derive-affected.outputs.test-changes == 'true'
    uses: ./.github/workflows/test-matrix.yml
    with:
      matrix: ${{ needs.derive-affected.outputs.test-matrix }}
      base-branch-name: 'main'

  build:
    needs: [derive-affected, test]
    if: needs.derive-affected.outputs.build-changes == 'true'
    uses: ./.github/workflows/build-matrix.yml
    with:
      matrix: ${{ needs.derive-affected.outputs.build-matrix }}
      base-branch-name: 'main'

  deploy:
    needs: [derive-affected, test, build]
    uses: ./.github/workflows/deploy-eks.yml
    secrets: inherit
    with:
      tag-name: 'production'

  # This allows us to have a branch protection rule for tests with matrix
  ci-result:
    name: CI Result
    runs-on: ubuntu-latest
    needs: [derive-affected, test, build, deploy]
    if: always()
    steps:
      - name: Run successful!
        if: ${{ !(contains(needs.*.result, 'failure')) }}
        run: exit 0
      - name: Run failed!
        if: ${{ contains(needs.*.result, 'failure') }}
        run: exit 1
