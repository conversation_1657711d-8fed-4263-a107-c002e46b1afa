import { Locale as KeycloakLocale } from '../value-objects/keycloak-locale.enum';
import { KeycloakUserCredential } from './keycloak-user-credentials.interface';

export interface KeycloakUser {
  id?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  emailVerified?: boolean;
  attributes?: {
    locale: KeycloakLocale[];
  };
  enabled?: boolean;
  totp?: boolean;
  createdTimestamp?: string;
  credentials?: KeycloakUserCredential[];
}
