import { KeycloakUser, KeycloakUserCredential } from '../interfaces';
import { Locale } from '../value-objects';

export class KeycloakCreateUserDTO implements KeycloakUser {
  id?: string;
  email: string;
  username: string;
  firstName: string;
  lastName?: string;
  enabled: boolean;
  emailVerified = true;
  totp = false;
  credentials: KeycloakUserCredential[] = [];
  attributes: {
    locale: Locale[];
  };

  constructor(email: string, firstName: string, lastName = '', locale: Locale = Locale.PT_BR, enabled = true) {
    this.attributes = { locale: [locale] };
    this.enabled = enabled;
    this.email = email;
    this.username = email;
    this.firstName = firstName;
    this.lastName = lastName;
    this.credentials = [];
  }

  setPassword(value: string, temporary = false) {
    this.credentials.push({ type: 'password', value, temporary });
  }
}
