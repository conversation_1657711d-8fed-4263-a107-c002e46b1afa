import { Test, TestingModule } from '@nestjs/testing';
import { KeycloakRepository } from './keycloak.repository';
import { ConfigService } from '@nestjs/config';
import { HTTP_CLIENT, HttpClient } from '../common';
import { KeycloakCreateUserDTO } from './dtos';
import { KeycloakFederatedIdentity, KeycloakTokenResponse, KeycloakUser } from './interfaces';
import { HttpMethod } from '../common/rest/http-client.interface';

describe('KeycloakRepository', () => {
  let repository: KeycloakRepository;
  let httpClientMock: jest.Mocked<HttpClient>;
  let configServiceMock: jest.Mocked<ConfigService>;

  const mockAuthConfig = {
    authServerUrl: 'https://keycloak.example.com',
    realm: 'test-realm',
    resource: 'test-client',
    secret: 'test-secret',
  };

  const mockTokenResponse: KeycloakTokenResponse = {
    access_token: 'mock-access-token',
    expires_in: 300,
    refresh_expires_in: 1800,
    token_type: 'Bearer',
    'not-before-policy': 0,
    scope: 'profile email',
  };

  const mockUser: KeycloakUser = {
    id: '123',
    username: '<EMAIL>',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User',
    enabled: true,
    emailVerified: true,
  };

  beforeEach(async () => {
    httpClientMock = {
      get: jest.fn(),
      post: jest.fn(),
      put: jest.fn(),
      delete: jest.fn(),
      patch: jest.fn(),
      makeRequest: jest.fn(),
    } as unknown as jest.Mocked<HttpClient>;

    configServiceMock = {
      get: jest.fn().mockReturnValue(mockAuthConfig),
    } as unknown as jest.Mocked<ConfigService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeycloakRepository,
        {
          provide: HTTP_CLIENT,
          useValue: httpClientMock,
        },
        {
          provide: ConfigService,
          useValue: configServiceMock,
        },
      ],
    }).compile();

    repository = module.get<KeycloakRepository>(KeycloakRepository);
  });

  it('should be defined', () => {
    expect(repository).toBeDefined();
  });

  describe('getAccessToken', () => {
    it('should return cached token if valid', async () => {
      // Set up private properties using any type assertion
      (repository as any).cachedToken = 'cached-token';
      (repository as any).tokenExpiryTime = Math.floor(Date.now() / 1000) + 3600; // Valid for 1 hour

      const token = await repository.getAccessToken();

      expect(token).toBe('cached-token');
      expect(httpClientMock.post).not.toHaveBeenCalled();
    });

    it('should fetch new token if no cached token exists', async () => {
      httpClientMock.post.mockResolvedValue(mockTokenResponse);

      const token = await repository.getAccessToken();

      expect(token).toBe(mockTokenResponse.access_token);
      expect(httpClientMock.post).toHaveBeenCalledWith(
        'https://keycloak.example.com/realms/test-realm/protocol/openid-connect/token',
        {
          client_id: 'test-client',
          client_secret: 'test-secret',
          grant_type: 'client_credentials',
        },
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        },
      );
    });

    it('should fetch new token if cached token is expired', async () => {
      // Set up expired token
      (repository as any).cachedToken = 'expired-token';
      (repository as any).tokenExpiryTime = Math.floor(Date.now() / 1000) - 60; // Expired 1 minute ago

      httpClientMock.post.mockResolvedValue(mockTokenResponse);

      const token = await repository.getAccessToken();

      expect(token).toBe(mockTokenResponse.access_token);
      expect(httpClientMock.post).toHaveBeenCalled();
    });
  });

  describe('getUserAccessToken', () => {
    it('should fetch user token with username and password', async () => {
      httpClientMock.post.mockResolvedValue(mockTokenResponse);

      const token = await repository.getUserAccessToken('<EMAIL>', 'password');

      expect(token).toBe(mockTokenResponse.access_token);
      expect(httpClientMock.post).toHaveBeenCalledWith(
        'https://keycloak.example.com/realms/test-realm/protocol/openid-connect/token',
        {
          client_id: 'test-client',
          client_secret: 'test-secret',
          grant_type: 'password',
          username: '<EMAIL>',
          password: 'password',
        },
        {
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        },
      );
    });
  });

  describe('createUser', () => {
    it('should create a user in Keycloak', async () => {
      const newUser = new KeycloakCreateUserDTO('<EMAIL>', 'New', 'User');
      httpClientMock.makeRequest.mockResolvedValue(mockUser);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      const result = await repository.createUser(newUser);

      expect(result).toEqual(mockUser);
      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.POST,
        'https://keycloak.example.com/admin/realms/test-realm/users',
        { Authorization: 'Bearer test-token' },
        newUser,
      );
    });
  });

  describe('getUserByEmail', () => {
    it('should return user when found by email', async () => {
      httpClientMock.makeRequest.mockResolvedValue([mockUser]);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      const result = await repository.getUserByEmail('<EMAIL>');

      expect(result).toEqual(mockUser);
      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.GET,
        'https://keycloak.example.com/admin/realms/test-realm/users?search=<EMAIL>',
        { Authorization: 'Bearer test-token' },
        null,
      );
    });

    it('should return null when user not found by email', async () => {
      httpClientMock.makeRequest.mockResolvedValue([]);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      const result = await repository.getUserByEmail('<EMAIL>');

      expect(result).toBeNull();
    });
  });

  describe('getUserById', () => {
    it('should return user when found by ID', async () => {
      httpClientMock.makeRequest.mockResolvedValue(mockUser);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      const result = await repository.getUserById('123');

      expect(result).toEqual(mockUser);
      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.GET,
        'https://keycloak.example.com/admin/realms/test-realm/users/123',
        { Authorization: 'Bearer test-token' },
        null,
      );
    });
  });

  describe('updateUser', () => {
    it('should update a user in Keycloak', async () => {
      const updatedUser: KeycloakUser = {
        ...mockUser,
        firstName: 'Updated',
      };
      httpClientMock.makeRequest.mockResolvedValue(undefined);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      await repository.updateUser('123', updatedUser);

      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.PUT,
        'https://keycloak.example.com/admin/realms/test-realm/users/123',
        { Authorization: 'Bearer test-token' },
        updatedUser,
      );
    });
  });

  describe('resetUserPassword', () => {
    it('should reset a user password in Keycloak', async () => {
      httpClientMock.makeRequest.mockResolvedValue({});

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      await repository.resetUserPassword('123', 'newPassword');

      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.PUT,
        'https://keycloak.example.com/admin/realms/test-realm/users/123/reset-password',
        { Authorization: 'Bearer test-token' },
        { type: 'password', value: 'newPassword', temporary: false },
      );
    });
  });

  describe('federated identity operations', () => {
    const mockFederatedIdentity: KeycloakFederatedIdentity = {
      identityProvider: 'google',
      userId: 'google-user-id',
      userName: '<EMAIL>',
    };

    it('should get federated identities', async () => {
      httpClientMock.makeRequest.mockResolvedValue([mockFederatedIdentity]);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      const result = await repository.getFederatedIdentity('123');

      expect(result).toEqual([mockFederatedIdentity]);
      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.GET,
        'https://keycloak.example.com/admin/realms/test-realm/users/123/federated-identity',
        { Authorization: 'Bearer test-token' },
        null,
      );
    });

    it('should add federated identity', async () => {
      httpClientMock.makeRequest.mockResolvedValue(undefined);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      await repository.addFederatedIdentity('123', mockFederatedIdentity);

      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.POST,
        'https://keycloak.example.com/admin/realms/test-realm/users/123/federated-identity/google',
        { Authorization: 'Bearer test-token' },
        mockFederatedIdentity,
      );
    });

    it('should delete federated identity', async () => {
      httpClientMock.makeRequest.mockResolvedValue(undefined);

      // Mock getAccessToken for this specific test
      jest.spyOn(repository, 'getAccessToken').mockResolvedValue('test-token');

      await repository.deleteFederatedIdentity('123', 'google');

      expect(httpClientMock.makeRequest).toHaveBeenCalledWith(
        HttpMethod.DELETE,
        'https://keycloak.example.com/admin/realms/test-realm/users/123/federated-identity/google',
        { Authorization: 'Bearer test-token' },
        null,
      );
    });
  });
});
