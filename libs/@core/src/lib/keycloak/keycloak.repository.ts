import { HTTP_CLIENT, HttpClient, HttpMethod } from '../common';
import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { KeycloakConnectConfig } from 'nest-keycloak-connect';
import { KeycloakCreateUserDTO } from './dtos';
import { KeycloakFederatedIdentity, KeycloakTokenResponse, KeycloakUser } from './interfaces';

/**
 * The `KeycloakRepository` class is responsible for interacting with Keycloak's REST API to manage user-related operations.
 * It provides methods to create, update, retrieve, and delete users, reset passwords, send verification emails,
 * and manage federated identities for users.
 *
 * This class handles communication with the Keycloak API by getting an access token for authentication
 * and then performing various operations related to user management, such as user creation, updates,
 * password resets, and federated identity management.
 */
@Injectable()
export class KeycloakRepository {
  private readonly baseUrl: string | null;
  private readonly realm: string | null;
  private readonly clientId: string | null;
  private readonly clientSecret: string | null;
  private readonly tokenEndpoint: string;

  private cachedToken: string | undefined = undefined;
  private tokenExpiryTime: number | undefined = undefined;

  constructor(
    @Inject(HTTP_CLIENT) private readonly httpclient: HttpClient,
    config: ConfigService,
  ) {
    const authConfig: KeycloakConnectConfig | undefined = config.get('auth');
    this.baseUrl = authConfig?.authServerUrl || null;
    this.realm = authConfig?.realm || null;
    this.clientId = authConfig?.resource || null;
    this.clientSecret = authConfig?.secret || null;
    this.tokenEndpoint = `${this.baseUrl}/realms/${this.realm}/protocol/openid-connect/token`;
  }

  /**
   * Retrieves the Keycloak access token for client credentials flow.
   * If the token is expired or not cached, it fetches a new one.
   * @returns The access token as a string.
   */
  public async getAccessToken(): Promise<string> {
    if (this.cachedToken && this.isTokenValid(this.cachedToken, this.tokenExpiryTime)) {
      return this.cachedToken;
    }

    const tokenData = await this.requestToken({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      grant_type: 'client_credentials',
    });

    this.cachedToken = tokenData.access_token;
    this.tokenExpiryTime = Math.floor(Date.now() / 1000) + tokenData.expires_in;

    return this.cachedToken;
  }

  /**
   * Retrieves a Keycloak user access token using username and password.
   * Always fetches a new token without caching.
   * @param username - The username of the user.
   * @param password - The password of the user.
   * @returns The users access token as a string.
   */
  public async getUserAccessToken(username: string, password: string): Promise<string> {
    const tokenData = await this.requestToken({
      client_id: this.clientId,
      client_secret: this.clientSecret,
      grant_type: 'password',
      username,
      password,
    });

    return tokenData.access_token;
  }

  /**
   * Checks if a cached token is still valid.
   * @param token - The cached token.
   * @param expiryTime - The expiry timestamp of the token.
   * @returns True if the token is still valid, otherwise false.
   */
  private isTokenValid(token?: string, expiryTime?: number): boolean {
    const currentTime = Math.floor(Date.now() / 1000);
    return token !== undefined && expiryTime !== undefined && currentTime < expiryTime;
  }

  /**
   * Makes a request to Keycloak to get an access token.
   * @param payload - The request body containing authentication details.
   * @returns The token response object containing access_token and expires_in.
   */
  private async requestToken(payload: Record<string, any>): Promise<KeycloakTokenResponse> {
    return this.httpclient.post<KeycloakTokenResponse>(this.tokenEndpoint, payload, {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    });
  }

  /**
   * Makes a request to the Keycloak API with the provided HTTP method, URL, and data.
   * @param method The HTTP method to use (GET, POST, PUT, DELETE).
   * @param url The URL for the API request.
   * @param data The request body (optional).
   * @returns The response from the API request.
   */
  private async makeRequest<T>(method: HttpMethod, url: string, data: any = null): Promise<T> {
    const token = await this.getAccessToken();
    const headers = { Authorization: `Bearer ${token}` };
    return this.httpclient.makeRequest<T>(method, url, headers, data);
  }

  /**
   * Creates a new user in Keycloak.
   * @param user The user data to be created.
   * @returns The created Keycloak user.
   */
  async createUser(user: KeycloakCreateUserDTO): Promise<KeycloakUser> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users`;
    return this.makeRequest<KeycloakUser>(HttpMethod.POST, url, user);
  }

  /**
   * Updates an existing user in Keycloak.
   * @param userId The ID of the user to be updated.
   * @param updatedUser The updated user data.
   * @returns A promise indicating when the update is completed.
   */
  async updateUser(userId: string, updatedUser: KeycloakUser): Promise<void> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}`;
    await this.makeRequest<void>(HttpMethod.PUT, url, updatedUser);
  }

  /**
   * Retrieves a user from Keycloak by their email address.
   * @param email The email of the user to search for.
   * @returns The Keycloak user corresponding to the email, or null if not found.
   */
  async getUserByEmail(email: string): Promise<KeycloakUser | null> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users?search=${email}`;
    const response = await this.makeRequest<KeycloakUser[]>(HttpMethod.GET, url);
    return response.length > 0 ? response[0] : null;
  }

  /**
   * Retrieves a user from Keycloak by their user ID.
   * @param userId The ID of the user to retrieve.
   * @returns The Keycloak user corresponding to the user ID.
   */
  async getUserById(userId: string): Promise<KeycloakUser> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}`;
    return this.makeRequest<KeycloakUser>(HttpMethod.GET, url);
  }

  /**
   * Resets the password for a user in Keycloak.
   * @param userId The ID of the user whose password will be reset.
   * @param newPassword The new password for the user.
   * @param temporary Whether the password is temporary (default is false).
   * @returns A promise indicating when the password reset is completed.
   */
  async resetUserPassword(userId: string, newPassword: string, temporary = false): Promise<any> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}/reset-password`;
    const passwordPayload = { type: 'password', value: newPassword, temporary };
    return this.makeRequest<any>(HttpMethod.PUT, url, passwordPayload);
  }

  /**
   * Sends a verification email to a user in Keycloak.
   * @param userId The ID of the user to send the verification email to.
   * @returns A promise indicating when the email is sent.
   */
  async sendVerifyEmail(userId: string): Promise<void> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}/send-verify-email`;
    await this.makeRequest<void>(HttpMethod.PUT, url, null);
  }

  /**
   * Retrieves the federated identity of a user in Keycloak.
   * @param userId The ID of the user whose federated identity is to be retrieved.
   * @returns A list of federated identities associated with the user.
   */
  async getFederatedIdentity(userId: string): Promise<KeycloakFederatedIdentity[]> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}/federated-identity`;
    return this.makeRequest<KeycloakFederatedIdentity[]>(HttpMethod.GET, url);
  }

  /**
   * Adds a federated identity to a user in Keycloak.
   * @param userId The ID of the user to add the federated identity for.
   * @param federatedIdentity The federated identity data to add.
   * @returns A promise indicating when the federated identity is added.
   */
  async addFederatedIdentity(userId: string, federatedIdentity: KeycloakFederatedIdentity): Promise<void> {
    const provider = federatedIdentity.identityProvider;
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}/federated-identity/${provider}`;
    await this.makeRequest<void>(HttpMethod.POST, url, federatedIdentity);
  }

  /**
   * Deletes a federated identity from a user in Keycloak.
   * @param userId The ID of the user whose federated identity is to be deleted.
   * @param identityProvider The identity provider to remove.
   * @returns A promise indicating when the federated identity is deleted.
   */
  async deleteFederatedIdentity(userId: string, identityProvider: string): Promise<void> {
    const url = `${this.baseUrl}/admin/realms/${this.realm}/users/${userId}/federated-identity/${identityProvider}`;
    await this.makeRequest<void>(HttpMethod.DELETE, url);
  }
}
