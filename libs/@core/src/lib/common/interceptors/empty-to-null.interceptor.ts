import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class EmptyToNullInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    if (request.body && typeof request.body === 'object') {
      request.body = this.convertEmptyToNull(request.body);
    }

    return next.handle();
  }

  private convertEmptyToNull(value: any): any {
    if (Array.isArray(value)) {
      return value.map(this.convertEmptyToNull.bind(this));
    }

    if (value && typeof value === 'object') {
      return Object.fromEntries(
        Object.entries(value).map(([key, val]) => [
          key,
          typeof val === 'object' ? this.convertEmptyToNull(val) : val === '' ? null : val,
        ]),
      );
    }

    return value;
  }
}
