import { registerDecorator, ValidationOptions, ValidationArguments } from 'class-validator';
import { cpf } from 'cpf-cnpj-validator';

export function IsCPF(validationOptions?: ValidationOptions) {
  return function (target: any, propertyName: string | symbol) {
    registerDecorator({
      name: 'isCPF',
      target: target.constructor,
      propertyName: propertyName.toString(),
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments): boolean {
          return typeof value === 'string' && cpf.isValid(value);
        },
        defaultMessage(): string {
          return 'Invalid CPF';
        },
      },
    });
  };
}
