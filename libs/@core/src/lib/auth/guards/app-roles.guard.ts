import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { AuthUser } from '../../common';
import { AppRoles } from '../../common/decorators/app-roles.decorator';

@Injectable()
export class AppRolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredAppRoles = this.reflector.get(AppRoles, context.getHandler());
    if (!requiredAppRoles?.length) return true;

    const user: AuthUser = context.switchToHttp().getRequest().user;
    const userRoles: string[] = user?.roles || [];

    const roleSet = new Set(userRoles);

    return requiredAppRoles.some((role) => roleSet.has(role));
  }
}
