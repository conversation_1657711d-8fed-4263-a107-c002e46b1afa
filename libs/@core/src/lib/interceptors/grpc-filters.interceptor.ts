import { <PERSON><PERSON><PERSON><PERSON>, ExecutionContext, Injectable, NestInterceptor } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class GrpcFiltersInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler<unknown>): Observable<unknown> {
    const data = context.switchToRpc().getData();

    Object.keys(data?.filters ?? {}).forEach((key) => (data[`filter.${key}`] = data.filters[key].values));
    delete data?.filters;

    return next.handle().pipe(
      map((response) => {
        return response;
      }),
    );
  }
}
