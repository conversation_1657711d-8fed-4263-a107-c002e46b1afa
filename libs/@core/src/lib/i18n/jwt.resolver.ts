import { Injectable, ExecutionContext, Logger } from '@nestjs/common';
import { I18nResolver, I18nResolverOptions } from 'nestjs-i18n';

@Injectable()
export class JWTResolver implements I18nResolver {
  private readonly logger = new Logger('I18nService');
  constructor(
    @I18nResolverOptions()
    private readonly tokenHeaderName = 'authorization',
    private readonly jwtLangKey = 'locale',
  ) {}

  resolve(context: ExecutionContext) {
    return 'pt-BR';
    const req = context.switchToHttp().getRequest();

    let lang: string;

    if (req) {
      if (req.headers !== undefined && req.headers[this.tokenHeaderName] !== undefined) {
        const token = req.headers[this.tokenHeaderName];
        const decodedJwt = JSON.parse(atob(token.split('.')[1]));
        lang = decodedJwt[this.jwtLangKey];
      }
    }

    return lang;
  }
}
