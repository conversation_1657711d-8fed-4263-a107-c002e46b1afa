import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MessagesModule } from '../messages';
import { PROVIDERS } from './mobile-notifications.providers';
import { MobileNotificationsService } from './services/mobile-notifications.service';
import { MobileNotificationStrategy } from './notifications/mobile-notification-strategy';
import { KonquestOnboardingNotification } from './notifications/konquest-onboarding-notification';
import { KonquestInviteNotification } from './notifications/konquest-invite-notification';
import { SmartZapOnboardingNotification } from './notifications/smartzap-onboarding-notification';
import { SmartZapInviteNotification } from './notifications/smartzap-invite-notification';

@Module({
  imports: [ConfigModule, MessagesModule],
  providers: PROVIDERS,
  exports: [
    MobileNotificationsService,
    MobileNotificationStrategy,
    KonquestOnboardingNotification,
    KonquestInviteNotification,
    SmartZapOnboardingNotification,
    SmartZapInviteNotification,
  ],
})
export class MobileNotificationsModule {}
