import { Provider } from '@nestjs/common';
import { MobileNotificationsService } from './services/mobile-notifications.service';
import { KonquestOnboardingNotification } from './notifications/konquest-onboarding-notification';
import { KonquestInviteNotification } from './notifications/konquest-invite-notification';
import { MobileNotificationStrategy } from './notifications/mobile-notification-strategy';
import { SmartZapOnboardingNotification } from './notifications/smartzap-onboarding-notification';
import { SmartZapInviteNotification } from './notifications/smartzap-invite-notification';

export const PROVIDERS: Provider[] = [
  MobileNotificationsService,
  KonquestOnboardingNotification,
  KonquestInviteNotification,
  SmartZapOnboardingNotification,
  SmartZapInviteNotification,
  MobileNotificationStrategy,
];
