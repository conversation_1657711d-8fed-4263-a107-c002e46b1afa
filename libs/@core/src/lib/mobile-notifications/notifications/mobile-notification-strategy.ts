import { Injectable, Logger } from '@nestjs/common';
import { AbstractMobileNotification } from './abstract-mobile-notification';
import { KonquestOnboardingNotification } from './konquest-onboarding-notification';
import { KonquestInviteNotification } from './konquest-invite-notification';
import { SmartZapOnboardingNotification } from './smartzap-onboarding-notification';
import { SmartZapInviteNotification } from './smartzap-invite-notification';
import { MobileNotificationsService } from '../services/mobile-notifications.service';
import { SendMobileNotificationDto } from '../dtos/send-mobile-notification.dto';

@Injectable()
export class MobileNotificationStrategy {
  private readonly logger = new Logger(MobileNotificationStrategy.name);
  private notificationMap: Map<string, AbstractMobileNotification>;

  constructor(
    private mobileNotificationsService: MobileNotificationsService,
    private konquestOnboardingNotification: KonquestOnboardingNotification,
    private konquestInviteNotification: KonquestInviteNotification,
    private smartZapOnboardingNotification: SmartZapOnboardingNotification,
    private smartZapInviteNotification: SmartZapInviteNotification,
  ) {
    this.initializeNotificationMap();
  }

  private initializeNotificationMap(): void {
    this.notificationMap = new Map<string, AbstractMobileNotification>();

    this.registerNotification(this.konquestOnboardingNotification);
    this.registerNotification(this.konquestInviteNotification);
    this.registerNotification(this.smartZapOnboardingNotification);
    this.registerNotification(this.smartZapInviteNotification);
  }

  private registerNotification(notification: AbstractMobileNotification): void {
    this.notificationMap.set(notification.getTypeKey(), notification);
  }

  /**
   * Gets the notification handler for the given type key
   * @param typeKey The type key of the notification
   * @returns The notification handler or undefined if not found
   */
  public getNotificationHandler(typeKey: string): AbstractMobileNotification | undefined {
    return this.notificationMap.get(typeKey);
  }

  /**
   * Sends a notification using the appropriate handler based on the type key
   * @param typeKey The type key of the notification
   * @param recipientPhone The phone number of the recipient
   * @param contentData The content data to send
   * @param language The language code for the notification
   * @param workspaceId The ID of the workspace
   * @throws Error if no handler is found for the given type key
   */
  public async sendNotification(
    typeKey: string,
    recipientPhone: string,
    contentData: Record<string, string>,
    language: string,
    workspaceId: string,
  ): Promise<void> {
    const handler = this.getNotificationHandler(typeKey);

    if (!handler) {
      throw new Error(`No notification handler found for type key: ${typeKey}`);
    }

    try {
      const notificationDto: SendMobileNotificationDto = {
        recipientPhone,
        contentData,
        language,
        workspaceId,
      };

      await handler.sendNotification(notificationDto);
      this.logger.log(`Successfully sent notification of type: ${typeKey}`);
    } catch (error) {
      this.logger.error(`Error sending notification of type ${typeKey}: ${error.message}`);
      throw error;
    }
  }
}
