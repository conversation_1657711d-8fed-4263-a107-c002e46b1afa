import { Injectable } from '@nestjs/common';
import { AbstractMobileNotification } from './abstract-mobile-notification';
import { MobileNotificationsService } from '../services/mobile-notifications.service';

@Injectable()
export class KonquestInviteNotification extends AbstractMobileNotification {
  private static readonly TYPE_KEY = 'MYACCOUNT_KONQUEST_INVITE';

  // Content data mapping from numeric keys to Twilio expected keys
  private static readonly CONTENT_DATA_MAPPING = {
    '1': 'company',
    '2': 'user_login',
    '3': 'app_url',
  };

  constructor(mobileNotificationsService: MobileNotificationsService) {
    super(mobileNotificationsService);
  }

  public getTypeKey(): string {
    return KonquestInviteNotification.TYPE_KEY;
  }

  protected getRequiredContentDataKeys(): string[] {
    return Object.keys(KonquestInviteNotification.CONTENT_DATA_MAPPING);
  }

  protected validateContentData(contentData: Record<string, string>): boolean {
    return this.defaultValidateContentData(contentData);
  }

  protected mapContentData(contentData: Record<string, string>): Record<string, string> {
    const mappedContentData: Record<string, string> = {};

    // Map numeric keys to Twilio expected keys
    for (const [numericKey, twilioKey] of Object.entries(KonquestInviteNotification.CONTENT_DATA_MAPPING)) {
      if (contentData[numericKey]) {
        mappedContentData[twilioKey] = contentData[numericKey];
      }
    }

    return mappedContentData;
  }
}
