import { MobileNotificationsService } from '../services/mobile-notifications.service';
import { MobileNotificationMessageDto } from '../dtos/mobile-notification-message.dto';
import { SendMobileNotificationDto } from '../dtos/send-mobile-notification.dto';

export abstract class AbstractMobileNotification {
  constructor(protected mobileNotificationsService: MobileNotificationsService) {}

  /**
   * Validates if all required content data is present
   * @param contentData The content data to validate
   * @returns True if all required content data is present, false otherwise
   */
  protected abstract validateContentData(contentData: Record<string, string>): boolean;

  /**
   * Gets the required content data keys for this notification
   * @returns An array of required content data keys
   */
  protected abstract getRequiredContentDataKeys(): string[];

  /**
   * Maps the content data keys to the appropriate values for Twilio
   * @param contentData The content data to map
   * @returns The mapped content data
   */
  protected abstract mapContentData(contentData: Record<string, string>): Record<string, string>;

  /**
   * Gets the type key for this notification
   * @returns The type key
   */
  public abstract getTypeKey(): string;

  /**
   * Sends the notification
   * @param params The notification parameters
   * @throws Error if the content data is invalid
   */
  public async sendNotification(params: SendMobileNotificationDto): Promise<void> {
    if (!this.validateContentData(params.contentData)) {
      const requiredKeys = this.getRequiredContentDataKeys();
      throw new Error(
        `Invalid content data for notification type ${this.getTypeKey()}. Required keys: ${requiredKeys.join(', ')}`,
      );
    }

    const notification: MobileNotificationMessageDto = {
      recipientPhone: params.recipientPhone,
      typeKey: this.getTypeKey(),
      contentData: params.contentData,
      language: params.language,
      workspaceId: params.workspaceId,
    };

    await this.mobileNotificationsService.sendMobileNotification(notification);
  }

  /**
   * Default implementation of validateContentData
   * @param contentData The content data to validate
   * @returns True if all required content data is present, false otherwise
   */
  protected defaultValidateContentData(contentData: Record<string, string>): boolean {
    const requiredKeys = this.getRequiredContentDataKeys();
    return requiredKeys.every((key) => contentData[key] !== undefined && contentData[key] !== null);
  }
}
