import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

class PaginatedMetaDto {
  @Expose()
  @ApiProperty()
  itemsPerPage: number;

  @Expose()
  @ApiProperty({ required: false })
  totalItems?: number;

  @Expose()
  @ApiProperty({ required: false })
  currentPage?: number;

  @Expose()
  @ApiProperty({ required: false })
  totalPages?: number;

  @Expose()
  @ApiProperty({ type: 'array', items: { type: 'array', items: { type: 'string' } } })
  sortBy: [string, string][];

  @Expose()
  @ApiProperty({ type: [String] })
  searchBy: string[];

  @Expose()
  @ApiProperty()
  search: string;

  @Expose()
  @ApiProperty({ type: [String] })
  select: string[];

  @Expose()
  @ApiProperty({
    required: false,
    type: Object,
    additionalProperties: { type: 'string' },
  })
  filter?: Record<string, string | string[]>;

  @Expose()
  @ApiProperty({ required: false })
  cursor?: string;

  @Expose()
  @ApiProperty({ required: false })
  firstCursor?: string;

  @Expose()
  @ApiProperty({ required: false })
  lastCursor?: string;
}

class PaginatedLinksDto {
  @Expose()
  @ApiProperty({ required: false })
  first?: string;

  @Expose()
  @ApiProperty({ required: false })
  previous?: string;

  @Expose()
  @ApiProperty()
  current: string;

  @Expose()
  @ApiProperty({ required: false })
  next?: string;

  @Expose()
  @ApiProperty({ required: false })
  last?: string;
}

export class PaginatedResponseDto<T> {
  @Expose()
  @ApiProperty({ isArray: true })
  data: T[];

  @Expose()
  @Type(() => PaginatedMetaDto)
  @ApiProperty({ type: PaginatedMetaDto })
  meta: PaginatedMetaDto;

  @Expose()
  @Type(() => PaginatedLinksDto)
  @ApiProperty({ type: PaginatedLinksDto })
  links: PaginatedLinksDto;
}
