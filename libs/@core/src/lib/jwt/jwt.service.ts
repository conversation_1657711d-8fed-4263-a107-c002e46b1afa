import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';

type RequestWithJwt = Request & { accessTokenJWT?: string; accessToken?: string };

@Injectable({ scope: Scope.REQUEST })
export class JwtService {
  constructor(@Inject(REQUEST) private readonly request: RequestWithJwt) {}

  getJwt(): string | undefined {
    return this.request.accessTokenJWT || this.request.accessToken;
  }
}
