import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { validate as uuidValidate } from 'uuid';
import { META_SKIP_TENANT } from '../common';

/**
 * An authorization guard. Will return a 403 forbidden when it is unable to
 * verify the `x-client` header, or when it is missing.
 */
@Injectable()
export class TenantGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const tenantId = request.headers['x-client'];
    const shouldSkipGuard = this.reflector.getAllAndOverride<boolean>(META_SKIP_TENANT, [
      context.getClass(),
      context.getHandler(),
    ]);

    if (shouldSkipGuard) {
      return true;
    }

    if (!tenantId) {
      throw new ForbiddenException('x-client is required');
    }

    if (!uuidValidate(tenantId)) {
      throw new ForbiddenException('x-client must be a valid UUID');
    }

    request.tenantId = tenantId;
    return true;
  }
}
