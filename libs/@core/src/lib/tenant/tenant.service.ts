import { Inject, Injectable, Scope } from '@nestjs/common';
import { REQUEST } from '@nestjs/core';
import { Request } from 'express';

type RequestWithTenant = Request & { tenantId: string };

@Injectable({ scope: Scope.REQUEST })
export class TenantService {
  constructor(@Inject(REQUEST) private readonly request: RequestWithTenant) {}

  getTenantId(): string | undefined {
    return this.request.tenantId;
  }
}
