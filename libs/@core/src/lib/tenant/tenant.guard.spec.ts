import { ExecutionContext, ForbiddenException } from '@nestjs/common';
import { TenantGuard } from './tenant.guard';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { Reflector } from '@nestjs/core';
import { META_SKIP_TENANT } from '../common';

describe('TenantGuard', () => {
  let guard: TenantGuard;
  let httpContext: jest.Mocked<HttpArgumentsHost>;
  let context: jest.Mocked<ExecutionContext>;
  let reflector: jest.Mocked<Reflector>;

  beforeEach(() => {
    httpContext = {
      getRequest: jest.fn().mockReturnValue({
        headers: {},
      }),
    } as unknown as jest.Mocked<HttpArgumentsHost>;

    context = {
      switchToHttp: jest.fn().mockReturnValue(httpContext),
      getClass: jest.fn(),
      getHandler: jest.fn(),
    } as unknown as jest.Mocked<ExecutionContext>;

    reflector = { getAllAndOverride: jest.fn().mockReturnValue(false) } as unknown as jest.Mocked<Reflector>;

    guard = new TenantGuard(reflector);
  });

  describe('canActivate', () => {
    it('should throw ForbiddenException when x-client header is missing', async () => {
      await expect(guard.canActivate(context)).rejects.toThrow(new ForbiddenException('x-client is required'));
    });

    it('should throw ForbiddenException when x-client is not a valid UUID', async () => {
      httpContext.getRequest.mockReturnValue({
        headers: {
          'x-client': 'not-a-uuid',
        },
      });

      await expect(guard.canActivate(context)).rejects.toThrow(new ForbiddenException('x-client must be a valid UUID'));
    });

    it('should allow request and set tenantId when x-client is a valid UUID', async () => {
      const validUUID = '123e4567-e89b-12d3-a456-************';
      const request = {
        headers: {
          'x-client': validUUID,
        },
      };

      httpContext.getRequest.mockReturnValue(request);

      const result = await guard.canActivate(context);

      expect(result).toBe(true);
      expect(request).toHaveProperty('tenantId', validUUID);
    });

    it('should set tenantId in request object when x-client is valid', async () => {
      const validUUID = '123e4567-e89b-12d3-a456-************';
      const request = {
        headers: {
          'x-client': validUUID,
        },
      };

      httpContext.getRequest.mockReturnValue(request);

      await guard.canActivate(context);

      // Verifica se o tenantId foi definido corretamente no objeto request
      expect(request).toEqual({
        headers: {
          'x-client': validUUID,
        },
        tenantId: validUUID,
      });
    });

    it('should allow a request without the x-client when the skip-tenant decorator is set', async () => {
      httpContext.getRequest.mockReturnValue({ headers: {} });
      reflector.getAllAndOverride.mockReturnValueOnce(true);

      const canActivate = await guard.canActivate(context);

      expect(canActivate).toBeTruthy();
      expect(reflector.getAllAndOverride).toHaveBeenCalledWith(META_SKIP_TENANT, []);
    });
  });
});
