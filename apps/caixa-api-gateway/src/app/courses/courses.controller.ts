import { Body, Controller, Get, Headers, Param, Post, Query } from '@nestjs/common';
import { CoursesListParamsDto } from './dtos/courses-list-params.dto';
import { PageDtoSwaggerResponse } from '@keeps-node-apis/@core';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { CourseEnrollDto } from './dtos/course-enroll.dto';
import { CourseDto } from './dtos/course.dto';
import { CoursesService } from './services/courses.service';

@ApiTags('Courses')
@Controller('courses')
@ApiResponse({ status: 403, description: 'Forbidden.' })
export class CoursesController {
  constructor(private coursesService: CoursesService) {}

  @Get()
  @ApiOperation({ summary: 'List courses' })
  @PageDtoSwaggerResponse(CourseDto, { status: 200, description: 'The paginated list of courses.' })
  getCourses(@Headers('x-workspace-id') workspaceId: string, @Query() params: CoursesListParamsDto) {
    return this.coursesService.getCourses(workspaceId, params);
  }

  @Post(':id/enroll')
  @ApiOperation({ summary: 'Enroll into a course' })
  @ApiResponse({
    description: 'The enrollment operation was successful',
    status: 204,
  })
  @ApiResponse({
    description: 'The user already has an ongoing enrollment.',
    status: 409,
  })
  enroll(
    @Headers('x-workspace-id') workspaceId: string,
    @Param('id') courseId: string,
    @Body() courseEnroll: CourseEnrollDto,
  ) {
    return this.coursesService.enroll(workspaceId, courseId, courseEnroll);
  }
}
