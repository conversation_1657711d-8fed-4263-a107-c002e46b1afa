import { CourseCategory } from '../models/course-category';
import { ApiProperty } from '@nestjs/swagger';

export class CourseDto {
  @ApiProperty({ description: 'The course id', example: 'e5077e79-1ac8-4f84-a54e-c768eaf6a552' })
  id: string;

  @ApiProperty({ description: 'The course name', example: 'My Course' })
  name: string;

  @ApiProperty({ description: 'The course creation date in the yyyy-mm-dd format', example: '2024-12-01' })
  created: string;

  @ApiProperty({ description: 'The course updated date in the yyyy-mm-dd format', example: '2024-12-01' })
  updated: string;

  @ApiProperty({ description: 'Whether the course is active or not', example: true })
  is_active: boolean;

  @ApiProperty({ description: 'The course holder image url' })
  holder_image: string;

  @ApiProperty({ description: 'The course thumbnail image url' })
  thumb_image: string;

  @ApiProperty({ description: 'The course category', example: { name: 'Category name', id: 'Category id' } })
  category: CourseCategory;

  @ApiProperty({ description: 'The description of the course' })
  description: string;

  @ApiProperty({ description: 'The course language' })
  lang: string;

  @ApiProperty({ description: 'The course duration' })
  duration: number;
}
