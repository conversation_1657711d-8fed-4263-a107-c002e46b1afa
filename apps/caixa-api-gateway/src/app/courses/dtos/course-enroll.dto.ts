import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsUUID } from 'class-validator';

export class CourseEnrollDto {
  @ApiProperty({ description: 'The user uuid', example: 'b3a0d9a7-0f85-4b19-b75f-9512be4f9210' })
  @IsUUID()
  @IsNotEmpty()
  user_id: string;

  @ApiProperty({
    description: 'Whether the terms and conditions were accepted by the user.',
    example: true,
  })
  @IsBoolean()
  terms_accept: boolean;
}
