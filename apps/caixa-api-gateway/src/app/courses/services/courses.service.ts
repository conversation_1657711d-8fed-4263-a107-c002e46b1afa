import { ConflictException, Injectable } from '@nestjs/common';
import { SmartzapClient } from '../../core';
import { CoursesListParamsDto } from '../dtos/courses-list-params.dto';
import { SmartZapResponseDto } from '../../common/dtos';
import { CourseDto } from '../dtos/course.dto';
import { HttpClientError, KpCacheService, PageDto } from '@keeps-node-apis/@core';
import { CourseCategory } from '../models/course-category';
import { CourseEnrollDto } from '../dtos/course-enroll.dto';
import { UserAlreadyEnrolledException } from '../../exceptions';

const CACHE_TTL = 18000; // 30 minutes

@Injectable()
export class CoursesService {
  constructor(
    private smartZapClient: SmartzapClient,
    private cache: KpCacheService,
  ) {}

  async getCourses(workspaceId: string, params: CoursesListParamsDto): Promise<PageDto<CourseDto>> {
    const smartZapParams = this.buildSmartZapParams(params);

    const cacheKey = ['caixa-api-gateway-courses', workspaceId, JSON.stringify(smartZapParams)];
    const cachedCourses = await this.cache.get<PageDto<CourseDto>>(cacheKey);

    if (cachedCourses) {
      return cachedCourses;
    }

    const response = await this.smartZapClient.get<SmartZapResponseDto<CourseDto>>(
      '/courses',
      workspaceId,
      smartZapParams,
    );
    const paginatedResponse = this.buildPaginatedResponse(response, params);

    await this.cache.set(cacheKey, paginatedResponse, { ttl: CACHE_TTL });
    return paginatedResponse;
  }

  async enroll(workspaceId: string, courseId: string, courseEnroll: CourseEnrollDto): Promise<void> {
    if (courseEnroll.terms_accept !== true) {
      throw new ConflictException('The terms and conditions should be accepted');
    }
    try {
      await this.smartZapClient.post<{ id: string }>('/enrollments', workspaceId, {
        course_id: courseId,
        user_id: courseEnroll.user_id,
      });
    } catch (e) {
      if (e instanceof HttpClientError && e.status === 409) {
        throw new UserAlreadyEnrolledException(
          'The user already has an ongoing enrollment.',
          workspaceId,
          courseEnroll.user_id,
          courseId,
        );
      }
      throw e;
    }
  }

  private buildSmartZapParams(params: CoursesListParamsDto) {
    const { page, perPage, search } = params;
    return { page, per_page: perPage, name__ilike: search || null, status: 'FINISHED' };
  }

  private buildPaginatedResponse(response: SmartZapResponseDto<CourseDto>, params: CoursesListParamsDto) {
    const courses: CourseDto[] = response.result.map((course) => {
      const { id, name, created, updated, is_active, holder_image, thumb_image, lang, description, duration } = course;
      const category: CourseCategory = { id: course.category.id, name: course.category.name };
      return {
        id,
        name,
        created,
        updated,
        is_active,
        holder_image,
        thumb_image,
        category,
        lang,
        description,
        duration,
      };
    });
    return new PageDto(courses, params, response.count);
  }
}
