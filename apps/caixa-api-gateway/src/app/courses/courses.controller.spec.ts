import { CoursesController } from './courses.controller';
import { CoursesService } from './services/courses.service';
import { CoursesListParamsDto } from './dtos/courses-list-params.dto';
import { Chance } from 'chance';
import { CourseEnrollDto } from './dtos/course-enroll.dto';

describe('CoursesController', () => {
  let controller: CoursesController;
  let coursesService: jest.Mocked<CoursesService>;
  const chance = new Chance();

  beforeEach(async () => {
    coursesService = { getCourses: jest.fn(), enroll: jest.fn() } as unknown as jest.Mocked<CoursesService>;
    controller = new CoursesController(coursesService);
  });

  it('should fetch courses', async () => {
    const workspaceId = chance.guid();
    const params = { search: 'mock_search' } as unknown as CoursesListParamsDto;

    await controller.getCourses(workspaceId, params);

    expect(coursesService.getCourses).toHaveBeenCalledWith(workspaceId, params);
  });

  it('should enroll a user into a course', async () => {
    const workspaceId = chance.guid();
    const userId = chance.guid();
    const courseId = chance.guid();
    const enrollDto = { user_id: userId } as unknown as CourseEnrollDto;

    await controller.enroll(workspaceId, courseId, enrollDto);

    expect(coursesService.enroll).toHaveBeenCalledWith(workspaceId, courseId, enrollDto);
  });
});
