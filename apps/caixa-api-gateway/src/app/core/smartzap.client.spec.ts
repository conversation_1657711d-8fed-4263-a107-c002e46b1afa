import { SmartzapClient } from './smartzap.client';
import { HttpClient } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';
import { Chance } from 'chance';

describe('SmartzapClient', () => {
  let service: SmartzapClient;
  let httpClient: jest.Mocked<HttpClient>;
  let configServiceMock: jest.Mocked<ConfigService>;
  const apiUrl = 'https://caixa-api.com/v1';
  const chance = new Chance();
  const workspaceId = chance.guid();
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'x-client': workspaceId,
  };

  beforeEach(async () => {
    httpClient = {
      get: jest.fn().mockResolvedValue([]),
      put: jest.fn(),
      delete: jest.fn(),
      post: jest.fn(),
    } as unknown as jest.Mocked<HttpClient>;
    configServiceMock = { getOrThrow: jest.fn().mockReturnValue(apiUrl) } as unknown as jest.Mocked<ConfigService>;
    service = new SmartzapClient(httpClient, configServiceMock);
  });

  it('should make post requests', () => {
    const endpoint = '/users';
    const payload = { name: chance.name() };

    service.post(endpoint, workspaceId, payload);

    expect(httpClient.post).toHaveBeenCalledWith(`${apiUrl}${endpoint}`, payload, { headers: defaultHeaders });
  });

  it('should make get requests', () => {
    const endpoint = '/users';
    const params = { name: chance.name() };

    service.get(endpoint, workspaceId, params);

    expect(httpClient.get).toHaveBeenCalledWith(`${apiUrl}${endpoint}`, {
      headers: defaultHeaders,
      params,
    });
  });
});
