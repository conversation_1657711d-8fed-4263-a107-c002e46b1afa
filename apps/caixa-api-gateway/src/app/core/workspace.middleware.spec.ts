import { WorkspaceIdMiddleware } from './workspace-id.middleware';
import { Request } from 'express';
import { ConfigService } from '@nestjs/config';
import { Chance } from 'chance';

const chance = new Chance();
const agenciesWorkspaceId = chance.guid();
const partnersWorkspaceId = chance.guid();
const getOrThrowStub = jest.fn().mockImplementation((value) => {
  return value === 'AGENCIES_WORKSPACE_ID' ? agenciesWorkspaceId : partnersWorkspaceId;
});

describe('WorkspaceMiddleware', () => {
  let middleware: WorkspaceIdMiddleware;
  let configServiceMock: jest.Mocked<ConfigService>;

  beforeEach(() => {
    configServiceMock = { getOrThrow: getOrThrowStub } as unknown as jest.Mocked<ConfigService>;
    middleware = new WorkspaceIdMiddleware(configServiceMock);
  });

  it('should set the correct workspace id when a request includes the x-application header', () => {
    const partnersRequest = { headers: { 'x-application': 'partners' } } as unknown as Request;
    const agenciesRequest = { headers: { 'x-application': 'agencies' } } as unknown as Request;
    const nextFn = jest.fn();

    middleware.use(partnersRequest, null, nextFn);
    middleware.use(agenciesRequest, null, nextFn);

    expect(partnersRequest.headers).toEqual({
      'x-application': 'partners',
      'x-workspace-id': partnersWorkspaceId,
    });
    expect(agenciesRequest.headers).toEqual({
      'x-application': 'agencies',
      'x-workspace-id': agenciesWorkspaceId,
    });
    expect(nextFn).toHaveBeenCalledTimes(2);
  });

  it('should throw when the request does not have a valid x-application header', () => {
    const invalidHeadersReq = { headers: { 'x-application': 'invalid-application-name' } } as unknown as Request;
    const nextFn = jest.fn();

    expect(() => middleware.use(invalidHeadersReq, null, nextFn)).toThrow(
      'Invalid x-application header: invalid-application-name',
    );
    expect(nextFn).not.toHaveBeenCalled();
  });
});
