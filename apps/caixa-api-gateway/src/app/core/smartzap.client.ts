import { Inject, Injectable } from '@nestjs/common';
import { HTTP_CLIENT, HttpClient } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class SmartzapClient {
  private readonly CAIXA_API_URL: string;

  constructor(
    @Inject(HTTP_CLIENT) private http: HttpClient,
    private configService: ConfigService,
  ) {
    this.CAIXA_API_URL = this.configService.getOrThrow('CAIXA_API_URL');
  }

  post<T>(url: string, workspaceId: string, data: any) {
    const headers = this.buildHeaders(workspaceId);
    return this.http.post<T>(`${this.CAIXA_API_URL}${url}`, data, { headers });
  }

  get<T>(url: string, workspaceId: string, params: any) {
    const headers = this.buildHeaders(workspaceId);
    return this.http.get<T>(`${this.CAIXA_API_URL}${url}`, { headers, params });
  }

  private buildHeaders(workspaceId: string) {
    return {
      'Content-Type': 'application/json',
      'x-client': workspaceId,
    };
  }
}
