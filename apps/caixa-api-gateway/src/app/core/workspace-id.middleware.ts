import { BadRequestException, Injectable, NestMiddleware } from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { ConfigService } from '@nestjs/config';

type RequestApplication = 'partners' | 'agencies';

@Injectable()
export class WorkspaceIdMiddleware implements NestMiddleware {
  private readonly AGENCIES_WORKSPACE_ID: string;
  private readonly PARTNERS_WORKSPACE_ID: string;
  private readonly applicationWorkspaceIds: Map<RequestApplication, string>;

  constructor(private configService: ConfigService) {
    this.AGENCIES_WORKSPACE_ID = this.configService.getOrThrow('AGENCIES_WORKSPACE_ID');
    this.PARTNERS_WORKSPACE_ID = this.configService.getOrThrow('PARTNERS_WORKSPACE_ID');
    this.applicationWorkspaceIds = new Map<RequestApplication, string>([
      ['agencies', this.AGENCIES_WORKSPACE_ID],
      ['partners', this.PARTNERS_WORKSPACE_ID],
    ]);
  }

  use(req: Request, _res: Response, next: NextFunction) {
    this.setWorkspaceIdHeader(req);
    next();
  }

  private setWorkspaceIdHeader(req: Request) {
    const application = req.headers['x-application'] as RequestApplication;
    const workspaceId = this.applicationWorkspaceIds.get(application);
    if (!workspaceId) {
      throw new BadRequestException(`Invalid x-application header: ${application}`);
    }
    req.headers['x-workspace-id'] = workspaceId;
  }
}
