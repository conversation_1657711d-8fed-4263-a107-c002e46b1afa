import { Injectable } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { QueryDslQueryContainer } from '@elastic/elasticsearch/lib/api/types';

@Injectable()
export class ElasticSearchClient {
  private readonly caixaApiIndex = 'search-caixa-partners';

  constructor(private readonly es: ElasticsearchService) {}

  search<T>(query: QueryDslQueryContainer, from: number, size: number, sort = ['_score']) {
    const search = { index: this.caixaApiIndex, query, from, size, sort };
    return this.es.search<T>(search);
  }

  async searchOne<T>(query: QueryDslQueryContainer) {
    const search = { index: this.caixaApiIndex, query, size: 1 };
    const response = await this.es.search<T>(search);
    const hits = response.hits.hits;
    if (!hits.length) {
      return null;
    }
    return hits.at(0)._source;
  }
}
