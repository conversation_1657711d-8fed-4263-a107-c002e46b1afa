import { ElasticSearchClient } from './elastic-search.client';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { Chance } from 'chance';
import { QueryDslQueryContainer, SearchResponse } from '@elastic/elasticsearch/lib/api/types';

describe('ElasticSearchClient', () => {
  let service: ElasticSearchClient;
  let elasticServiceMock: jest.Mocked<ElasticsearchService>;
  const chance = new Chance();
  const index = 'search-caixa-partners';

  beforeEach(async () => {
    elasticServiceMock = {
      bulk: jest.fn(),
      search: jest.fn(),
    } as unknown as jest.Mocked<ElasticsearchService>;
    service = new ElasticSearchClient(elasticServiceMock);
  });

  it('should make searches in the index', () => {
    const query: QueryDslQueryContainer = { bool: { must: { term: { name: 'mock_name' } } } };

    service.search(query, 10, 100);

    expect(elasticServiceMock.search).toHaveBeenCalledWith({ index, query, from: 10, size: 100, sort: ['_score'] });
  });

  it('should search for one registry in the index', async () => {
    const query: QueryDslQueryContainer = { bool: { must: { term: { name: 'mock_name' } } } };
    const mockResult = { name: chance.name() };

    const mockResponse = { hits: { hits: [{ _source: mockResult }] } } as unknown as SearchResponse;
    elasticServiceMock.search.mockResolvedValueOnce(mockResponse);

    const result = await service.searchOne(query);

    expect(elasticServiceMock.search).toHaveBeenCalledWith({ index, query, size: 1 });
    expect(result).toBe(mockResult);
  });
});
