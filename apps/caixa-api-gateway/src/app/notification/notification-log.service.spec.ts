import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { NotificationService } from '@keeps-node-apis/@core';
import { HTTP_CLIENT } from '@keeps-node-apis/@core';
import { NotificationLogService } from './notification-log.service';

describe('NotificationLogService', () => {
  let service: NotificationLogService;
  let mockNotificationService: jest.Mocked<NotificationService>;
  let mockConfigService: jest.Mocked<ConfigService>;
  let mockHttpClient: any;

  beforeEach(async () => {
    mockNotificationService = {
      notifyViaEmail: jest.fn(),
    } as any;

    mockConfigService = {
      getOrThrow: jest.fn().mockReturnValue('<EMAIL>'),
    } as any;

    mockHttpClient = {};

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationLogService,
        { provide: NotificationService, useValue: mockNotificationService },
        { provide: ConfigService, useValue: mockConfigService },
        { provide: HTTP_CLIENT, useValue: mockHttpClient },
      ],
    }).compile();

    service = module.get<NotificationLogService>(NotificationLogService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('notifyViaEmail', () => {
    it('should call notificationService.notifyViaEmail with correct parameters', async () => {
      const mockNotification = {
        subject: 'Test Subject',
        language: 'pt-BR',
        body: 'Test Body',
      } as any;

      await service.notifyViaEmail(mockNotification);

      expect(mockNotificationService.notifyViaEmail).toHaveBeenCalledWith(mockNotification, '<EMAIL>');
    });

    it('should use ERROR_NOTIFICATION_RECEIVER_EMAIL from config', () => {
      expect(mockConfigService.getOrThrow).toHaveBeenCalledWith('ERROR_NOTIFICATION_RECEIVER_EMAIL');
    });
  });
});
