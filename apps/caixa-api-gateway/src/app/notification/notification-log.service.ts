import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HTTP_CLIENT, HttpClient, NotificationService } from '@keeps-node-apis/@core';
import { NotificationDto } from './notification.dto';

@Injectable()
export class NotificationLogService {
  private readonly NOTIFICATION_URL: string;
  private readonly ERROR_NOTIFICATION_RECEIVER_EMAIL: string;

  constructor(
    private notificationService: NotificationService,
    private configService: ConfigService,
    @Inject(HTTP_CLIENT) private http: HttpClient,
  ) {
    this.ERROR_NOTIFICATION_RECEIVER_EMAIL = this.configService.getOrThrow('ERROR_NOTIFICATION_RECEIVER_EMAIL');
  }

  async notifyViaEmail(notification: NotificationDto) {
    this.notificationService.notifyViaEmail(
      { language: 'pt-BR', ...notification },
      this.ERROR_NOTIFICATION_RECEIVER_EMAIL,
    );
  }
}
