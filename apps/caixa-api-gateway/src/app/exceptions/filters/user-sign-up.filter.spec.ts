import { UserSignUpFilter } from './user-sign-up.filter';
import { ArgumentsHost, HttpException, HttpStatus } from '@nestjs/common';
import { Chance } from 'chance';
import { UserSignUpException } from '../exceptions';
import { UserSignupDto } from '../../users/dtos/user-signup.dto';
import { PARTNER_TYPE } from '../../partners/models/partner-type.enum';
import { NotificationLogService, NotificationTemplate } from '../../notification';
import { NotificationDto } from '../../notification/notification.dto';

describe('UserSignUpFilter', () => {
  let filter: UserSignUpFilter;
  let notificationService: jest.Mocked<NotificationLogService>;
  let mockResponse: any;
  let mockArgumentsHost: ArgumentsHost;
  const chance = new Chance();
  const workspaceId = chance.guid();

  beforeEach(() => {
    notificationService = { notifyViaEmail: jest.fn() } as unknown as jest.Mocked<NotificationLogService>;
    filter = new UserSignUpFilter(notificationService);

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnThis(),
      getResponse: jest.fn().mockReturnValue(mockResponse),
    } as unknown as ArgumentsHost;
  });

  it('should throw the conflict exception and notify via email', () => {
    const userSignupDto: UserSignupDto = {
      name: chance.name(),
      cpf: chance.cpf(),
      email: chance.email(),
      partner_type: PARTNER_TYPE.CORRESPONDENTE,
      partner_convention_number: chance.string(),
      phone: chance.phone(),
      terms_accept: true,
    };

    const exception = new UserSignUpException(
      'Error while registering user',
      HttpStatus.BAD_REQUEST,
      workspaceId,
      userSignupDto,
    );

    const expectedPayload: NotificationDto = {
      subject: 'SMARTZAP CAIXA - Erro ao registrar usuário',
      template: NotificationTemplate.USER_SIGN_UP,
      workspaceId: exception.workspaceId,
      templateData: {
        workspace_id: exception.workspaceId,
        ...userSignupDto,
      },
    };

    try {
      filter.catch(exception, mockArgumentsHost);
    } catch (error) {
      expect(error).toBeInstanceOf(HttpException);
      expect(error.message).toBe('Error while registering user');
      expect(error.status).toBe(HttpStatus.BAD_REQUEST);
      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(expectedPayload);
    }
  });
});
