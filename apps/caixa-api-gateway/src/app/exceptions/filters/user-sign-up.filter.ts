import { ArgumentsHost, Catch, ExceptionFilter, HttpExceptionBody } from '@nestjs/common';
import { UserSignUpException } from '../exceptions';
import { Response } from 'express';
import { NotificationLogService, NotificationTemplate } from '../../notification';
import { NotificationDto } from '../../notification/notification.dto';

@Catch(UserSignUpException)
export class UserSignUpFilter implements ExceptionFilter {
  constructor(private notificationService: NotificationLogService) {}

  catch(exception: UserSignUpException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const httpStatus = exception.status;

    const responseBody: HttpExceptionBody = {
      statusCode: httpStatus,
      message: exception.message,
    };

    this.sendNotification(exception);
    response.status(httpStatus).json(responseBody);
  }

  private async sendNotification(exception: UserSignUpException) {
    const userSignUpDto = exception.userSignupDto;
    const notification: NotificationDto = {
      subject: 'SMARTZAP CAIXA - Erro ao registrar usuário',
      template: NotificationTemplate.USER_SIGN_UP,
      workspaceId: exception.workspaceId,
      templateData: {
        workspace_id: exception.workspaceId,
        name: userSignUpDto.name,
        cpf: userSignUpDto.cpf,
        email: userSignUpDto.email,
        partner_type: userSignUpDto.partner_type,
        partner_convention_number: userSignUpDto.partner_convention_number,
        phone: userSignUpDto.phone,
      },
    };
    await this.notificationService.notifyViaEmail(notification);
  }
}
