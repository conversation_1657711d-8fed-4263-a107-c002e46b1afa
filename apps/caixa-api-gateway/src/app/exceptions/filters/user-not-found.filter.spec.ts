import { UserNotFoundFilter } from './user-not-found.filter';
import { ArgumentsHost, ConflictException, HttpStatus } from '@nestjs/common';
import { Chance } from 'chance';
import { UserNotFoundException } from '../exceptions';
import { NotificationLogService, NotificationTemplate } from '../../notification';
import { NotificationDto } from '../../notification/notification.dto';

describe('UserNotFoundFilter', () => {
  let filter: UserNotFoundFilter;
  let notificationService: jest.Mocked<NotificationLogService>;
  let mockResponse: any;
  let mockArgumentsHost: ArgumentsHost;
  const chance = new Chance();
  const workspaceId = chance.guid();
  const cpf = chance.cpf();

  beforeEach(() => {
    notificationService = { notifyViaEmail: jest.fn() } as unknown as jest.Mocked<NotificationLogService>;
    filter = new UserNotFoundFilter(notificationService);

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnThis(),
      getResponse: jest.fn().mockReturnValue(mockResponse),
    } as unknown as ArgumentsHost;
  });

  it('should throw the conflict exception and notify via email', () => {
    const exception = new UserNotFoundException('User not found', workspaceId, cpf, 'cpf');
    const expectedPayload: NotificationDto = {
      subject: 'SMARTZAP CAIXA - Erro ao pesquisar usuário',
      template: NotificationTemplate.USER_NOT_FOUND,
      workspaceId: exception.workspaceId,
      templateData: {
        search_key: exception.searchKey,
        workspace_id: exception.workspaceId,
        search_value: exception.searchValue,
      },
    };

    try {
      filter.catch(exception, mockArgumentsHost);
    } catch (error) {
      expect(error).toBeInstanceOf(ConflictException);
      expect(error.message).toBe('User not found');
      expect(error.status).toBe(HttpStatus.NOT_FOUND);
      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(expectedPayload);
    }
  });
});
