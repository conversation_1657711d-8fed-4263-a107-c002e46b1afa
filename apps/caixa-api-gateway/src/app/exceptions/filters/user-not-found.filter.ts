import { ArgumentsHost, Catch, ExceptionFilter, HttpExceptionBody, HttpStatus } from '@nestjs/common';
import { UserNotFoundException } from '../exceptions';
import { Response } from 'express';
import { NotificationLogService, NotificationTemplate } from '../../notification';
import { NotificationDto } from '../../notification/notification.dto';

@Catch(UserNotFoundException)
export class UserNotFoundFilter implements ExceptionFilter {
  constructor(private notificationService: NotificationLogService) {}

  catch(exception: UserNotFoundException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const httpStatus = HttpStatus.NOT_FOUND;

    const responseBody: HttpExceptionBody = {
      statusCode: httpStatus,
      error: 'Not Found',
      message: exception.message,
    };

    this.sendNotification(exception);
    response.status(httpStatus).json(responseBody);
  }

  private async sendNotification(exception: UserNotFoundException) {
    const notification: NotificationDto = {
      subject: 'SMARTZAP CAIXA - Erro ao pesquisar usuário',
      template: NotificationTemplate.USER_NOT_FOUND,
      workspaceId: exception.workspaceId,
      templateData: {
        search_key: exception.searchKey,
        workspace_id: exception.workspaceId,
        search_value: exception.searchValue,
      },
    };
    await this.notificationService.notifyViaEmail(notification);
  }
}
