import { UserAlreadyEnrolledFilter } from './user-already-enrolled.filter';
import { ArgumentsHost, ConflictException, HttpStatus } from '@nestjs/common';
import { Chance } from 'chance';
import { UserAlreadyEnrolledException } from '../exceptions';
import { NotificationDto } from '@keeps-node-apis/@core';
import { NotificationLogService } from '../../notification/notification-log.service';
import { NotificationTemplate } from '../../notification';

describe('UserAlreadyEnrolledFilter', () => {
  let filter: UserAlreadyEnrolledFilter;
  let notificationService: jest.Mocked<NotificationLogService>;
  let mockResponse: any;
  let mockArgumentsHost: ArgumentsHost;
  const chance = new Chance();
  const workspaceId = chance.guid();
  const userId = chance.guid();
  const courseId = chance.guid();

  beforeEach(() => {
    notificationService = { notifyViaEmail: jest.fn() } as unknown as jest.Mocked<NotificationLogService>;
    filter = new UserAlreadyEnrolledFilter(notificationService);

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnThis(),
      getResponse: jest.fn().mockReturnValue(mockResponse),
    } as unknown as ArgumentsHost;
  });

  it('should throw the conflict exception and notify via email', () => {
    const exception = new UserAlreadyEnrolledException(
      'The user already has an ongoing enrollment.',
      workspaceId,
      userId,
      courseId,
    );
    const expectedPayload: NotificationDto = {
      subject: 'SMARTZAP CAIXA - Erro ao realizar matrícula',
      template: NotificationTemplate.USER_ALREADY_ENROLLED,
      workspaceId: exception.workspaceId,
      templateData: {
        course_id: exception.courseId,
        workspace_id: exception.workspaceId,
        user_id: exception.userId,
      },
      language: 'pt-BR',
    };

    try {
      filter.catch(exception, mockArgumentsHost);
    } catch (error) {
      expect(error).toBeInstanceOf(ConflictException);
      expect(error.message).toBe('The user already has an ongoing enrollment.');
      expect(error.status).toBe(HttpStatus.CONFLICT);
      expect(notificationService.notifyViaEmail).toHaveBeenCalledWith(expectedPayload);
    }
  });
});
