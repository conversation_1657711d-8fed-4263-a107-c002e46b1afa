import { ArgumentsHost, Catch, ExceptionFilter, HttpExceptionBody, HttpStatus } from '@nestjs/common';
import { UserAlreadyEnrolledException } from '../exceptions';
import { Response } from 'express';
import { NotificationLogService, NotificationTemplate } from '../../notification';
import { NotificationDto } from '../../notification/notification.dto';

@Catch(UserAlreadyEnrolledException)
export class UserAlreadyEnrolledFilter implements ExceptionFilter {
  constructor(private notificationService: NotificationLogService) {}

  catch(exception: UserAlreadyEnrolledException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const httpStatus = HttpStatus.CONFLICT;

    const responseBody: HttpExceptionBody = {
      statusCode: httpStatus,
      error: 'Conflict',
      message: exception.message,
    };

    this.sendNotification(exception);
    response.status(httpStatus).json(responseBody);
  }

  private async sendNotification(exception: UserAlreadyEnrolledException) {
    const notification: NotificationDto = {
      subject: 'SMARTZAP CAIXA - Erro ao realizar matrícula',
      template: NotificationTemplate.USER_ALREADY_ENROLLED,
      workspaceId: exception.workspaceId,
      templateData: {
        course_id: exception.courseId,
        workspace_id: exception.workspaceId,
        user_id: exception.userId,
      },
    };
    await this.notificationService.notifyViaEmail(notification);
  }
}
