import { Module } from '@nestjs/common';
import { APP_FILTER } from '@nestjs/core';
import { UserAlreadyEnrolledFilter, UserNotFoundFilter } from './filters';
import { ConfigModule } from '@nestjs/config';
import { NotificationLogModule } from '../notification';

@Module({
  providers: [
    {
      provide: APP_FILTER,
      useClass: UserNotFoundFilter,
    },
    {
      provide: APP_FILTER,
      useClass: UserAlreadyEnrolledFilter,
    },
  ],
  imports: [NotificationLogModule, ConfigModule],
})
export class ExceptionsModule {}
