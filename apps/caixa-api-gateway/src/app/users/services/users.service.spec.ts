import { UsersService } from './users.service';
import { SmartzapClient } from '../../core';
import { Chance } from 'chance';
import { UserSignupDto } from '../dtos/user-signup.dto';
import { PartnersSearchService } from '../../partners';
import { PartnerElasticDto } from '../../common/dtos';
import { SmartzapUserDto } from '../dtos/smartzap-user.dto';
import { PARTNER_TYPE } from '../../partners/models/partner-type.enum';
import { cpf } from 'cpf-cnpj-validator';
import { CoursesService } from '../../courses/services/courses.service';
import { UserUpdateDto } from '../dtos/user-update.dto';

describe('UsersService', () => {
  let service: UsersService;
  let smartZapClientMock: jest.Mocked<SmartzapClient>;
  let partnerSearchService: jest.Mocked<PartnersSearchService>;
  let coursesService: jest.Mocked<CoursesService>;
  const chance = new Chance();
  const workspaceId = chance.guid();

  beforeEach(async () => {
    smartZapClientMock = { post: jest.fn().mockResolvedValue({}) } as unknown as jest.Mocked<SmartzapClient>;
    partnerSearchService = { getByConventionNumber: jest.fn() } as unknown as jest.Mocked<PartnersSearchService>;
    coursesService = { enroll: jest.fn() } as unknown as jest.Mocked<CoursesService>;
    service = new UsersService(smartZapClientMock, partnerSearchService, coursesService);
  });

  describe('create user', () => {
    it('should create a new user', async () => {
      const mockPartner = {
        name: chance.name(),
        sr_name: chance.name(),
        sev_name: chance.name(),
      } as unknown as PartnerElasticDto;
      partnerSearchService.getByConventionNumber.mockResolvedValueOnce(mockPartner);
      const user = {
        name: chance.name(),
        cpf: chance.cpf(),
        partner_convention_number: chance.string(),
        email: chance.email(),
        phone: chance.phone(),
        terms_accept: true,
      } as UserSignupDto;
      const expectedUser: SmartzapUserDto = {
        name: user.name,
        cpf: cpf.strip(user.cpf),
        email: user.email,
        phone: user.phone,
        area_of_activity: mockPartner.name,
        director: mockPartner.sr_name,
        manager: mockPartner.sev_name,
      };

      await service.createUser(workspaceId, user);

      expect(smartZapClientMock.post).toHaveBeenCalledWith('/users', workspaceId, expectedUser);
    });

    it('should create a new user with default values when a convention number is not provided', async () => {
      const user = {
        name: chance.name(),
        cpf: chance.cpf(),
        email: chance.email(),
        phone: chance.phone(),
        terms_accept: true,
      } as UserSignupDto;
      const defaultValue = 'REDE NÃO IDENTIFICADA';
      const expectedUser: SmartzapUserDto = {
        name: user.name,
        cpf: cpf.strip(user.cpf),
        email: user.email,
        phone: user.phone,
        area_of_activity: defaultValue,
        director: defaultValue,
        manager: defaultValue,
      };

      await service.createUser(workspaceId, user);

      expect(smartZapClientMock.post).toHaveBeenCalledWith('/users', workspaceId, expectedUser);
    });

    it('should create a user with the default user email when the user does not provide one', async () => {
      const defaultValue = 'REDE NÃO IDENTIFICADA';
      const userCPF = chance.cpf();
      const strippedCPF = cpf.strip(userCPF);
      const lotteryUser = {
        name: chance.name(),
        cpf: userCPF,
        phone: chance.phone(),
        partner_type: PARTNER_TYPE.LOTERICO,
        terms_accept: true,
      } as UserSignupDto;
      const correspondentUser = {
        name: chance.name(),
        cpf: userCPF,
        phone: chance.phone(),
        partner_type: PARTNER_TYPE.CORRESPONDENTE,
        terms_accept: true,
      } as UserSignupDto;
      const expectedLotteryUser: SmartzapUserDto = {
        name: lotteryUser.name,
        cpf: strippedCPF,
        email: `${strippedCPF}@cvp-loterica.com`,
        phone: lotteryUser.phone,
        area_of_activity: defaultValue,
        director: defaultValue,
        manager: defaultValue,
      };
      const expectedCorrespondentUser: SmartzapUserDto = {
        name: correspondentUser.name,
        cpf: strippedCPF,
        email: `${strippedCPF}@cvp-correspondente.com`,
        phone: correspondentUser.phone,
        area_of_activity: defaultValue,
        director: defaultValue,
        manager: defaultValue,
      };

      await service.createUser(workspaceId, lotteryUser);
      await service.createUser(workspaceId, correspondentUser);

      expect(smartZapClientMock.post).toHaveBeenCalledWith('/users', workspaceId, expectedLotteryUser);
      expect(smartZapClientMock.post).toHaveBeenCalledWith('/users', workspaceId, expectedCorrespondentUser);
    });

    it('should a bad request exception if terms_accept is false', () => {
      const user = {
        name: chance.name(),
        cpf: chance.cpf(),
        partner_convention_number: chance.string(),
        email: chance.email(),
        phone: chance.phone(),
        terms_accept: false,
      } as UserSignupDto;

      expect(async () => {
        await service.createUser(workspaceId, user);
      }).rejects.toThrow('The terms and conditions should be accepted');
    });

    it('should enroll the user if a course_id is provided in the request DTO', async () => {
      const mockPartner = {
        name: chance.name(),
        sr_name: chance.name(),
        sev_name: chance.name(),
      } as unknown as PartnerElasticDto;
      partnerSearchService.getByConventionNumber.mockResolvedValueOnce(mockPartner);
      const user = {
        name: chance.name(),
        cpf: chance.cpf(),
        partner_convention_number: chance.string(),
        email: chance.email(),
        phone: chance.phone(),
        terms_accept: true,
        course_id: chance.guid(),
      } as UserSignupDto;
      const userId = chance.guid();
      smartZapClientMock.post.mockResolvedValueOnce({ id: userId });

      await service.createUser(workspaceId, user);

      expect(coursesService.enroll).toHaveBeenCalledWith(workspaceId, user.course_id, {
        user_id: userId,
        terms_accept: true,
      });
    });
  });

  describe('updateUser', () => {
    it('should update an user', async () => {
      const user = {
        name: chance.name(),
        email: chance.email(),
        phone: chance.phone(),
      } as UserUpdateDto;

      await service.updateUser(workspaceId, user);

      expect(smartZapClientMock.post).toHaveBeenCalledWith('/users', workspaceId, user);
    });

    it('should throw an internal server error exception if there is a problem updating the user', () => {
      const user = {
        name: chance.name(),
        email: chance.email(),
        phone: chance.phone(),
      } as UserUpdateDto;

      const error = new Error();
      smartZapClientMock.post.mockRejectedValueOnce(error);

      expect(async () => {
        await service.updateUser(workspaceId, user);
      }).rejects.toThrow('There was an error while updating the user');
    });
  });

  describe('giveUpCurrentEnrollment', () => {
    it('should cancel an user current enrollment', async () => {
      const userId = chance.guid();

      await service.giveUpCurrentEnrollment(workspaceId, userId);

      expect(smartZapClientMock.post).toHaveBeenCalledWith('/enrollments/give-up', workspaceId, { user_id: userId });
    });

    it('should throw an internal server error exception if there is a problem canceling the user enrollment', () => {
      const userId = chance.guid();

      const error = new Error();
      smartZapClientMock.post.mockRejectedValueOnce(error);

      expect(async () => {
        await service.giveUpCurrentEnrollment(workspaceId, userId);
      }).rejects.toThrow('There was an error while cancelling the user enrollment');
    });
  });
});
