import { Module } from '@nestjs/common';
import { UsersController } from './controllers/users.controller';
import { UsersService } from './services/users.service';
import { PartnersModule } from '../partners';
import { CoursesModule } from '../courses/courses.module';

@Module({
  imports: [PartnersModule, CoursesModule],
  controllers: [UsersController],
  providers: [UsersService],
})
export class UsersModule {}
