import { UserDto } from './user.dto';

export class SmartzapUserDto {
  id?: string;
  name: string;
  email: string;
  director: string;
  manager: string;
  area_of_activity: string;
  cpf: string;
  phone: string;
  job?: string;
  ein?: string;

  static toUserDto(user: SmartzapUserDto): UserDto {
    return {
      cpf: user.cpf,
      email: user.email,
      ein: user.ein,
      id: user.id,
      job: user.job,
      name: user.name,
      office: user.area_of_activity,
      regional_superintendence: user.director,
      retail_superintendence: user.manager,
    };
  }
}
