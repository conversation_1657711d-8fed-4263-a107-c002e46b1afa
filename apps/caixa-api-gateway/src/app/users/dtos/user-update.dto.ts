import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsPhoneNumber, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class UserUpdateDto {
  @ApiProperty({ description: 'The user name', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'The user email, must be an valid address', example: '<EMAIL>' })
  @IsEmail()
  email: string;

  @ApiProperty({
    description:
      'A valid phone number in the brazilian format, without the international prefix, the use of parentheses or dashes is optional',
    example: '(00) 00000-0000',
  })
  @IsPhoneNumber('BR')
  @IsNotEmpty()
  @Transform(({ value }) => {
    if (!value.startsWith('55')) {
      const cleanedValue = value.replace(/\D/g, '');
      return `55${cleanedValue}`;
    }
    return value;
  })
  phone: string;
}
