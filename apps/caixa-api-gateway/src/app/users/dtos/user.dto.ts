import { ApiProperty } from '@nestjs/swagger';

export class UserDto {
  @ApiProperty({ description: 'The user id', example: 'b3a0d9a7-0f85-4b19-b75f-9512be4f9210' })
  id: string;

  @ApiProperty({ description: 'The user name', example: '<PERSON>' })
  name: string;

  @ApiProperty({ description: 'The user email', example: '<EMAIL>' })
  email: string;

  @ApiProperty({ description: 'The user CPF', example: '418.313.600-80' })
  cpf: string;

  @ApiProperty({ description: 'The user employer identification number', example: '123456' })
  ein: string;

  @ApiProperty({ description: 'The user job inside the organization', example: 'Manager' })
  job: string;

  @ApiProperty({ description: 'The user agency, correspondent or lottery name', example: 'Central Bank' })
  office: string;

  @ApiProperty({ description: 'The user agency regional superintendence', example: 'SR 0000 VALE DO PARAIBA, SP' })
  regional_superintendence: string;

  @ApiProperty({ description: 'The user agency retail superintendence', example: 'SEV 0000 SAO JOSE DOS CAMPOS, SP' })
  retail_superintendence: string;
}
