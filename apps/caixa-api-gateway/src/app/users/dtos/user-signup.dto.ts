import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEmail, IsEnum, IsNotEmpty, Is<PERSON><PERSON>al, IsString, ValidateIf } from 'class-validator';
import { PARTNER_TYPE } from '../../partners/models/partner-type.enum';
import { IsCPF } from 'class-validator-cpf';
import { Transform } from 'class-transformer';
import { parsePhoneNumber } from 'libphonenumber-js/max';
import { BadRequestException } from '@nestjs/common';

export class UserSignupDto {
  @ApiProperty({ description: 'The user name', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'The user CPF', example: '000.000.000-00' })
  @IsString()
  @IsNotEmpty()
  @IsCPF()
  cpf: string;

  @ApiProperty({
    description: 'The partner type where the user works',
    enum: PARTNER_TYPE,
    example: PARTNER_TYPE.CORRESPONDENTE,
  })
  @IsEnum(PARTNER_TYPE)
  partner_type: PARTNER_TYPE;

  @ApiProperty({ description: 'The user email, if provided must be an valid address', example: '<EMAIL>' })
  @ValidateIf((dto: UserSignupDto) => !!dto?.email)
  @IsEmail()
  email: string;

  @ApiProperty({
    description:
      'A valid phone number in the brazilian format, without the international prefix, the use of parentheses or dashes is optional',
    example: '(00) 00000-0000',
  })
  @IsNotEmpty()
  @Transform(({ value }) => {
    const phoneNumber = parsePhoneNumber(value, 'BR');
    if (!phoneNumber?.isValid()) {
      throw new BadRequestException(`Invalid phone number format: ${value}`);
    }
    return phoneNumber.format('E.164');
  })
  phone: string;

  @ApiProperty({
    description: 'The convention number of the partner correspondent or lottery where the user works',
    example: '110216490',
  })
  @IsOptional()
  @IsString()
  partner_convention_number: string;

  @ApiProperty({
    description: 'Whether the terms and conditions were accepted by the user.',
    example: true,
  })
  @IsBoolean()
  terms_accept: boolean;

  @ApiProperty({
    description: 'An optional course ID, if provided, the user will be enrolled on it after being registered',
    example: 'b3a0d9a7-0f85-4b19-b75f-9512be4f9210',
    required: false,
  })
  @IsString()
  @IsOptional()
  course_id?: string;
}
