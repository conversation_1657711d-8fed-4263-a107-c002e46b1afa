import { Body, Controller, Get, Headers, HttpCode, HttpStatus, Param, Patch, Post, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserDto } from '../dtos/user.dto';
import { UserSignupDto } from '../dtos/user-signup.dto';
import { UsersService } from '../services/users.service';
import { UserUpdateDto } from '../dtos/user-update.dto';

@ApiTags('Users')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@Controller('users')
export class UsersController {
  constructor(private usersService: UsersService) {}

  @Get('/find')
  @ApiOperation({ summary: 'Filter a single user by CPF or EIN' })
  @ApiResponse({
    type: UserDto,
    status: 200,
  })
  getUser(@Headers('x-workspace-id') workspaceId: string, @Query('search') search: string) {
    return this.usersService.findUser(workspaceId, search);
  }

  @Post()
  @ApiOperation({ summary: 'Create a new user' })
  @ApiResponse({
    type: UserDto,
    status: 201,
  })
  signUp(@Headers('x-workspace-id') workspaceId: string, @Body() userSignupDto: UserSignupDto) {
    return this.usersService.createUser(workspaceId, userSignupDto);
  }

  @Patch()
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Update an existing user' })
  @ApiResponse({ status: 202 })
  update(@Headers('x-workspace-id') workspaceId: string, @Body() userUpdateDto: UserUpdateDto) {
    return this.usersService.updateUser(workspaceId, userUpdateDto);
  }

  @Post(':id/give-up')
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({ summary: 'Cancel the user current enrollment' })
  @ApiResponse({
    description: 'The enrollment operation was successful',
    status: 202,
  })
  giveUp(@Headers('x-workspace-id') workspaceId: string, @Param('id') userId: string) {
    return this.usersService.giveUpCurrentEnrollment(workspaceId, userId);
  }
}
