import { UsersController } from './users.controller';
import { UsersService } from '../services/users.service';
import { UserSignupDto } from '../dtos/user-signup.dto';
import { Chance } from 'chance';
import { UserUpdateDto } from '../dtos/user-update.dto';

describe('UsersController', () => {
  let controller: UsersController;
  let usersServiceMock: jest.Mocked<UsersService>;
  const chance = new Chance();
  const workspaceId = chance.guid();

  beforeEach(async () => {
    usersServiceMock = {
      createUser: jest.fn().mockResolvedValue({}),
      findUser: jest.fn().mockResolvedValue({}),
      updateUser: jest.fn().mockResolvedValue({}),
      giveUpCurrentEnrollment: jest.fn().mockResolvedValue({}),
    } as unknown as jest.Mocked<UsersService>;
    controller = new UsersController(usersServiceMock);
  });

  it('should create a new user', async () => {
    const user = {
      name: chance.name(),
      cpf: chance.cpf(),
    } as UserSignupDto;

    await controller.signUp(workspaceId, user);

    expect(usersServiceMock.createUser).toHaveBeenCalledWith(workspaceId, user);
  });

  it('should retrieve an user by CPF or EIN', async () => {
    const search = chance.cpf();
    const workspaceId = chance.guid();

    await controller.getUser(workspaceId, search);

    expect(usersServiceMock.findUser).toHaveBeenCalledWith(workspaceId, search);
  });

  it('should update an user', async () => {
    const user = {
      name: chance.name(),
      email: chance.email(),
      phone: chance.phone(),
    } as UserUpdateDto;

    await controller.update(workspaceId, user);

    expect(usersServiceMock.updateUser).toHaveBeenCalledWith(workspaceId, user);
  });

  it('should cancel an user current enrollment', async () => {
    const userId = chance.guid();

    await controller.giveUp(workspaceId, userId);

    expect(usersServiceMock.giveUpCurrentEnrollment).toHaveBeenCalledWith(workspaceId, userId);
  });
});
