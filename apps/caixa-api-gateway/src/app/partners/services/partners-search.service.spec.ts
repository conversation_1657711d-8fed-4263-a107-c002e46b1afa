import { PartnersSearchService } from './partners-search.service';
import { ElasticSearchClient } from '../../elastic';
import { PartnersDslBuilder } from '../buildes/partners-dsl.builder';
import { PARTNER_TYPE } from '../models/partner-type.enum';
import { PartnerListParamsDto } from '../dtos/partner-list-params.dto';
import { PartnerElasticDto } from '../../common/dtos';
import { Chance } from 'chance';

describe('PartnersSearchService', () => {
  let service: PartnersSearchService;
  let searchClientMock: jest.Mocked<ElasticSearchClient>;
  const chance = new Chance();
  const mockPartnerSync: Partial<PartnerElasticDto> = {
    name: chance.name(),
    convention_number: chance.string(),
    city: chance.city(),
    cnpj: chance.string(),
    uf: chance.state(),
    zip_code: chance.zip(),
  };

  beforeEach(async () => {
    searchClientMock = {
      search: jest.fn().mockResolvedValue({
        hits: {
          hits: [{ _source: mockPartnerSync }],
          total: { value: 100 },
        },
      }),
      searchOne: jest.fn(),
    } as unknown as jest.Mocked<ElasticSearchClient>;
    service = new PartnersSearchService(searchClientMock);
  });

  it('should search in elastic with the correct filter and build the', async () => {
    const query = new PartnersDslBuilder('mock_search', PARTNER_TYPE.LOTERICO, 20)
      .withNameFilter()
      .withFieldQuery('convention_number')
      .withFieldQuery('cnpj')
      .withFieldQuery('address')
      .withFieldQuery('number')
      .withFieldQuery('district')
      .withFieldQuery('city')
      .withFieldQuery('uf')
      .withFieldQuery('region')
      .withFieldQuery('phone')
      .build();

    const params = {
      search: 'mock_search',
      partnerType: PARTNER_TYPE.LOTERICO,
      perPage: 100,
      page: 2,
    } as PartnerListParamsDto;

    await service.getAll(params);

    expect(searchClientMock.search).toHaveBeenCalledWith({ bool: query }, params.skip, params.perPage);
  });

  it('should build paginated response', async () => {
    const params = {
      search: 'mock_search',
      partnerType: PARTNER_TYPE.LOTERICO,
      perPage: 100,
      page: 2,
    } as PartnerListParamsDto;
    const expectedResult = {
      hasNextPage: false,
      hasPreviousPage: true,
      items: [
        {
          city: mockPartnerSync.city,
          cnpj: mockPartnerSync.cnpj,
          convention_number: mockPartnerSync.convention_number,
          name: mockPartnerSync.name,
          state: mockPartnerSync.uf,
          zip_code: mockPartnerSync.zip_code,
        },
      ],
      page: 2,
      total: 100,
    };

    const result = await service.getAll(params);

    expect(result).toEqual(expectedResult);
  });

  it('should filter by convention number', async () => {
    const conventionNumber = '1122334455';
    await service.getByConventionNumber(conventionNumber);

    expect(searchClientMock.searchOne).toHaveBeenCalledWith({ term: { _id: conventionNumber } });
  });
});
