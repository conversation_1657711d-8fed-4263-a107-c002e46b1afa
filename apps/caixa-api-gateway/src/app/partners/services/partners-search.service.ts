import { Injectable } from '@nestjs/common';
import { PartnerListParamsDto } from '../dtos/partner-list-params.dto';
import { PageDto } from '@keeps-node-apis/@core';
import { PartnerDto } from '../dtos/partner.dto';
import { PartnersDslBuilder } from '../buildes/partners-dsl.builder';
import { ElasticSearchClient } from '../../elastic';
import { PartnerElasticDto } from '../../common/dtos';
import { QueryDslQueryContainer, SearchResponse } from '@elastic/elasticsearch/lib/api/types';

@Injectable()
export class PartnersSearchService {
  static extractTotal(response: SearchResponse): number {
    const total = response.hits.total;
    return typeof total == 'number' ? total : total.value;
  }

  constructor(private elasticSearchClient: ElasticSearchClient) {}

  async getAll(params: PartnerListParamsDto): Promise<PageDto<PartnerDto>> {
    const { search, partnerType, perPage, skip } = params;
    const query = new PartnersDslBuilder(search, partnerType, 20)
      .withNameFilter()
      .withFieldQuery('convention_number')
      .withFieldQuery('cnpj')
      .withFieldQuery('address')
      .withFieldQuery('number')
      .withFieldQuery('district')
      .withFieldQuery('city')
      .withFieldQuery('uf')
      .withFieldQuery('region')
      .withFieldQuery('phone')
      .build();

    const boolQuery: QueryDslQueryContainer = { bool: query };
    const searchResult = await this.elasticSearchClient.search<PartnerElasticDto>(boolQuery, skip, perPage);
    return this.buildResult(searchResult, params);
  }

  async getByConventionNumber(conventionNumber: string): Promise<PartnerElasticDto> {
    const query: QueryDslQueryContainer = { term: { _id: conventionNumber } };
    return await this.elasticSearchClient.searchOne<PartnerElasticDto>(query);
  }

  private buildResult(searchResult: SearchResponse<PartnerElasticDto>, params: PartnerListParamsDto) {
    const items = searchResult.hits.hits.map((hit) => PartnerDto.fromPartnerElasticDto(hit._source));
    const total = PartnersSearchService.extractTotal(searchResult);
    return new PageDto<PartnerDto>(items, params, total);
  }
}
