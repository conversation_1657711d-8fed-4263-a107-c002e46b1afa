import { ApiProperty } from '@nestjs/swagger';
import { PartnerElasticDto } from '../../common/dtos';

export class PartnerDto {
  @ApiProperty({ description: 'The partner convention number', example: '110216490' })
  convention_number: string;

  @ApiProperty({ description: 'The name of the partner', example: 'Casa Lotérica Mais Sorte' })
  name: string;

  @ApiProperty({ description: 'The CNPJ of the partner', example: '82932610000198' })
  cnpj: string;

  @ApiProperty({ description: 'The city of the partner', example: 'Florianopolis' })
  city: string;

  @ApiProperty({ description: 'The state of the partner', example: 'Santa Catarina' })
  state: string;

  @ApiProperty({ description: 'The zipcode of the partner', example: '88000000' })
  zip_code: string;

  static fromPartnerElasticDto(partner: PartnerElasticDto): PartnerDto {
    return {
      name: partner.name,
      convention_number: partner.convention_number,
      city: partner.city,
      cnpj: partner.cnpj,
      state: partner.uf,
      zip_code: partner.zip_code,
    };
  }
}
