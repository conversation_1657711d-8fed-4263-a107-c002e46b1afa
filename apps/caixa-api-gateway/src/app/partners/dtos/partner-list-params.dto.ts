import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { PageOptionsDto } from '@keeps-node-apis/@core';
import { PARTNER_TYPE } from '../models/partner-type.enum';

export class PartnerListParamsDto extends PageOptionsDto {
  @ApiProperty({
    required: false,
    description:
      'The partner name, convention number, cnpj, address, number, district, zip code, state, region or phone',
    example: 'Lotérica Mais Sorte',
  })
  @IsOptional()
  search?: string;

  @ApiProperty({ description: 'The partner type', enum: PARTNER_TYPE, example: PARTNER_TYPE.CORRESPONDENTE })
  @IsEnum(PARTNER_TYPE)
  partnerType: PARTNER_TYPE;
}
