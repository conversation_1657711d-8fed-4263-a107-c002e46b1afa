import { Controller, Get, Query } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PageDtoSwaggerResponse } from '@keeps-node-apis/@core';
import { PartnerListParamsDto } from '../dtos/partner-list-params.dto';
import { PartnerDto } from '../dtos/partner.dto';
import { PartnersSearchService } from '../services';

@ApiTags('Partners')
@ApiResponse({ status: 403, description: 'Forbidden.' })
@Controller('partners')
export class PartnersController {
  constructor(private partnersSearchService: PartnersSearchService) {}

  @Get()
  @ApiOperation({ summary: 'List partners' })
  @PageDtoSwaggerResponse(PartnerDto, { status: 200, description: 'The paginated list of partners' })
  getPartners(@Query() params: PartnerListParamsDto) {
    return this.partnersSearchService.getAll(params);
  }
}
