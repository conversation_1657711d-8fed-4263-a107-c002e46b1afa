import { PartnersController } from './partners.controller';
import { PartnersSearchService } from '../services';
import { PartnerListParamsDto } from '../dtos/partner-list-params.dto';

describe('PartnersController', () => {
  let controller: PartnersController;
  let partnersSearchService: jest.Mocked<PartnersSearchService>;

  beforeEach(async () => {
    partnersSearchService = { getAll: jest.fn() } as unknown as jest.Mocked<PartnersSearchService>;

    controller = new PartnersController(partnersSearchService);
  });

  it('should fetch the partners applying the filters', async () => {
    const filter = { search: 'mock_search' } as PartnerListParamsDto;

    await controller.getPartners(filter);

    expect(partnersSearchService.getAll).toHaveBeenCalledWith(filter);
  });
});
