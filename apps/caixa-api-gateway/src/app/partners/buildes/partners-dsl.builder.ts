import { QueryDslBoolQuery, QueryDslQueryContainer } from '@elastic/elasticsearch/lib/api/types';
import { PartnerElasticDto } from '../../common/dtos';
import { PARTNER_TYPE } from '../models/partner-type.enum';
import { QueryTermBuilder } from '../../elastic/query-term.builder';

export class PartnersDslBuilder {
  private readonly should: QueryDslQueryContainer[] = [];

  constructor(
    private readonly search: string,
    private readonly partnerType: PARTNER_TYPE,
    private readonly maxBoost = 10,
  ) {}

  withNameFilter() {
    if (!this.search) {
      return this;
    }

    const nGramBoost = this.maxBoost / 2;
    const queryStringBoost = this.maxBoost / 2.5;

    this.should.push(
      {
        multi_match: {
          query: this.search,
          type: 'bool_prefix',
          fields: [`name^${this.maxBoost}`, `name._2gram^${nGramBoost}`, 'name._3gram'],
        },
      },
      {
        query_string: QueryTermBuilder.createQueryStringForTerm(
          this.search,
          'name',
          queryStringBoost,
          'AND',
          'folding',
        ),
      },
    );

    return this;
  }

  withFieldQuery(field: keyof PartnerElasticDto, boost = 2) {
    if (!this.search) {
      return this;
    }

    this.should.push({ match_phrase_prefix: { [field]: { query: this.search, boost } } });
    return this;
  }

  build(): QueryDslBoolQuery {
    // We need to lowercase the partner type because the analyzer configured in the index creates
    // tokens for keyword analysis in lowercase
    const filter: QueryDslQueryContainer = { term: { type: this.partnerType.toLowerCase() } };
    const minimum_should_match = this.should.length ? 1 : 0;
    return { minimum_should_match, should: this.should, filter: filter };
  }
}
