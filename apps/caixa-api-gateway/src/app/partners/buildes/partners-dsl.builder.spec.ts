import { PartnersDslBuilder } from './partners-dsl.builder';
import { PARTNER_TYPE } from '../models/partner-type.enum';
import { QueryDslBoolQuery, QueryDslQueryContainer } from '@elastic/elasticsearch/lib/api/types';
import { QueryTermBuilder } from '../../elastic/query-term.builder';
import { PartnerElasticDto } from '../../common/dtos';

describe('PartnersDslBuilder', () => {
  const search = 'mock_search_value';
  const defaultTypeFilter: QueryDslQueryContainer = { term: { type: PARTNER_TYPE.LOTERICO.toLowerCase() } };

  it('should filter with name', () => {
    const expectedQuery: QueryDslBoolQuery = {
      minimum_should_match: 1,
      should: [
        {
          multi_match: {
            query: search,
            type: 'bool_prefix',
            fields: [`name^10`, `name._2gram^5`, 'name._3gram'],
          },
        },
        {
          query_string: QueryTermBuilder.createQueryStringForTerm(search, 'name', 4, 'AND', 'folding'),
        },
      ],
      filter: defaultTypeFilter,
    };

    const query = new PartnersDslBuilder(search, PARTNER_TYPE.LOTERICO).withNameFilter().build();

    expect(query).toEqual(expectedQuery);
  });

  it(`should filter with a field query`, () => {
    const filedName: keyof PartnerElasticDto = 'convention_number';
    const expectedQuery: QueryDslBoolQuery = {
      minimum_should_match: 1,
      should: [{ match_phrase_prefix: { [filedName]: { query: search, boost: 2 } } }],
      filter: defaultTypeFilter,
    };

    const query = new PartnersDslBuilder(search, PARTNER_TYPE.LOTERICO).withFieldQuery('convention_number').build();

    expect(query).toEqual(expectedQuery);
  });

  it('it should filter only by partner type when no search is provided', () => {
    const expectedQuery: QueryDslBoolQuery = {
      minimum_should_match: 0,
      should: [],
      filter: defaultTypeFilter,
    };

    const query = new PartnersDslBuilder('', PARTNER_TYPE.LOTERICO)
      .withNameFilter()
      .withFieldQuery('convention_number')
      .withFieldQuery('cnpj')
      .withFieldQuery('address')
      .build();

    expect(query).toEqual(expectedQuery);
  });

  it('should filter by multiple values at the same time', () => {
    const query = new PartnersDslBuilder(search, PARTNER_TYPE.CORRESPONDENTE)
      .withNameFilter()
      .withFieldQuery('convention_number')
      .withFieldQuery('cnpj')
      .withFieldQuery('address')
      .withFieldQuery('number')
      .withFieldQuery('district')
      .withFieldQuery('city')
      .withFieldQuery('uf')
      .withFieldQuery('region')
      .withFieldQuery('phone')
      .build();

    expect(query.minimum_should_match).toBe(1);
    expect((query.should as QueryDslQueryContainer[]).length).toBe(11);
    expect(query.filter).toEqual({ term: { type: PARTNER_TYPE.CORRESPONDENTE.toLowerCase() } });
  });
});
