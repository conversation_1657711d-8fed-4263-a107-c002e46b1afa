{"name": "caixa-api-gateway", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/caixa-api-gateway/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/caixa-api-gateway", "main": "apps/caixa-api-gateway/src/main.ts", "tsConfig": "apps/caixa-api-gateway/tsconfig.app.json", "assets": ["apps/caixa-api-gateway/src/assets"], "webpackConfig": "apps/caixa-api-gateway/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "caixa-api-gateway:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "caixa-api-gateway:build:development"}, "production": {"buildTarget": "caixa-api-gateway:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/caixa-api-gateway/jest.config.ts"}, "configurations": {"dev": {"ci": true, "codeCoverage": true, "coverageReporters": ["html", "text-summary", "lcov"]}}, "defaultConfiguration": "dev"}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "Caixa API Gateway", "hostUrl": "https://sonar.keepsdev.com", "projectKey": "caixa-api-gateway", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "**/db/**, **/assets/**, libs/**", "extra": {"sonar.coverage.exclusions": "**/db/**, **/assets/**, libs/**,**/*.module.ts,**/index.ts,apps/caixa-api-gateway/src/main.ts", "sonar.testExecutionReportPaths": "coverage/caixa-api-gateway/jest-sonar.xml", "sonar.plugins.downloadOnlyRequired": "true"}}}}}