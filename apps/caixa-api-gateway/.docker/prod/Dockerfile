FROM node:20-alpine AS build

WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install

COPY . .

RUN npm run build --name=caixa-api-gateway


FROM node:20-alpine AS production

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install --omit=dev --ignore-scripts

COPY --from=build /usr/src/app/dist/apps/caixa-api-gateway ./dist

CMD ["node", "dist/main"]

EXPOSE 3000
