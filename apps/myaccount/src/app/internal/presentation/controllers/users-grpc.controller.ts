import { Controller, UseInterceptors } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { GrpcFiltersInterceptor, GrpcWorkspaceId } from '@keeps-node-apis/@core';
import { Paginate, PaginateQuery } from 'nestjs-paginate';
import { toGrpcUserListResponse } from '../../mappers/user-list-response-grpc.mapper';
import { ListAllUsersUseCase } from '../../application/list-all-users.use-case';

@Controller()
@UseInterceptors(GrpcFiltersInterceptor)
export class InternalUsersGrpcController {
  constructor(private readonly listUsersUseCase: ListAllUsersUseCase) {}

  @GrpcMethod('UserService', 'Get')
  async listAllUsers(@Paginate() query: PaginateQuery, @GrpcWorkspaceId() workspaceId: string) {
    const result = await this.listUsersUseCase.execute({
      workspaceId,
      query,
    });
    return toGrpcUserListResponse(result.data, result.meta);
  }
}
