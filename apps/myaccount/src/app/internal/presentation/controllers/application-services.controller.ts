import { Serialize } from '@keeps-node-apis/@core';
import { Controller, Get, Headers, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Resource, Scopes } from 'nest-keycloak-connect';
import { ApplicationServiceDto } from '../../../application-services/presentation/dtos/application-service.dto';
import { ApplicationServicesService } from '../../../application-services/services/application-services.service';

@ApiTags('Application Services')
@Controller('/internal/application-services')
@Resource('myaccount-internal-resources')
export class ApplicationServicesController {
  constructor(private readonly applicationServicesService: ApplicationServicesService) {}

  @Get()
  @Scopes('myaccount-internal')
  @Serialize(ApplicationServiceDto)
  @ApiOperation({ summary: 'Fetch the list of services for all applications related to the workspace' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list with all application services of the workspace',
    type: ApplicationServiceDto,
  })
  getApplicationServicesByWorkspace(@Headers('x-client') xClient: string): Promise<ApplicationServiceDto[]> {
    return this.applicationServicesService.getApplicationServicesByWorkspace(xClient);
  }
}
