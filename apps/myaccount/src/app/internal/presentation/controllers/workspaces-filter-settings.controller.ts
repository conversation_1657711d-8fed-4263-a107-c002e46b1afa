import { <PERSON>, Get, Headers, HttpStatus } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { FindAllWorkspaceFilterSettingsUseCase } from '../../../workspaces-filter-settings/application/use-cases/find-all-workspace-filter-settings.use-case';
import { Resource, Scopes } from 'nest-keycloak-connect';
import { Serialize } from '@keeps-node-apis/@core';
import { WorkspacesFilterSettingsResponseDto } from '../../../workspaces-filter-settings/presentation/dtos/workspaces-filter-settings.response.dto';

@ApiTags('Workspace Filter Settings')
@Controller('/internal/workspaces-filter-settings')
@Resource('myaccount-internal-resources')
export class WorkspacesFilterSettingsController {
  constructor(private readonly findAllWorkspaceFilterSettingsUseCase: FindAllWorkspaceFilterSettingsUseCase) {}

  @Get()
  @Scopes('myaccount-internal')
  @Serialize(WorkspacesFilterSettingsResponseDto)
  @ApiOperation({ summary: 'Fetch the workspace filter settings' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list with the workspace filter settings',
    type: WorkspacesFilterSettingsResponseDto,
  })
  async findAllWorkspaceFilterSettings(@Headers('x-client') xClient: string) {
    return this.findAllWorkspaceFilterSettingsUseCase.execute(xClient);
  }
}
