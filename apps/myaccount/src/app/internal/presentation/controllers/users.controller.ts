import { <PERSON>, Get, Headers } from '@nestjs/common';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { Resource, Scopes } from 'nest-keycloak-connect';
import { Paginate, PaginatedSwaggerDocs, PaginateQuery } from 'nestjs-paginate';
import { UserListResponseDto } from '../../../users/dtos/user-list-reponse.dto';
import { USER_PAGINATION_CONFIG } from '../../../users/users.query-config';
import { plainToInstance } from 'class-transformer';
import { ListUsersUseCase } from '../../application/list-users.use-case';
import { PaginatedResponseDto } from '@keeps-node-apis/@core';

@ApiTags('User Access Check')
@Controller('/internal/users')
@Resource('myaccount-internal-resources')
export class UsersController {
  constructor(private readonly listUsersUseCase: ListUsersUseCase) {}

  @Get()
  @PaginatedSwaggerDocs(UserListResponseDto, USER_PAGINATION_CONFIG)
  @Scopes('myaccount-internal')
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to access this resource' })
  async findAll(@Headers('x-client') workspaceId: string, @Paginate() query: PaginateQuery) {
    const result = await this.listUsersUseCase.execute({ workspaceId, query });
    return plainToInstance(PaginatedResponseDto, {
      ...result,
      data: result.data.map((item) => plainToInstance(UserListResponseDto, item)),
    });
  }
}
