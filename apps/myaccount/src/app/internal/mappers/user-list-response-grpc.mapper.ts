import { User } from '../../entities/user.entity';
import { UserListResponse } from '../proto-interfaces';
export function toGrpcUserListResponse(dtoUsers: User[], meta: any): UserListResponse {
  return {
    items: dtoUsers.map((dto) => ({
      id: dto.id,
      email: dto.email,
      name: dto.name,
      status: dto.status,
    })),
    total: meta.totalItems,
    page: meta.currentPage,
    limit: meta.itemsPerPage,
  };
}
