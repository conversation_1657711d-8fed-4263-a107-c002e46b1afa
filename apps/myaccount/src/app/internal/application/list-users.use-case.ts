import { Injectable } from '@nestjs/common';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { User } from '../../entities/user.entity';
import { UsersRepository } from '../../users/repositories/users.repository';

@Injectable()
export class ListUsersUseCase {
  constructor(private readonly userRepository: UsersRepository) {}

  async execute(input: Input): Promise<Paginated<User>> {
    return this.userRepository.findAllowed(input.workspaceId, false, input.query);
  }
}

class Input {
  workspaceId: string;
  query: PaginateQuery;
}
