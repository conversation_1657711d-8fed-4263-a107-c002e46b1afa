import { Injectable } from '@nestjs/common';
import { User } from '../../entities/user.entity';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { UsersRepository } from '../../users/repositories/users.repository';

@Injectable()
export class ListAllUsersUseCase {
  constructor(private userRepository: UsersRepository) {}

  async execute(input: Input): Promise<Paginated<User>> {
    return this.userRepository.findAllowedIgnoringPagination(input.workspaceId, false, input.query);
  }
}

class Input {
  workspaceId: string;
  query: PaginateQuery;
}
