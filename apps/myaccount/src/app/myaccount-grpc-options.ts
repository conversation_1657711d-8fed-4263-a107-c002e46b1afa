import { Transport } from '@nestjs/microservices';
import { join } from 'path';

export const MYACCOUNT_GRPC_OPTIONS = {
  transport: Transport.GRPC,
  options: {
    package: 'users',
    protoPath: join(__dirname, './assets/protos/users.proto'),
    url: process.env.GRPC_URL || '0.0.0.0:50051',
    loader: {
      keepCase: false,
      longs: String,
      enums: String,
      defaults: true,
      oneofs: true,
    },
  },
} as const;
