import { Provider } from '@nestjs/common';
import { GetUserRolesUseCase } from './application/get-user-roles.use-case';
import { UserRolesRepository } from './domain/user-roles.repository.interface';
import { UserRolesTypeOrmRepository } from './infrastructure/user-roles-type-orm.repository';
import { ConfigService } from '@nestjs/config';
import { RoleResolversStrategies, ROLES_RESOLVERS_STRATEGIES } from './application/role-resolvers-tokens';
import {
  AnalyticsRoleResolver,
  KonquestRoleResolver,
  MyAccountRoleResolver,
  SmartzapRoleResolver,
} from './domain/roles-resolvers';
import { ROLES_BUILDERS_FACTORY, RolesBuilderFactory } from './application/roles-builders.factory';
import {
  ApplicationRolesBuilder,
  DefaultApplicationRolesBuilder,
} from './domain/roles-builders/application-roles.builder';
import { MyAccountRolesBuilder } from './domain/roles-builders/my-account-roles.builder';
import { SetUserRolesUseCase } from './application/set-user-roles.use-case';
import { PERSISTENT_ROLES_IDS } from './application/persistent-roles-tokens';
import { DeleteUserRolesUseCase } from './application/delete-user-roles.use-case';

const ROLES_RESOLVERS_PROVIDER: Provider = {
  provide: ROLES_RESOLVERS_STRATEGIES,
  useFactory: (configService: ConfigService): RoleResolversStrategies => {
    return new Map([
      [configService.get('KONQUEST_ID'), new KonquestRoleResolver()],
      [configService.get('MYACCOUNT_ID'), new MyAccountRoleResolver()],
      [configService.get('ANALYTICS_ID'), new AnalyticsRoleResolver()],
      [configService.get('SMARTZAP_ID'), new SmartzapRoleResolver()],
    ]);
  },
  inject: [ConfigService],
};

const ROLES_BUILDERS_FACTORY_PROVIDER: Provider = {
  provide: ROLES_BUILDERS_FACTORY,
  useFactory: (configService: ConfigService) => {
    const factoriesMap = new Map<string, ApplicationRolesBuilder>();
    const myAccountId = configService.get('MYACCOUNT_ID');
    const accountAdminRoleId = configService.get('MYACCOUNT_ACCOUNT_ADMIN_ROLE_ID');
    const keepsAdminRoleId = configService.get('KEEPS_ADMIN_ROLE_ID');
    const myAccountRolesBuilder = new MyAccountRolesBuilder([accountAdminRoleId], [keepsAdminRoleId]);
    factoriesMap.set(myAccountId, myAccountRolesBuilder);
    return new RolesBuilderFactory(factoriesMap, new DefaultApplicationRolesBuilder());
  },
  inject: [ConfigService],
};

const PERSISTENT_ROLES_PROVIDER: Provider = {
  provide: PERSISTENT_ROLES_IDS,
  useFactory: (configService: ConfigService) => {
    const keepsAdminRoleId = configService.get('KEEPS_ADMIN_ROLE_ID');
    return [keepsAdminRoleId];
  },
  inject: [ConfigService],
};

export const PROVIDERS: Provider[] = [
  GetUserRolesUseCase,
  SetUserRolesUseCase,
  DeleteUserRolesUseCase,
  ROLES_RESOLVERS_PROVIDER,
  ROLES_BUILDERS_FACTORY_PROVIDER,
  PERSISTENT_ROLES_PROVIDER,
  { provide: UserRolesRepository, useClass: UserRolesTypeOrmRepository },
];
