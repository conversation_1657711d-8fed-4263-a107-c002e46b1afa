import { Inject, Injectable, Logger } from '@nestjs/common';
import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { RoleResolversStrategies, ROLES_RESOLVERS_STRATEGIES } from './role-resolvers-tokens';
import { ListUserApplicationsRolesDto } from '../presentation/dtos/list-user-applications-roles.dto';
import { Application } from '../../entities/application.entity';
import { UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';

@Injectable()
export class GetUserRolesUseCase {
  private readonly logger = new Logger(GetUserRolesUseCase.name);

  constructor(
    private readonly userRolesRepository: UserRolesRepository,
    @Inject(ROLES_RESOLVERS_STRATEGIES) private readonly roleResolversStrategies: RoleResolversStrategies,
  ) {}

  async execute(userId: string, workspaceId: string): Promise<ListUserApplicationsRolesDto[]> {
    const user = await this.userRolesRepository.findUserById(userId);
    if (!user) {
      this.logger.warn(`Attempt to retrieve roles for non-existent user: ${userId}`);
      throw new UserRolesUserNotFoundException(userId);
    }

    const roles = await this.userRolesRepository.getUserRoles(userId, workspaceId);
    const groupedApplicationRoles = this.groupRolesByApplication(roles);
    const result: ListUserApplicationsRolesDto[] = [];

    groupedApplicationRoles.forEach((applicationRoles) => {
      applicationRoles.roles = this.resolveRoleForApplication(applicationRoles.application.id, applicationRoles.roles);
      result.push(applicationRoles);
    });

    return result;
  }

  private groupRolesByApplication(roles: UserRoleWorkspace[]) {
    const applicationsRoles = new Map<string, ListUserApplicationsRolesDto>();

    if (!roles?.length) {
      return applicationsRoles;
    }

    roles.forEach((workspaceRole) => {
      const application = workspaceRole.role.application;
      const applicationRolesDto =
        applicationsRoles.get(application.id) || this.buildNewApplicationRolesDto(application);

      applicationRolesDto.roles.push(workspaceRole.role.key);
      applicationsRoles.set(application.id, applicationRolesDto);
    });

    return applicationsRoles;
  }

  private resolveRoleForApplication(applicationId: string, applicationRoles: string[]) {
    if (!applicationRoles?.length) {
      return [];
    }

    const roleResolver = this.roleResolversStrategies.get(applicationId);
    if (!roleResolver) {
      // When no role resolver is found, we just return all the application roles
      this.logger.debug(`No role resolver found for application with id: ${applicationId}. Returning all roles.`);
      return applicationRoles;
    }

    return roleResolver.getHighestRole(applicationRoles);
  }

  private buildNewApplicationRolesDto(application: Application): ListUserApplicationsRolesDto {
    return {
      application: {
        id: application.id,
        name: application.name,
      },
      roles: [],
    };
  }
}
