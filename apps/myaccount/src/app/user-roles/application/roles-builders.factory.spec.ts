import { ApplicationRolesBuilder } from '../domain/roles-builders/application-roles.builder';
import { RolesBuilderFactory } from './roles-builders.factory';

class TestApplicationRolesBuilder extends ApplicationRolesBuilder {
  buildRoles(rolesIds: string[]): string[] {
    return rolesIds.map((roleId) => `role-${roleId}`);
  }
}

describe('RolesBuilderFactory', () => {
  let factory: RolesBuilderFactory;
  let defaultStrategy: ApplicationRolesBuilder;
  let customStrategy: ApplicationRolesBuilder;
  let strategies: Map<string, ApplicationRolesBuilder>;

  beforeEach(() => {
    defaultStrategy = new TestApplicationRolesBuilder();
    customStrategy = new TestApplicationRolesBuilder();
    strategies = new Map<string, ApplicationRolesBuilder>([['app1', customStrategy]]);
    factory = new RolesBuilderFactory(strategies, defaultStrategy);
  });

  it('should return the custom strategy for the given application ID', () => {
    const strategy = factory.get('app1');
    expect(strategy).toBe(customStrategy);
  });

  it('should return the default strategy when application ID is not found', () => {
    const strategy = factory.get('non-existent-app');
    expect(strategy).toBe(defaultStrategy);
  });
});
