import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { RolesBuilderFactory } from './roles-builders.factory';
import { SetUserRolesUseCase } from './set-user-roles.use-case';
import { SetUserApplicationsRolesDto } from '../presentation/dtos/set-user-applications-roles.dto';
import { ApplicationRolesBuilder } from '../domain/roles-builders/application-roles.builder';
import { Logger } from '@nestjs/common';
import { UserRolesNotFoundException, UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';
import { UserNotificationService } from '../../users-creator/services/user-notification.service';
import { PasswordService } from '../../users-creator/services/password.service';
import { ConfigService } from '@nestjs/config';

const DEFAULT_ROLES = [{ id: 'role-id-1' }, { id: 'role-id-2' }, { id: 'role-id-3' }];

const DEFAULT_DTO: SetUserApplicationsRolesDto[] = [
  { applicationId: 'app-id-1', roles: ['role-id-1', 'role-id-2'] },
  { applicationId: 'app-id-2', roles: ['role-id-3'] },
];

const PERSISTENT_ROLES_ID = ['keeps_admin_role_id'];

describe('SetUserRolesUseCase', () => {
  let useCase: SetUserRolesUseCase;
  let userRolesRepositoryMock: jest.Mocked<UserRolesRepository>;
  let rolesBuilderFactoryMock: jest.Mocked<RolesBuilderFactory>;
  let defaultRoleBuilderMock: jest.Mocked<ApplicationRolesBuilder>;
  let userNotificationServiceMock: jest.Mocked<UserNotificationService>;
  let passwordServiceMock: jest.Mocked<PasswordService>;
  let configServiceMock: jest.Mocked<ConfigService>;

  beforeEach(() => {
    userRolesRepositoryMock = {
      getUserRoles: jest.fn(),
      overrideUserRoles: jest.fn(),
      deleteUserRoles: jest.fn(),
      findApplicationRolesById: jest.fn().mockResolvedValue(DEFAULT_ROLES),
      findUserById: jest.fn().mockImplementation((userId) => Promise.resolve({ userId })),
      getAllUserRoles: jest.fn(),
    } as jest.Mocked<UserRolesRepository>;

    defaultRoleBuilderMock = { buildRoles: jest.fn().mockImplementation((rolesIds: string[]) => rolesIds) };

    rolesBuilderFactoryMock = {
      get: jest.fn().mockReturnValue(defaultRoleBuilderMock),
    } as unknown as jest.Mocked<RolesBuilderFactory>;

    userNotificationServiceMock = {
      notifyUserWithRoles: jest.fn(),
    } as unknown as jest.Mocked<UserNotificationService>;

    passwordServiceMock = {
      generatePassword: jest.fn().mockReturnValue('123456'),
      updatePassword: jest.fn(),
    } as unknown as jest.Mocked<PasswordService>;

    configServiceMock = {
      get: jest.fn(),
    } as unknown as jest.Mocked<ConfigService>;

    // Suppress debug logs from the logger
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => undefined);
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);

    useCase = new SetUserRolesUseCase(
      userRolesRepositoryMock,
      rolesBuilderFactoryMock,
      PERSISTENT_ROLES_ID,
      userNotificationServiceMock,
      passwordServiceMock,
      configServiceMock,
    );
  });

  it('should do nothing if rolesDTOs is empty', async () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';

    await useCase.execute(userId, workspaceId, []);

    expect(userRolesRepositoryMock.overrideUserRoles).not.toHaveBeenCalled();
  });

  it('should throw an exception if the user is not found', async () => {
    userRolesRepositoryMock.findUserById.mockResolvedValueOnce(null);

    await expect(useCase.execute('invalidUserId', 'workspaceId', DEFAULT_DTO)).rejects.toThrow(
      UserRolesUserNotFoundException,
    );
  });

  it('should throw an exception if one or more of the provided roles are not found', async () => {
    const invalidRolesDto: SetUserApplicationsRolesDto[] = [
      { applicationId: 'app-id-1', roles: ['invalid-role', 'role-id-2'] },
      { applicationId: 'app-id-2', roles: ['role-id-3'] },
    ];

    await expect(useCase.execute('user-id', 'workspaceId', invalidRolesDto)).rejects.toThrow(
      UserRolesNotFoundException,
    );
  });

  it('should call overrideUserRoles with the correct roles by application', async () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const rolesDTOs: SetUserApplicationsRolesDto[] = [
      { applicationId: 'app-id-1', roles: ['role-id-1', 'role-id-2'] },
      { applicationId: 'app-id-2', roles: ['role-id-3'] },
    ];

    userRolesRepositoryMock.getUserRoles.mockResolvedValue([]);
    userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([]);

    await useCase.execute(userId, workspaceId, rolesDTOs);

    const expectedRolesMap = new Map<string, string[]>();
    expectedRolesMap.set('app-id-1', ['role-id-1', 'role-id-2']);
    expectedRolesMap.set('app-id-2', ['role-id-3']);

    expect(rolesBuilderFactoryMock.get).toHaveBeenCalledWith('app-id-1');
    expect(rolesBuilderFactoryMock.get).toHaveBeenCalledWith('app-id-2');
    expect(userRolesRepositoryMock.overrideUserRoles).toHaveBeenCalledTimes(1);
    expect(userRolesRepositoryMock.overrideUserRoles).toHaveBeenCalledWith(
      userId,
      workspaceId,
      expectedRolesMap,
      PERSISTENT_ROLES_ID,
    );
  });

  describe('notification logic', () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const rolesDTOs: SetUserApplicationsRolesDto[] = [{ applicationId: 'app-id-1', roles: ['role-id-1'] }];

    const mockMyAccountRole = {
      id: 'myaccount-role-workspace-id',
      status: true,
      workspaceId: 'workspace-id',
      roleId: 'myaccount-role-id',
      userId: 'user-id',
      selfSignUp: false,
      role: {
        id: 'myaccount-role-id',
        application: { id: 'myaccount-app-id' },
      },
    } as any;

    const mockOtherAppRole = {
      id: 'other-role-workspace-id',
      status: true,
      workspaceId: 'workspace-id',
      roleId: 'other-role-id',
      userId: 'user-id',
      selfSignUp: false,
      role: {
        id: 'other-role-id',
        application: { id: 'other-app-id' },
      },
    } as any;

    beforeEach(() => {
      configServiceMock.get.mockImplementation((key: string) => {
        if (key === 'MYACCOUNT_ID') return 'myaccount-app-id';
        if (key === 'SMARTZAP_DEFAULT_ROLE') return 'smartzap-default-role-id';
        return undefined;
      });
    });

    it('should reset password and send notification when user had only MyAccount roles and gets additional roles', async () => {
      const currentRoles = [mockMyAccountRole, mockOtherAppRole];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockMyAccountRole]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue(currentRoles);

      await useCase.execute(userId, workspaceId, rolesDTOs);

      expect(passwordServiceMock.generatePassword).toHaveBeenCalledTimes(1);
      expect(passwordServiceMock.updatePassword).toHaveBeenCalledWith(userId, '123456', true);
      expect(userNotificationServiceMock.notifyUserWithRoles).toHaveBeenCalledWith(currentRoles, '123456', true);
    });

    it('should send notification without password reset when user already had other application roles', async () => {
      const currentRoles = [mockMyAccountRole, mockOtherAppRole];
      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockMyAccountRole, mockOtherAppRole]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue(currentRoles);

      await useCase.execute(userId, workspaceId, rolesDTOs);

      expect(passwordServiceMock.generatePassword).not.toHaveBeenCalled();
      expect(passwordServiceMock.updatePassword).not.toHaveBeenCalled();
      expect(userNotificationServiceMock.notifyUserWithRoles).toHaveBeenCalledWith(currentRoles, undefined, false);
    });

    it('should reset password and send notification when user had no previous roles and gets non-default role', async () => {
      const currentRoles = [mockOtherAppRole];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue(currentRoles);

      await useCase.execute(userId, workspaceId, rolesDTOs);

      expect(passwordServiceMock.generatePassword).toHaveBeenCalledTimes(1);
      expect(passwordServiceMock.updatePassword).toHaveBeenCalledWith(userId, '123456', true);
      expect(userNotificationServiceMock.notifyUserWithRoles).toHaveBeenCalledWith(currentRoles, '123456', true);
    });

    it('should not send notification when user has no current roles', async () => {
      const currentRoles = [];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockMyAccountRole]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue(currentRoles);

      await useCase.execute(userId, workspaceId, rolesDTOs);

      expect(passwordServiceMock.generatePassword).not.toHaveBeenCalled();
      expect(passwordServiceMock.updatePassword).not.toHaveBeenCalled();
      expect(userNotificationServiceMock.notifyUserWithRoles).not.toHaveBeenCalled();
    });

    it('should handle notification errors gracefully and log them', async () => {
      const currentRoles = [mockMyAccountRole, mockOtherAppRole];
      const notificationError = new Error('Notification service failed');

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockMyAccountRole]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue(currentRoles);

      userNotificationServiceMock.notifyUserWithRoles.mockRejectedValueOnce(notificationError);

      const loggerErrorSpy = jest.spyOn(Logger.prototype, 'error').mockImplementation(() => undefined);

      await expect(useCase.execute(userId, workspaceId, rolesDTOs)).resolves.not.toThrow();

      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'Failed to send notification after setting user roles:',
        notificationError,
      );

      loggerErrorSpy.mockRestore();
    });

    it('should handle password service errors gracefully and log them', async () => {
      const currentRoles = [mockMyAccountRole, mockOtherAppRole];
      const passwordError = new Error('Password service failed');

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([mockMyAccountRole]);
      userRolesRepositoryMock.getUserRoles.mockResolvedValue(currentRoles);

      passwordServiceMock.updatePassword.mockRejectedValueOnce(passwordError);

      const loggerErrorSpy = jest.spyOn(Logger.prototype, 'error').mockImplementation(() => undefined);

      await expect(useCase.execute(userId, workspaceId, rolesDTOs)).resolves.not.toThrow();

      expect(loggerErrorSpy).toHaveBeenCalledWith(
        'Failed to send notification after setting user roles:',
        passwordError,
      );

      loggerErrorSpy.mockRestore();
    });
  });

  describe('shouldResetPassword logic', () => {
    beforeEach(() => {
      configServiceMock.get.mockImplementation((key: string) => {
        if (key === 'MYACCOUNT_ID') return 'myaccount-app-id';
        if (key === 'SMARTZAP_DEFAULT_ROLE') return 'smartzap-default-role-id';
        return undefined;
      });
    });

    it('should return false when user already has non-default roles in any workspace', async () => {
      const userId = 'user-123';
      const newRolesDTOs = [{ applicationId: 'other-app-id', roles: ['role-123'] }];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([
        { role: { application: { id: 'other-app-id' }, id: 'existing-role' } } as any,
      ]);

      const shouldReset = await (useCase as any).shouldResetPassword(userId, newRolesDTOs);

      expect(shouldReset).toBe(false);
      expect(userRolesRepositoryMock.getAllUserRoles).toHaveBeenCalledWith(userId);
    });

    it('should return true when user only has default roles and receives first non-default role', async () => {
      const userId = 'user-123';
      const newRolesDTOs = [{ applicationId: 'other-app-id', roles: ['role-123'] }];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([
        { role: { application: { id: 'myaccount-app-id' }, id: 'myaccount-role' } } as any,
      ]);

      const shouldReset = await (useCase as any).shouldResetPassword(userId, newRolesDTOs);

      expect(shouldReset).toBe(true);
    });

    it('should return false when user only receives SMARTZAP_DEFAULT_ROLE', async () => {
      const userId = 'user-123';
      const newRolesDTOs = [{ applicationId: 'smartzap-app-id', roles: ['smartzap-default-role-id'] }];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([
        { role: { application: { id: 'myaccount-app-id' }, id: 'myaccount-role' } } as any,
      ]);

      const shouldReset = await (useCase as any).shouldResetPassword(userId, newRolesDTOs);

      expect(shouldReset).toBe(false);
    });

    it('should return false when user only receives MYACCOUNT roles', async () => {
      const userId = 'user-123';
      const newRolesDTOs = [{ applicationId: 'myaccount-app-id', roles: ['myaccount-role'] }];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([
        { role: { application: { id: 'myaccount-app-id' }, id: 'existing-myaccount-role' } } as any,
      ]);

      const shouldReset = await (useCase as any).shouldResetPassword(userId, newRolesDTOs);

      expect(shouldReset).toBe(false);
    });

    it('should return true when user has only default roles and receives non-default role along with SMARTZAP_DEFAULT_ROLE', async () => {
      const userId = 'user-123';
      const newRolesDTOs = [
        { applicationId: 'smartzap-app-id', roles: ['smartzap-default-role-id', 'smartzap-admin-role'] },
      ];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([
        { role: { application: { id: 'myaccount-app-id' }, id: 'myaccount-role' } } as any,
      ]);

      const shouldReset = await (useCase as any).shouldResetPassword(userId, newRolesDTOs);

      expect(shouldReset).toBe(true);
    });

    it('should return false when user has no existing roles and receives only default roles', async () => {
      const userId = 'user-123';
      const newRolesDTOs = [
        { applicationId: 'myaccount-app-id', roles: ['myaccount-role'] },
        { applicationId: 'smartzap-app-id', roles: ['smartzap-default-role-id'] },
      ];

      userRolesRepositoryMock.getAllUserRoles.mockResolvedValue([]);

      const shouldReset = await (useCase as any).shouldResetPassword(userId, newRolesDTOs);

      expect(shouldReset).toBe(false);
    });
  });
});
