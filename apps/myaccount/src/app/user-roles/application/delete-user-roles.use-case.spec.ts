import { DeleteUserRolesUseCase } from './delete-user-roles.use-case';
import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';
import { Chance } from 'chance';

describe('DeleteUserRolesUseCase', () => {
  let useCase: DeleteUserRolesUseCase;
  let userRolesRepository: jest.Mocked<UserRolesRepository>;
  const chance = Chance();

  const userId = chance.guid();
  const workspaceId = chance.guid();

  beforeEach(() => {
    userRolesRepository = {
      findUserById: jest.fn(),
      deleteUserRoles: jest.fn(),
    } as unknown as jest.Mocked<UserRolesRepository>;

    useCase = new DeleteUserRolesUseCase(userRolesRepository);
  });

  it('should delete user roles if user exists', async () => {
    userRolesRepository.findUserById.mockResolvedValue({ id: userId } as any);

    await useCase.execute(userId, workspaceId);

    expect(userRolesRepository.findUserById).toHaveBeenCalledWith(userId);
    expect(userRolesRepository.deleteUserRoles).toHaveBeenCalledWith(userId, workspaceId);
  });

  it('should throw UserRolesUserNotFoundException if user does not exist', async () => {
    userRolesRepository.findUserById.mockResolvedValue(null);

    await expect(useCase.execute(userId, workspaceId)).rejects.toThrow(UserRolesUserNotFoundException);

    expect(userRolesRepository.findUserById).toHaveBeenCalledWith(userId);
    expect(userRolesRepository.deleteUserRoles).not.toHaveBeenCalled();
  });
});
