import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { SetUserApplicationsRolesDto } from '../presentation/dtos/set-user-applications-roles.dto';
import { ROLES_BUILDERS_FACTORY, RolesBuilderFactory } from './roles-builders.factory';
import { UserRolesNotFoundException, UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';
import { PERSISTENT_ROLES_IDS } from './persistent-roles-tokens';
import { UserNotificationService } from '../../users-creator/services/user-notification.service';
import { PasswordService } from '../../users-creator/services/password.service';

@Injectable()
export class SetUserRolesUseCase {
  private readonly logger = new Logger(SetUserRolesUseCase.name);

  constructor(
    private readonly userRolesRepository: UserRolesRepository,
    @Inject(ROLES_BUILDERS_FACTORY) private readonly rolesBuilderFactory: RolesBuilderFactory,
    @Inject(PERSISTENT_ROLES_IDS) private readonly persistentRoleIds: string[],
    private readonly userNotificationService: UserNotificationService,
    private readonly passwordService: PasswordService,
    private readonly configService: ConfigService,
  ) {}

  async execute(userId: string, workspaceId: string, rolesDTOs: SetUserApplicationsRolesDto[]) {
    if (!rolesDTOs.length) {
      return;
    }

    const user = await this.userRolesRepository.findUserById(userId);
    if (!user) {
      this.logger.warn(`Attempt to define roles for non-existent user: ${userId}`);
      throw new UserRolesUserNotFoundException(userId);
    }

    const notFoundRoles = await this.checkRolesExistence(rolesDTOs);
    if (notFoundRoles?.length) {
      this.logger.warn(`Attempt to define invalid roles for user`, notFoundRoles);
      throw new UserRolesNotFoundException(notFoundRoles);
    }

    const shouldResetPassword = await this.shouldResetPassword(userId, rolesDTOs);

    const rolesByApplication = this.createRolesByApplicationMap(rolesDTOs);
    await this.userRolesRepository.overrideUserRoles(userId, workspaceId, rolesByApplication, this.persistentRoleIds);

    try {
      const currentUserRoles = await this.userRolesRepository.getUserRoles(userId, workspaceId);
      if (currentUserRoles.length > 0) {
        if (shouldResetPassword) {
          const temporaryPassword = this.passwordService.generatePassword();
          await this.passwordService.updatePassword(userId, temporaryPassword, true);
          await this.userNotificationService.notifyUserWithRoles(currentUserRoles, temporaryPassword, true);
        } else {
          await this.userNotificationService.notifyUserWithRoles(currentUserRoles, undefined, false);
        }
      }
    } catch (error) {
      this.logger.error('Failed to send notification after setting user roles:', error);
    }
  }

  private createRolesByApplicationMap(rolesDTOs: SetUserApplicationsRolesDto[]) {
    const rolesByApplication = new Map<string, string[]>();

    rolesDTOs.forEach((rolesDTO) => {
      const applicationId = rolesDTO.applicationId;
      const rolesBuilder = this.rolesBuilderFactory.get(applicationId);
      const applicationRoles = rolesBuilder.buildRoles(rolesDTO.roles);
      rolesByApplication.set(rolesDTO.applicationId, applicationRoles);
    });

    return rolesByApplication;
  }

  private async checkRolesExistence(rolesDTOs: SetUserApplicationsRolesDto[]) {
    const rolesIds = rolesDTOs.flatMap((rolesDTO) => rolesDTO.roles);
    const applicationIds = rolesDTOs.map((rolesDTO) => rolesDTO.applicationId);
    const roles = await this.userRolesRepository.findApplicationRolesById(rolesIds, applicationIds);
    return rolesIds.filter((roleId) => !roles?.some((role) => role.id === roleId));
  }

  private async shouldResetPassword(userId: string, newRolesDTOs: SetUserApplicationsRolesDto[]): Promise<boolean> {
    const myAccountId = this.configService.get('MYACCOUNT_ID');
    const smartzapDefaultRoleId = this.configService.get('SMARTZAP_DEFAULT_ROLE');

    const allUserRoles = await this.userRolesRepository.getAllUserRoles(userId);

    const hadNonDefaultRoles = allUserRoles.some(
      (role) => role.role?.application?.id !== myAccountId && role.role?.id !== smartzapDefaultRoleId,
    );

    if (hadNonDefaultRoles) {
      return false;
    }
    const hasNewNonDefaultRoles = newRolesDTOs.some((roleDTO) => {
      if (roleDTO.applicationId !== myAccountId) {
        return roleDTO.roles.some((roleId) => roleId !== smartzapDefaultRoleId);
      }
      return false;
    });

    return hasNewNonDefaultRoles;
  }
}
