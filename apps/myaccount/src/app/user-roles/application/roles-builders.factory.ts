import { InjectionToken } from '@nestjs/common';
import { ApplicationRolesBuilder } from '../domain/roles-builders/application-roles.builder';

export const ROLES_BUILDERS_FACTORY: InjectionToken<RolesBuilderFactory> = Symbol('ROLES_BUILDERS_FACTORY');

/**
 * A map of application role resolvers by application id used for applications where the user can have more than one
 * role at the same time
 */
export class RolesBuilderFactory {
  private readonly strategies: Map<string, ApplicationRolesBuilder>;

  constructor(
    strategies: Map<string, ApplicationRolesBuilder>,
    private readonly defaultStrategy: ApplicationRolesBuilder,
  ) {
    this.strategies = strategies;
  }

  /**
   * Retrieves a strategy based on the provided application identifier.
   *
   * @param {string} applicationId - The unique identifier of the application for which the strategy is being retrieved.
   * @return {ApplicationRolesBuilder} The corresponding strategy if found, otherwise the default strategy.
   */
  get(applicationId: string): ApplicationRolesBuilder {
    return this.strategies.get(applicationId) || this.defaultStrategy;
  }
}
