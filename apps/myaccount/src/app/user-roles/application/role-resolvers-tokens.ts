import { InjectionToken } from '@nestjs/common';
import { ApplicationRoleResolver } from '../domain/roles-resolvers';

/**
 * A map of application role resolvers by application id used for applications where the user can have more than one
 * role at the same time
 */
export type RoleResolversStrategies = Map<string, ApplicationRoleResolver>;

export const ROLES_RESOLVERS_STRATEGIES: InjectionToken<RoleResolversStrategies> = Symbol('ROLES_RESOLVERS_STRATEGIES');
