import { Injectable, Logger } from '@nestjs/common';
import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { UserRolesUserNotFoundException } from '../domain/exceptions/user-roles.exceptions';

@Injectable()
export class DeleteUserRolesUseCase {
  private readonly logger = new Logger(DeleteUserRolesUseCase.name);

  constructor(private readonly userRolesRepository: UserRolesRepository) {}

  async execute(userId: string, workspaceId: string) {
    const user = await this.userRolesRepository.findUserById(userId);
    if (!user) {
      this.logger.warn(`Attempt to clear roles for non-existent user: ${userId}`);
      throw new UserRolesUserNotFoundException(userId);
    }

    await this.userRolesRepository.deleteUserRoles(userId, workspaceId);
  }
}
