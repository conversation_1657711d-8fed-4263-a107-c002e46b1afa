import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  ParseArrayPipe,
  Patch,
  SerializeOptions,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { GetUserRolesUseCase } from '../../application/get-user-roles.use-case';
import { ListUserApplicationsRolesDto } from '../dtos/list-user-applications-roles.dto';
import { MYACCOUNT_ADMIN_ROLES, Roles, TenantService } from '@keeps-node-apis/@core';
import { SetUserRolesUseCase } from '../../application/set-user-roles.use-case';
import { SetUserApplicationsRolesDto } from '../dtos/set-user-applications-roles.dto';
import { DeleteUserRolesUseCase } from '../../application/delete-user-roles.use-case';

@ApiTags('User roles')
@Controller('user-roles')
@Roles(MYACCOUNT_ADMIN_ROLES)
export class UserRolesController {
  constructor(
    private readonly getUserRolesUseCase: GetUserRolesUseCase,
    private readonly setUserRolesUseCase: SetUserRolesUseCase,
    private readonly deleteUserRolesUseCase: DeleteUserRolesUseCase,
    private readonly tenantService: TenantService,
  ) {}

  @Get(':id')
  @ApiOperation({
    description: 'Retrieve the specified user roles for the applications they have access in the current workspace',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list of user roles by application in the current workspace',
    type: ListUserApplicationsRolesDto,
    isArray: true,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'The user was not found',
  })
  @SerializeOptions({ type: ListUserApplicationsRolesDto })
  getUserRoles(@Param('id') userId: string) {
    return this.getUserRolesUseCase.execute(userId, this.tenantService.getTenantId());
  }

  @Patch(':id')
  @ApiOperation({
    description: 'Update the user roles for the provided applications in the current workspace',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list of user roles by application in the current workspace',
    type: ListUserApplicationsRolesDto,
    isArray: true,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'The user was not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'One or more of the provided roles are invalid',
  })
  @ApiBody({ type: SetUserApplicationsRolesDto, isArray: true })
  @SerializeOptions({ type: ListUserApplicationsRolesDto })
  async updateUserRoles(
    @Param('id') userId: string,
    @Body(new ParseArrayPipe({ items: SetUserApplicationsRolesDto })) roles: SetUserApplicationsRolesDto[],
  ) {
    await this.setUserRolesUseCase.execute(userId, this.tenantService.getTenantId(), roles);
    return this.getUserRolesUseCase.execute(userId, this.tenantService.getTenantId());
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    description: 'Delete all roles for a user in the current workspace',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'User roles successfully deleted',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'The user was not found',
  })
  async deleteAllUserRoles(@Param('id') userId: string) {
    await this.deleteUserRolesUseCase.execute(userId, this.tenantService.getTenantId());
  }
}
