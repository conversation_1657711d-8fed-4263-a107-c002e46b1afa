import { UserRolesController } from './user-roles.controller';
import { GetUserRolesUseCase } from '../../application/get-user-roles.use-case';
import { SetUserRolesUseCase } from '../../application/set-user-roles.use-case';
import { SetUserApplicationsRolesDto } from '../dtos/set-user-applications-roles.dto';
import { TenantService } from '@keeps-node-apis/@core';
import { DeleteUserRolesUseCase } from '../../application/delete-user-roles.use-case';

describe('UserRolesController', () => {
  let controller: UserRolesController;
  let getUserRolesUseCase: jest.Mocked<GetUserRolesUseCase>;
  let setUserRolesUseCase: jest.Mocked<SetUserRolesUseCase>;
  let deleteUserRolesUseCase: jest.Mocked<DeleteUserRolesUseCase>;
  let tenantService: jest.Mocked<TenantService>;

  beforeEach(async () => {
    getUserRolesUseCase = { execute: jest.fn() } as unknown as jest.Mocked<GetUserRolesUseCase>;
    setUserRolesUseCase = { execute: jest.fn() } as unknown as jest.Mocked<SetUserRolesUseCase>;
    deleteUserRolesUseCase = { execute: jest.fn() } as unknown as jest.Mocked<DeleteUserRolesUseCase>;
    tenantService = {
      getTenantId: jest.fn().mockReturnValue('workspace-id'),
    } as unknown as jest.Mocked<TenantService>;
    controller = new UserRolesController(
      getUserRolesUseCase,
      setUserRolesUseCase,
      deleteUserRolesUseCase,
      tenantService,
    );
  });

  it('should read the user roles', async () => {
    await controller.getUserRoles('user-id');

    expect(getUserRolesUseCase.execute).toHaveBeenCalledWith('user-id', 'workspace-id');
  });

  it('should set the user roles and return the user roles afterwards', async () => {
    const roles: SetUserApplicationsRolesDto[] = [{ applicationId: 'app1', roles: ['role1', 'role2'] }];

    await controller.updateUserRoles('user-id', roles);

    expect(setUserRolesUseCase.execute).toHaveBeenCalledWith('user-id', 'workspace-id', roles);
    expect(getUserRolesUseCase.execute).toHaveBeenCalledWith('user-id', 'workspace-id');
  });

  it('should delete user roles', async () => {
    await controller.deleteAllUserRoles('user-id');

    expect(deleteUserRolesUseCase.execute).toHaveBeenCalledWith('user-id', 'workspace-id');
  });
});
