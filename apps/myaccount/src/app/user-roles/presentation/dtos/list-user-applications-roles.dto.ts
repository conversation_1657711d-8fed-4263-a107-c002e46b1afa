import { Expose, Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class UserRoleApplication {
  @Expose()
  @ApiProperty({ description: 'The application name', example: 'Konquest' })
  name: string;

  @Expose()
  @ApiProperty({ description: 'The application uuid', example: '0abf08ea-d252-4d7c-ab45-ab3f9135c288' })
  id: string;
}

export class ListUserApplicationsRolesDto {
  @Expose()
  @ApiProperty({ description: 'The application for this user roles', type: UserRoleApplication })
  @Type(() => UserRoleApplication)
  application: UserRoleApplication;

  @Expose()
  @ApiProperty({
    description: 'The user roles for this application',
    isArray: true,
    type: String,
    example: ['admin', 'user'],
  })
  roles: string[];
}
