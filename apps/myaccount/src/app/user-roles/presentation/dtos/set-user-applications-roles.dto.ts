import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID } from 'class-validator';

export class SetUserApplicationsRolesDto {
  @ApiProperty({ description: 'The application uuid', example: '0abf08ea-d252-4d7c-ab45-ab3f9135c288' })
  @IsUUID()
  @IsNotEmpty()
  applicationId: string;

  @ApiProperty({
    description: 'The user roles uuids for this application, if none are provided, the current ones will be removed',
    type: String,
    isArray: true,
    example: ['f47ac10b-58cc-4372-a567-0e02b2c3d479', '550e8400-e29b-41d4-a716-************'],
  })
  @IsUUID('4', { each: true })
  roles: string[];
}
