import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { User } from '../../entities/user.entity';
import { Role } from '../../entities/role.entity';

export abstract class UserRolesRepository {
  /**
   * Retrieves the roles of a specific user within a specific workspace.
   *
   * @param {string} userId - The unique identifier of the user.
   * @param {string} workspaceId - The unique identifier of the workspace.
   * @return {Promise<UserRoleWorkspace[]>} A promise that resolves to an array of user roles within the workspace.
   */
  abstract getUserRoles(userId: string, workspaceId: string): Promise<UserRoleWorkspace[]>;

  /**
   * Overrides the user roles for specific applications within a given workspace.
   *
   * This method deletes all user role associations for the specified applications
   * and replaces them with the new provided roles, if any.
   *
   * @param {string} userId - The ID of the user whose roles are to be updated.
   * @param {string} workspaceId - The ID of the workspace where roles are being managed.
   * @param {Map<string, string[]>} userRoles - A map where the key is the application ID,
   *        and the value is an array of role IDs to assign to the user for that application.
   * @param persistentRolesIds - List of roles that should not be overwritten when defining the user roles, if the user has any of them.
   * @return {Promise<void>} A promise that resolves when the user roles are successfully overridden.
   */
  abstract overrideUserRoles(
    userId: string,
    workspaceId: string,
    userRoles: Map<string, string[]>,
    persistentRolesIds: string[],
  ): Promise<void>;

  /**
   * Finds a user by their unique identifier.
   *
   * @param {string} userId - The unique identifier of the user to find.
   * @return {Promise<User>} A promise that resolves to the user object or null if not found.
   */
  abstract findUserById(userId: string): Promise<User>;

  /**
   * Retrieves a list of application roles based on the provided role IDs and application IDs.
   *
   * @param {string[]} roleIds - An array of role IDs to search for.
   * @param {string[]} applicationIds - An array of application IDs to filter the roles by.
   * @return {Promise<Role[]>} A promise that resolves to an array of roles matching the given criteria.
   */
  abstract findApplicationRolesById(roleIds: string[], applicationIds: string[]): Promise<Role[]>;

  /**
   * Delete the user roles for within a given workspace.
   *
   * This method deletes all user role associations for the specified workspace
   *
   * @param {string} userId - The ID of the user whose roles are to be updated.
   * @param {string} workspaceId - The ID of the workspace where roles are being managed.
   * @return {Promise<void>} A promise that resolves when the user roles are successfully deleted.
   */
  abstract deleteUserRoles(userId: string, workspaceId: string): Promise<void>;

  /**
   * Retrieves all roles of a specific user across all workspaces.
   *
   * @param {string} userId - The unique identifier of the user.
   * @return {Promise<UserRoleWorkspace[]>} A promise that resolves to an array of user roles across all workspaces.
   */
  abstract getAllUserRoles(userId: string): Promise<UserRoleWorkspace[]>;
}
