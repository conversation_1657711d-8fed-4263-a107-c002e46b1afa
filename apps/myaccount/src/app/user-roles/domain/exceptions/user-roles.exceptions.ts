import { DomainException } from '../../../@core/common/exceptions/base.exception';

export class UserRolesDomainException extends DomainException {
  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message, `USER_ROLES_${errorCode}`, details);
  }
}

export class UserRolesNotFoundException extends UserRolesDomainException {
  constructor(invalidRolesIds: string[]) {
    const ids = invalidRolesIds.join(', ');
    super(`Attempt to define invalid roles for user. Invalid roles ids: ${ids}.`, 'INVALID_ROLES_IDS');
  }
}

export class UserRolesUserNotFoundException extends UserRolesDomainException {
  constructor(userId: string) {
    super(`Non-existent user with id: ${userId}.`, 'USER_NOT_FOUND');
  }
}
