import { DefaultApplicationRolesBuilder } from './application-roles.builder';

describe('DefaultApplicationRolesBuilder', () => {
  let builder: DefaultApplicationRolesBuilder;

  beforeEach(() => {
    builder = new DefaultApplicationRolesBuilder();
  });

  it('should return the same array of role IDs passed to it', () => {
    const rolesIds = ['role1', 'role2', 'role3'];

    const result = builder.buildRoles(rolesIds);

    expect(result).toEqual(rolesIds);
  });

  it('should return an empty array if an empty array is passed', () => {
    const rolesIds: string[] = [];

    const result = builder.buildRoles(rolesIds);

    expect(result).toEqual([]);
  });
});
