import { ApplicationRolesBuilder } from './application-roles.builder';
import { Injectable } from '@nestjs/common';

/**
 * This class is responsible for building roles for MyAccount.
 * It will always return the permanent roles ids in the result and exclude any ignored roles, if provided.
 * Duplicates roles are also removed.
 */
@Injectable()
export class MyAccountRolesBuilder implements ApplicationRolesBuilder {
  constructor(
    private readonly permanentRoleIds: string[],
    private readonly ignoredRolesIds?: string[],
  ) {}

  buildRoles(rolesIds: string[]): string[] {
    const updatedRoles = new Set([...this.permanentRoleIds, ...rolesIds]);
    this.ignoredRolesIds?.forEach((roleId) => updatedRoles.delete(roleId));
    return Array.from(updatedRoles.values());
  }
}
