import { MyAccountRolesBuilder } from './my-account-roles.builder';

describe('MyAccountRolesBuilder', () => {
  const permanentRoleIds = ['role1', 'role2'];
  const excludedRoles = ['excludedRole'];
  let builder: MyAccountRolesBuilder;

  beforeEach(() => {
    builder = new MyAccountRolesBuilder(permanentRoleIds, excludedRoles);
  });

  it('should include permanent roles in the result even if no roles are provided', () => {
    const result = builder.buildRoles([]);

    expect(result).toEqual(expect.arrayContaining(permanentRoleIds));
  });

  it('should combine provided roles with permanent roles', () => {
    const rolesIds = ['role3', 'role4'];

    const result = builder.buildRoles(rolesIds);

    expect(result).toEqual(expect.arrayContaining([...permanentRoleIds, ...rolesIds]));
  });

  it('should remove duplicates from provided roles and permanent roles', () => {
    const rolesIds = ['role2', 'role3'];

    const result = builder.buildRoles(rolesIds);

    expect(result).toEqual(['role1', 'role2', 'role3']);
  });

  it('should remove ignored roles from the result', () => {
    const rolesIds = ['role1', 'role2', 'excludedRole'];

    const result = builder.buildRoles(rolesIds);

    expect(result).toEqual(['role1', 'role2']);
  });
});
