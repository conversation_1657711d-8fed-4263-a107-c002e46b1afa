import { Injectable } from '@nestjs/common';

/**
 * The `ApplicationRolesBuilder` provides an abstract definition for the logic used
 * for mapping the roleIds for an application.
 */
export abstract class ApplicationRolesBuilder {
  /**
   * Returns the list of built roles for the application.
   */
  abstract buildRoles(rolesIds: string[]): string[];
}

/**
 * The DefaultApplicationRolesBuilder takes in an array of role identifiers and simply returns the same value
 */
@Injectable()
export class DefaultApplicationRolesBuilder implements ApplicationRolesBuilder {
  buildRoles(rolesIds: string[]): string[] {
    return rolesIds;
  }
}
