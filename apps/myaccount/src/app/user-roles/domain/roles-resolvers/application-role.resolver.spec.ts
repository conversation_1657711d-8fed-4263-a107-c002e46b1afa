import { ApplicationRoleResolver, RolesHierarchy } from './application-role.resolver';

class TestApplicationRoleResolver extends ApplicationRoleResolver {
  readonly rolesHierarchy: RolesHierarchy;

  constructor(hierarchy: RolesHierarchy) {
    super();
    this.rolesHierarchy = hierarchy;
  }
}

describe('ApplicationRoleResolver', () => {
  let resolver: TestApplicationRoleResolver;

  beforeEach(() => {
    const rolesHierarchy: RolesHierarchy = new Map([
      ['Admin', 3],
      ['Manager', 2],
      ['User', 1],
    ]);

    resolver = new TestApplicationRoleResolver(rolesHierarchy);
  });

  it('should return the highest role from the user roles', () => {
    const userRoles = ['User', 'Admin', 'Manager'];

    const result = resolver.getHighestRole(userRoles);

    expect(result).toEqual(['Admin']);
  });

  it('should return an empty array when no roles are provided', () => {
    const userRoles: string[] = [];

    const result = resolver.getHighestRole(userRoles);

    expect(result).toEqual([]);
  });

  it('should return an empty array when rolesHierarchy is not defined', () => {
    resolver = new TestApplicationRoleResolver(new Map());
    const userRoles = ['User', 'Admin'];

    const result = resolver.getHighestRole(userRoles);

    expect(result).toEqual([]);
  });

  it('should return an empty array when no matching role exists in the hierarchy', () => {
    const userRoles = ['Guest'];
    const result = resolver.getHighestRole(userRoles);
    expect(result).toEqual([]);
  });
});
