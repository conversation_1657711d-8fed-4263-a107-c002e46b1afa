import { SMARTZAP_ROLES } from '@keeps-node-apis/@core';
import { Injectable } from '@nestjs/common';
import { ApplicationRoleResolver, RolesHierarchy } from './application-role.resolver';

const SMARTZAP_ROLES_HIERARCHY: RolesHierarchy = new Map([
  [SMARTZAP_ROLES.ADMIN, 2],
  [SMARTZAP_ROLES.USER, 1],
]);

@Injectable()
export class SmartzapRoleResolver extends ApplicationRoleResolver {
  readonly rolesHierarchy = SMARTZAP_ROLES_HIERARCHY;
}
