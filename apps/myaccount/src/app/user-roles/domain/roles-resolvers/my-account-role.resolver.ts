import { MYACCOUNT_ROLES } from '@keeps-node-apis/@core';
import { Injectable } from '@nestjs/common';
import { ApplicationRoleResolver, RolesHierarchy } from './application-role.resolver';

const MY_ACCOUNT_ROLES_HIERARCHY: RolesHierarchy = new Map([
  [MYACCOUNT_ROLES.KEEPS_ADMIN, 10],
  [MYACCOUNT_ROLES.COMPANY_ADMIN, 2],
  [MYACCOUNT_ROLES.ACCOUNT_ADMIN, 1],
]);

@Injectable()
export class MyAccountRoleResolver extends ApplicationRoleResolver {
  readonly rolesHierarchy = MY_ACCOUNT_ROLES_HIERARCHY;

  override getHighestRole(userRoles: string[]): string[] {
    const highestPriorityRole = super.getHighestRole(userRoles);
    // When the user has the account admin role, we also return it to be displayed; otherwise, it would be challenging
    // to determine if the user has the necessary role for basic interaction in the applications
    const shouldIncludeAccountAdminRole =
      userRoles.includes(MYACCOUNT_ROLES.ACCOUNT_ADMIN) && !highestPriorityRole.includes(MYACCOUNT_ROLES.ACCOUNT_ADMIN);
    if (shouldIncludeAccountAdminRole) {
      highestPriorityRole.push(MYACCOUNT_ROLES.ACCOUNT_ADMIN);
    }
    return highestPriorityRole;
  }
}
