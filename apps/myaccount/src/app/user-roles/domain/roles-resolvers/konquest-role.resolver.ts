import { Injectable } from '@nestjs/common';
import { ApplicationRoleResolver } from './application-role.resolver';
import { KONQUEST_ROLES } from '@keeps-node-apis/@core';

const KONQUEST_ROLES_HIERARCHY = new Map([
  [KONQUEST_ROLES.SUPER_ADMIN, 5],
  [KONQUEST_ROLES.ADMIN, 4],
  [KONQUEST_ROLES.CONTENT, 3],
  [KONQUEST_ROLES.INSTRUCTOR, 2],
  [KONQUEST_ROLES.USER, 1],
]);

@Injectable()
export class KonquestRoleResolver extends ApplicationRoleResolver {
  readonly rolesHierarchy = KONQUEST_ROLES_HIERARCHY;
}
