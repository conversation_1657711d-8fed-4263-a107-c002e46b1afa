import { ANALYTICS_ROLES } from '@keeps-node-apis/@core';
import { Injectable } from '@nestjs/common';
import { ApplicationRoleResolver, RolesHierarchy } from './application-role.resolver';

const ANALYTICS_ROLES_HIERARCHY: RolesHierarchy = new Map([
  [ANALYTICS_ROLES.BASIC_ANALYTICS_ADMIN, 3],
  [ANALYTICS_ROLES.BASIC_ANALYTICS_LEADER, 2],
  [ANALYTICS_ROLES.BASIC_ANALYTICS_USER, 1],
]);

@Injectable()
export class AnalyticsRoleResolver extends ApplicationRoleResolver {
  readonly rolesHierarchy = ANALYTICS_ROLES_HIERARCHY;
}
