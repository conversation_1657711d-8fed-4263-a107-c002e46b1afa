/**
 * Map defining the hierarchy of roles for the application.
 * The key is the role key, and the value is the role priority in descending order (higher value = higher priority).
 */
export type RolesHierarchy = Map<string, number>;

/**
 * Abstract base class for resolving application roles based on a predefined hierarchy.
 * This class provides functionality to determine the highest role from a set of user roles
 * according to the defined hierarchy specific to each application.
 */
export abstract class ApplicationRoleResolver {
  /**
   * Map defining the hierarchy of roles for the application.
   * Each extending class should initialize this map with their specific role hierarchy.
   */
  protected readonly rolesHierarchy: RolesHierarchy;

  /**
   * Determines the highest role from the given list of user roles based on a predefined hierarchy.
   *
   * @param {string[]} userRoles - An array of user roles to evaluate.
   * @return {string[]} An array containing the highest role based on the hierarchy, or an empty array if no roles are provided or hierarchy is not defined.
   */
  getHighestRole(userRoles: string[]): string[] {
    if (!this.rolesHierarchy?.size || !userRoles?.length) {
      return [];
    }

    let highestHierarchyRoleIndex = -1;
    let highestHierarchy = 0;

    for (let i = 0; i < userRoles.length; i++) {
      const currentRoleHierarchy = this.rolesHierarchy.get(userRoles[i]);

      if (currentRoleHierarchy > highestHierarchy) {
        highestHierarchyRoleIndex = i;
        highestHierarchy = currentRoleHierarchy;
      }
    }

    const highestHierarchyRole = userRoles[highestHierarchyRoleIndex];
    return highestHierarchyRole ? [highestHierarchyRole] : [];
  }
}
