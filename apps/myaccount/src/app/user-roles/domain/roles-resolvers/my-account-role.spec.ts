import { MyAccountRoleResolver } from './my-account-role.resolver';
import { MYACCOUNT_ROLES } from '@keeps-node-apis/@core';

describe('MyAccountRoleResolver', () => {
  let resolver: MyAccountRoleResolver;

  beforeEach(() => {
    resolver = new MyAccountRoleResolver();
  });

  it('should include the account admin role if it is present in the user roles', () => {
    const userRoles = [MYACCOUNT_ROLES.ACCOUNT_ADMIN, MYACCOUNT_ROLES.COMPANY_ADMIN];

    const result = resolver.getHighestRole(userRoles);

    expect(result).toEqual([MYACCOUNT_ROLES.COMPANY_ADMIN, MYACCOUNT_ROLES.ACCOUNT_ADMIN]);
  });

  it('should return the highest priority role when the user is not an account admin', () => {
    const userRoles = [MYACCOUNT_ROLES.COMPANY_ADMIN, MYACCOUNT_ROLES.KEEPS_ADMIN];

    const result = resolver.getHighestRole(userRoles);

    expect(result).toEqual([MYACCOUNT_ROLES.KEEPS_ADMIN]);
  });
});
