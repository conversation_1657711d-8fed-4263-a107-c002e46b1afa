import { UserRolesRepository } from '../domain/user-roles.repository.interface';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { DataSource, EntityManager, In, Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { Role } from '../../entities/role.entity';
import { User } from '../../entities/user.entity';

@Injectable()
export class UserRolesTypeOrmRepository implements UserRolesRepository {
  private readonly repository: Repository<UserRoleWorkspace>;
  private readonly rolesRepository: Repository<Role>;
  private readonly userRepository: Repository<User>;

  constructor(private readonly dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(UserRoleWorkspace);
    this.rolesRepository = this.dataSource.getRepository(Role);
    this.userRepository = this.dataSource.getRepository(User);
  }

  getUserRoles(userId: string, workspaceId: string): Promise<UserRoleWorkspace[]> {
    return this.repository
      .createQueryBuilder('workspace_role')
      .select()
      .where('user_id = :userId', { userId })
      .andWhere('workspace_id = :workspaceId', { workspaceId })
      .leftJoinAndSelect('workspace_role.user', 'user')
      .leftJoinAndSelect('workspace_role.role', 'role')
      .leftJoinAndSelect('role.application', 'application')
      .getMany();
  }

  findApplicationRolesById(roleIds: string[], applicationIds: string[]) {
    return this.rolesRepository.findBy({ id: In(roleIds), applicationId: In(applicationIds) });
  }

  findUserById(userId: string) {
    return this.userRepository.findOneBy({ id: userId });
  }

  async overrideUserRoles(
    userId: string,
    workspaceId: string,
    userRolesIdsByApplication: Map<string, string[]>,
    persistentRolesIds: string[],
  ) {
    await this.dataSource.transaction(async (manager) => {
      // Delete all user roles for the provided applications
      const applicationIds = Array.from(userRolesIdsByApplication.keys());
      await this.deleteUserRolesByApplication(userId, workspaceId, applicationIds, manager, persistentRolesIds);

      // Define the new roles for all the applications
      const newRoles = this.createRoles(userId, workspaceId, userRolesIdsByApplication, manager);
      if (newRoles.length) {
        await manager.save(UserRoleWorkspace, newRoles);
      }
    });
  }

  private async deleteUserRolesByApplication(
    userId: string,
    workspaceId: string,
    applicationIds: string[],
    manager: EntityManager,
    persistentRolesIds: string[],
  ) {
    const rolesForDeletion = await manager
      .createQueryBuilder(Role, 'role')
      .select('role.id')
      .where('role.application_id IN (:...applicationIds)', { applicationIds })
      .andWhere('role.id NOT IN (:...persistentRolesIds)', { persistentRolesIds })
      .getMany();
    const roleIdsForDeletion = rolesForDeletion.map((role) => role.id);

    await manager
      .createQueryBuilder(UserRoleWorkspace, 'workspace_role')
      .delete()
      .from(UserRoleWorkspace)
      .where('user_id = :userId', { userId })
      .andWhere('workspace_id = :workspaceId', { workspaceId })
      .andWhere('role_id IN (:...roleIdsForDeletion)', {
        roleIdsForDeletion,
      })
      .execute();
  }

  async deleteUserRoles(userId: string, workspaceId: string) {
    await this.repository.delete({ userId: userId, workspaceId: workspaceId });
  }

  private createRoles(
    userId: string,
    workspaceId: string,
    userRolesIdsByApplication: Map<string, string[]>,
    manager: EntityManager,
  ) {
    const newRoles: UserRoleWorkspace[] = [];

    for (const [applicationId, rolesIds] of userRolesIdsByApplication) {
      const applicationRoles = rolesIds.map((roleId) =>
        this.createUserRoleWorkspace(userId, workspaceId, roleId, applicationId, manager),
      );
      newRoles.push(...applicationRoles);
    }

    return newRoles;
  }

  private createUserRoleWorkspace(
    userId: string,
    workspaceId: string,
    roleId: string,
    applicationId: string,
    manager: EntityManager,
  ) {
    return manager.create(UserRoleWorkspace, {
      userId,
      role: { id: roleId, application: { id: applicationId } },
      workspace: { id: workspaceId },
      status: true,
      selfSignUp: false,
    });
  }

  getAllUserRoles(userId: string): Promise<UserRoleWorkspace[]> {
    return this.repository
      .createQueryBuilder('workspace_role')
      .select()
      .where('user_id = :userId', { userId })
      .leftJoinAndSelect('workspace_role.user', 'user')
      .leftJoinAndSelect('workspace_role.role', 'role')
      .leftJoinAndSelect('role.application', 'application')
      .getMany();
  }
}
