import { Test } from '@nestjs/testing';
import { UserRolesTypeOrmRepository } from './user-roles-type-orm.repository';
import { DataSource, Repository } from 'typeorm';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { Role } from '../../entities/role.entity';

const PERSISTENT_ROLES_IDS = ['keeps_admin_role_id'];

describe('UserRolesTypeOrmRepository', () => {
  let repository: UserRolesTypeOrmRepository;
  let dataSourceMock: jest.Mocked<DataSource>;
  let ormRepositoryMock: jest.Mocked<Repository<UserRoleWorkspace>>;

  beforeEach(async () => {
    dataSourceMock = {
      getRepository: jest.fn(),
      transaction: jest.fn(),
    } as unknown as jest.Mocked<DataSource>;

    ormRepositoryMock = {
      createQueryBuilder: jest.fn(),
      findBy: jest.fn(),
      findOneBy: jest.fn(),
    } as unknown as jest.Mocked<Repository<UserRoleWorkspace>>;

    dataSourceMock.getRepository.mockReturnValue(ormRepositoryMock);

    const moduleRef = await Test.createTestingModule({
      providers: [UserRolesTypeOrmRepository, { provide: DataSource, useValue: dataSourceMock }],
    }).compile();

    repository = moduleRef.get<UserRolesTypeOrmRepository>(UserRolesTypeOrmRepository);
  });

  describe('getUserRoles', () => {
    it('should return user roles filtered by userId and workspaceId', async () => {
      const queryBuilderMock = {
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        leftJoinAndSelect: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([{ roleId: 'role1' }]),
      };

      ormRepositoryMock.createQueryBuilder.mockReturnValue(queryBuilderMock as any);

      const result = await repository.getUserRoles('user1', 'workspace1');

      expect(ormRepositoryMock.createQueryBuilder).toHaveBeenCalledWith('workspace_role');
      expect(queryBuilderMock.where).toHaveBeenCalledWith('user_id = :userId', {
        userId: 'user1',
      });
      expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('workspace_id = :workspaceId', {
        workspaceId: 'workspace1',
      });
      expect(result).toEqual([{ roleId: 'role1' }]);
    });
  });

  describe('overrideUserRoles', () => {
    it('should override user roles within a transaction', async () => {
      const transactionManagerMock = {
        createQueryBuilder: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        where: jest.fn().mockReturnThis(),
        getMany: jest.fn().mockResolvedValue([{ id: 'role1' }]),
        delete: jest.fn().mockReturnThis(),
        from: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        execute: jest.fn(),
        create: jest.fn().mockReturnValue({ id: 'newRole1' }),
        save: jest.fn(),
      };

      const transactionMock = jest.fn().mockImplementation((callback) => callback(transactionManagerMock));
      dataSourceMock.transaction.mockImplementation(transactionMock);

      const userRoles = new Map<string, string[]>();
      userRoles.set('app1', ['role1', 'role2']);

      await repository.overrideUserRoles('user1', 'workspace1', userRoles, PERSISTENT_ROLES_IDS);

      expect(dataSourceMock.transaction).toHaveBeenCalled();
      expect(transactionManagerMock.createQueryBuilder).toHaveBeenCalledWith(Role, 'role');
      expect(transactionManagerMock.select).toHaveBeenCalledWith('role.id');
      expect(transactionManagerMock.where).toHaveBeenCalledWith('role.application_id IN (:...applicationIds)', {
        applicationIds: ['app1'],
      });
      expect(transactionManagerMock.andWhere).toHaveBeenCalledWith('role.id NOT IN (:...persistentRolesIds)', {
        persistentRolesIds: PERSISTENT_ROLES_IDS,
      });
      expect(transactionManagerMock.from).toHaveBeenCalledWith(UserRoleWorkspace);
      expect(transactionManagerMock.save).toHaveBeenCalled();
    });
  });
});
