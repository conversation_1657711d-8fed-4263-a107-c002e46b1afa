import { Module } from '@nestjs/common';
import { UserRolesController } from './presentation/controllers/user-roles.controller';
import { PROVIDERS } from './user-roles.providers';
import { EntitiesModule } from '../entities/entities.module';
import { NotificationModule, MobileNotificationsModule } from '@keeps-node-apis/@core';
import { UsersCreatorModule } from '../users-creator/users-creator.module';

@Module({
  controllers: [UserRolesController],
  imports: [EntitiesModule, NotificationModule, MobileNotificationsModule, UsersCreatorModule],
  providers: PROVIDERS,
})
export class UserRolesModule {}
