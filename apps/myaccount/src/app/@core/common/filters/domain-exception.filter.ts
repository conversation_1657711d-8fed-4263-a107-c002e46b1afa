import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { DomainException } from '../exceptions/base.exception';

@Catch(DomainException)
export class DomainExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(DomainExceptionFilter.name);

  catch(exception: DomainException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    const status = HttpStatus.BAD_REQUEST;
    const errorResponse: any = {
      timestamp: new Date().toISOString(),
      path: request.url,
      statusCode: status,
      errorCode: exception.errorCode,
      message: exception.message,
      details: exception.details,
    };

    this.logger.warn(`${request.method} ${request.url} - ${status}: ${exception.message}`, exception.stack);
    response.status(status).json(errorResponse);
  }
}
