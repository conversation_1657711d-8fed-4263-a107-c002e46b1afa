import { ExceptionFilter, Catch, ArgumentsHost, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { DomainException } from '../exceptions/base.exception';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(GlobalExceptionFilter.name);

  catch(exception: DomainException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorResponse: any = {
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    if (exception instanceof HttpException) {
      status = exception.getStatus();
      errorResponse = {
        ...errorResponse,
        statusCode: status,
        message: exception?.getResponse()?.['message'] || exception.message,
      };
    } else {
      errorResponse = {
        ...errorResponse,
        statusCode: status,
        message: 'Internal server error',
      };
    }

    if (status >= 500) {
      this.logger.error(`${request.method} ${request.url} - ${status}`, exception.stack);
    } else {
      this.logger.warn(`${request.method} ${request.url} - ${status}:`, exception.stack);
    }

    response.status(status).json(errorResponse);
  }
}
