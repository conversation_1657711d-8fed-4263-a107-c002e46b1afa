import { cpf } from 'cpf-cnpj-validator';
import { TransformFnParams } from 'class-transformer';
import { Logger } from '@nestjs/common';

const logger = new Logger('TransformCpf');

export function TransformCpf({ value }: TransformFnParams): string {
  if (value === null || value === '') {
    return null;
  }
  let cleaned = cpf.strip(value);

  if (cleaned.length < 11) {
    cleaned = cleaned.padStart(11, '0');
  }

  if (!cpf.isValid(cleaned)) {
    logger.warn(`Invalid CPF provided: "${value}" (after cleanup: "${cleaned}")`);
    return null;
  }

  value = cleaned;
  return value.toString();
}
