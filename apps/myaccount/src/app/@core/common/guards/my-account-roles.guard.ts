import { hasRoles, Roles } from '@keeps-node-apis/@core';
import { CanActivate, ExecutionContext, ForbiddenException, Injectable, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { UsersService } from '../../../users/services/users.service';

/**
 * Permissive role guard; permissions are defined using the {@link Roles} decorator.
 * Supports both Keycloak realm roles (prefixed with "realm_access.") and tenant-specific roles.
 * Tenant roles are retrieved via MyAccount's {@link UsersService}.
 * Should not be used on public routes, as it requires a valid tenant (x-client) context.
 */
@Injectable()
export class MyAccountRolesGuard implements CanActivate {
  private readonly logger = new Logger(MyAccountRolesGuard.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly reflector: Reflector,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const routeRoles = this.reflector.get(Roles, context.getHandler());
    const request = context.switchToHttp().getRequest();
    if (!routeRoles?.length) return true;

    const user = request.user;
    if (!user) throw new ForbiddenException('User not authenticated');

    const { realmRoles, tenantRoles } = this.extractRoles(routeRoles);

    if (realmRoles.length && this.hasRealmRoles(user, realmRoles)) {
      return true;
    }

    if (tenantRoles.length) {
      const tenantId = request?.tenantId;
      const userTenantRoles = await this.usersService.getUserRoles(user.sub, tenantId);
      request.user.roles = userTenantRoles;
      return hasRoles(tenantRoles, userTenantRoles);
    }

    return false;
  }

  private extractRoles(roles: string[]) {
    const prefix = 'realm_access.';
    return {
      realmRoles: roles.filter((role) => role.startsWith(prefix)).map((role) => role.slice(prefix.length)),
      tenantRoles: roles.filter((role) => !role.startsWith(prefix)),
    };
  }

  private hasRealmRoles(user: any, required: string[]): boolean {
    const tokenRoles = user?.realm_access?.roles || [];
    return hasRoles(required, tokenRoles);
  }
}
