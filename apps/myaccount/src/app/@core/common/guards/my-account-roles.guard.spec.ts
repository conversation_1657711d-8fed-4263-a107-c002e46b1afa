import { MyAccountRolesGuard } from './my-account-roles.guard';
import { UsersService } from '../../../users/services/users.service';
import { Reflector } from '@nestjs/core';
import { HttpArgumentsHost } from '@nestjs/common/interfaces';
import { ExecutionContext } from '@nestjs/common';

const DEFAULT_ROLES = ['admin', 'super_admin'];

describe('MyAccountRolesGuard', () => {
  let guard: MyAccountRolesGuard;
  let usersServiceMock: jest.Mocked<UsersService>;
  let reflectorMock: jest.Mocked<Reflector>;
  let httpContext: jest.Mocked<HttpArgumentsHost>;
  let context: jest.Mocked<ExecutionContext>;

  beforeEach(() => {
    httpContext = {
      getRequest: jest.fn().mockReturnValue({
        headers: {
          authorization: 'Bearer mock-token',
        },
        user: {
          sub: 'mock-user-id',
          realm_access: {
            roles: ['keeps_platform_admin'],
          },
        },
        tenantId: 'mock-x-client',
      }),
    } as unknown as jest.Mocked<HttpArgumentsHost>;

    context = {
      switchToHttp: jest.fn().mockReturnValue(httpContext),
      getHandler: jest.fn().mockReturnValue({ name: 'MockHandlerName' }),
      getClass: jest.fn().mockReturnValue({ name: 'MockController' }),
    } as unknown as jest.Mocked<ExecutionContext>;

    usersServiceMock = { getUserRoles: jest.fn() } as unknown as jest.Mocked<UsersService>;

    reflectorMock = {
      get: jest.fn().mockReturnValue(DEFAULT_ROLES),
    } as unknown as jest.Mocked<Reflector>;

    guard = new MyAccountRolesGuard(usersServiceMock, reflectorMock);
  });

  it('should skip the guard check if the route has no roles defined', async () => {
    reflectorMock.get.mockReturnValueOnce(undefined);

    const result = await guard.canActivate(context);

    expect(result).toBe(true);
  });

  it('should deny the request if the user does not have any rules and log the params', async () => {
    const result = await guard.canActivate(context);

    expect(result).toBe(false);
    expect(usersServiceMock.getUserRoles).toHaveBeenCalledWith('mock-user-id', 'mock-x-client');
  });

  it('should allow the request if the user has the one of the required roles', async () => {
    usersServiceMock.getUserRoles.mockResolvedValueOnce(['user', 'admin']);

    const result = await guard.canActivate(context);

    expect(result).toBe(true);
  });

  it('should allow the request if the user has a realm_access role defined in the route', async () => {
    reflectorMock.get.mockReturnValue(['realm_access.keeps_platform_admin']);

    const result = await guard.canActivate(context);

    expect(result).toBe(true);
    expect(usersServiceMock.getUserRoles).not.toHaveBeenCalled();
  });
});
