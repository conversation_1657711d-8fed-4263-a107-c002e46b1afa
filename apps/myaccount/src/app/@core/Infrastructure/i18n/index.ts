import { LANGUAGES } from '@keeps-node-apis/@core';
import { I18nOptions } from 'nestjs-i18n';
import path from 'path';
import { JWTLocaleResolver } from './jwt-locale.resolver';

export const I18nConfigOptions: I18nOptions = {
  fallbackLanguage: LANGUAGES.PT_BR,
  throwOnMissingKey: true,
  loaderOptions: {
    path: path.join(__dirname, 'assets/i18n/'),
    watch: true,
  },
  resolvers: [new JWTLocaleResolver()],
};
