import { ApiProperty, PickType } from '@nestjs/swagger';
import { ApplicationDto } from './aplication.dto';
import { RoleInfoDto } from '../../users/dtos/role-info.dto';
import { Expose, Type } from 'class-transformer';

class ApplicationRoleDto extends PickType(RoleInfoDto, ['id', 'key']) {}

export class ApplicationWithRolesDto extends PickType(ApplicationDto, ['id', 'name'] as const) {
  @Expose()
  @Type(() => ApplicationRoleDto)
  @ApiProperty({ description: 'List of available roles for the application', type: [ApplicationRoleDto] })
  roles: ApplicationRoleDto[];
}
