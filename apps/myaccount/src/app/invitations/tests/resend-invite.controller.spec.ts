import { Test, TestingModule } from '@nestjs/testing';
import { NotificationsController } from '../presentation/controllers/resend-invitation.controller';
import { ResendInvitationUseCase } from '../application/resend-invitation.use-case';
import { ResendInvitationDto } from '../presentation/dtos/resend-invitation.dto';
import { ResendInvitationCommand } from '../domain/commands/resend-invitation.command';
import { TenantService } from '@keeps-node-apis/@core';

describe('InvitationsController', () => {
  let controller: NotificationsController;
  let resendInvitationUseCase: ResendInvitationUseCase;
  let tenantService: TenantService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [NotificationsController],
      providers: [
        {
          provide: ResendInvitationUseCase,
          useValue: { execute: jest.fn() },
        },
        {
          provide: TenantService,
          useValue: { getTenantId: jest.fn() },
        },
      ],
    }).compile();

    controller = module.get<NotificationsController>(NotificationsController);
    resendInvitationUseCase = module.get<ResendInvitationUseCase>(ResendInvitationUseCase);
    tenantService = module.get<TenantService>(TenantService);
  });

  it('should call use case with correct command', async () => {
    const userId = 'e7b8d254-fbb1-4b26-8f50-123456789abc';
    const workspaceId = 'workspace-uuid';
    const dto: ResendInvitationDto = { userId };

    (tenantService.getTenantId as jest.Mock).mockReturnValue(workspaceId);

    await controller.resendInvitation(dto);

    expect(resendInvitationUseCase.execute).toHaveBeenCalledWith(new ResendInvitationCommand(userId, workspaceId));
  });
});
