import { Test, TestingModule } from '@nestjs/testing';
import { InvitationDomainService } from '../domain/services/invitation.domain-service';

describe('InvitationDomainService', () => {
  let service: InvitationDomainService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [InvitationDomainService],
    }).compile();

    service = module.get<InvitationDomainService>(InvitationDomainService);
  });

  describe('generateTemporaryPassword', () => {
    it('should generate a 6-digit numeric password as string', () => {
      const password = service.generateTemporaryPassword();
      expect(password).toMatch(/^\d{6}$/); // Regex para 6 dígitos numéricos
    });

    it('should generate different values on multiple calls (not always same)', () => {
      const password1 = service.generateTemporaryPassword();
      const password2 = service.generateTemporaryPassword();
      expect(password1).not.toEqual(password2);
    });
  });

  describe('resolveLanguage', () => {
    it('should return provided language if specified', () => {
      expect(service.resolveLanguage('en')).toBe('en');
    });

    it('should return default "pt-BR" if language is not provided', () => {
      expect(service.resolveLanguage(undefined)).toBe('pt-BR');
    });

    it('should format language country', () => {
      expect(service.resolveLanguage('pt-br')).toBe('pt-BR');
    });
  });
});
