import { InvitationDomainService } from '../domain/services/invitation.domain-service';
import { InvitationNotifier } from '../infrastructure/services/invitation-notifier.service';
import { UsersRepository } from '../../users/repositories/users.repository';
import { KeycloakRepository } from '@keeps-node-apis/@core';
import { ResendInvitationUseCase } from '../application/resend-invitation.use-case';
import { Repository } from 'typeorm';
import { Workspace } from '../../entities/workspace.entity';
import { ResendInvitationCommand } from '../domain/commands/resend-invitation.command';
import { UserNotFoundException } from '../domain/exceptions/invitations.exceptions';
import { User } from '../../entities/user.entity';
import Chance from 'chance';

const chance = new Chance();

describe('ResendInvitationUseCase', () => {
  let useCase: ResendInvitationUseCase;
  let domainService: InvitationDomainService;
  let notifier: InvitationNotifier;
  let usersRepo: UsersRepository;
  let keycloakRepo: KeycloakRepository;
  let workspaceRepo: Partial<Repository<Workspace>>;

  beforeEach(() => {
    domainService = {
      generateTemporaryPassword: jest.fn().mockReturnValue(chance.string({ length: 8 })),
      resolveLanguage: jest.fn().mockReturnValue('en'),
    } as any;

    notifier = { sendInvitation: jest.fn() } as any;
    usersRepo = { findById: jest.fn() } as any;
    keycloakRepo = { resetUserPassword: jest.fn() } as any;
    workspaceRepo = { findOneByOrFail: jest.fn() };

    useCase = new ResendInvitationUseCase(
      domainService,
      notifier,
      usersRepo,
      keycloakRepo,
      workspaceRepo as Repository<Workspace>,
    );
  });

  it('should resend invitation successfully', async () => {
    const command = new ResendInvitationCommand(chance.guid(), chance.guid());

    const user = {
      id: command.userId,
      email: chance.email(),
      name: chance.name(),
      phone: chance.phone(),
      language: { name: 'en' },
    } as User;

    const workspace = {
      id: command.workspaceId,
      name: chance.company(),
      logoUrl: chance.url(),
    } as Workspace;

    (usersRepo.findById as jest.Mock).mockResolvedValue(user);
    (workspaceRepo.findOneByOrFail as jest.Mock).mockResolvedValue(workspace);

    await useCase.execute(command);

    expect(usersRepo.findById).toHaveBeenCalledWith(command.userId, command.workspaceId);
    expect(workspaceRepo.findOneByOrFail).toHaveBeenCalledWith({ id: command.workspaceId });
    expect(keycloakRepo.resetUserPassword).toHaveBeenCalledWith(user.id, expect.any(String), true);
    expect(notifier.sendInvitation).toHaveBeenCalledWith(user, workspace, expect.any(String), 'en');
  });

  it('should throw UserNotFoundException if user not found', async () => {
    const command = new ResendInvitationCommand(chance.guid(), chance.guid());

    (usersRepo.findById as jest.Mock).mockRejectedValue(new Error('User not found'));

    await expect(useCase.execute(command)).rejects.toThrow(new UserNotFoundException(command.userId));
  });
});
