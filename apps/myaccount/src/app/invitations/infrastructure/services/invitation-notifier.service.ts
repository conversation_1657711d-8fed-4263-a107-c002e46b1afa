import { NotificationDto, NotificationService } from '@keeps-node-apis/@core';
import { Inject, Injectable } from '@nestjs/common';
import { default as AppConfig } from '../../../config/modules/app.config';
import { ConfigType } from '@nestjs/config';
import { User } from '../../../entities/user.entity';
import { Workspace } from '../../../entities/workspace.entity';

@Injectable()
export class InvitationNotifier {
  constructor(
    private readonly notificationService: NotificationService,
    @Inject(AppConfig.KEY) private readonly config: ConfigType<typeof AppConfig>,
  ) {}

  async sendInvitation(user: User, workspace: Workspace, password: string, language: string) {
    const templateData = {
      company_id: workspace.id,
      company_logo: workspace.logoUrl,
      company: workspace.name,
      user_email: user.email,
      user_login: user.email,
      user_name: user.name,
      user_phone: user.phone,
      user_password: password,
      app_web_link: this.config.applicationsWebUlrs.myaccount,
    };

    const notification: NotificationDto = {
      subject: 'konquest_email_subject',
      template: 'myaccount_resend_invite.html',
      workspaceId: workspace.id,
      templateData,
      language,
    };

    await this.notificationService.notifyViaEmail(notification, user.email);
  }
}
