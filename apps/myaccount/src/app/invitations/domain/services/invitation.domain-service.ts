import { Injectable } from '@nestjs/common';

@Injectable()
export class InvitationDomainService {
  generateTemporaryPassword(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  resolveLanguage(language?: string): string {
    if (!language) {
      return 'pt-BR';
    }

    const [lang, country] = language.split('-');
    if (!lang || !country) {
      return language;
    }

    return `${lang.toLowerCase()}-${country.toUpperCase()}`;
  }
}
