import { DomainException } from '../../../@core/common/exceptions/base.exception';

export class InvitationsDomainException extends DomainException {
  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message, `INVITATIONS_${errorCode}`, details);
  }
}

export class UserNotFoundException extends InvitationsDomainException {
  constructor(userId: string) {
    super(`Non-existent user with id: ${userId}.`, 'USER_NOT_FOUND');
  }
}
