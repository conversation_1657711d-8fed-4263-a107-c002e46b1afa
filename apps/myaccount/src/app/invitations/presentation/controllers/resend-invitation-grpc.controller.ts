import { Controller } from '@nestjs/common';
import { GrpcMethod } from '@nestjs/microservices';
import { ResendInvitationUseCase } from '../../application/resend-invitation.use-case';
import { ResendInvitationCommand } from '../../domain/commands/resend-invitation.command';
import { GrpcRequest, GrpcWorkspaceId } from '@keeps-node-apis/@core';

interface ResendInvitationRequest {
  userId: string;
}

interface ResendInvitationResponse {
  status: string;
}

@Controller()
export class InvitationGrpcController {
  constructor(private readonly resendInvitationUseCase: ResendInvitationUseCase) {}

  @GrpcMethod('InvitationService', 'ResendInvitation')
  async resendInvitation(
    @GrpcRequest() data: ResendInvitationRequest,
    @GrpcWorkspaceId() workspaceId: string,
  ): Promise<ResendInvitationResponse> {
    await this.resendInvitationUseCase.execute(new ResendInvitationCommand(data.userId, workspaceId));
    return { status: 'OK' };
  }
}
