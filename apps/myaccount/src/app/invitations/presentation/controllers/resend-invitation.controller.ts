import { Body, Controller, HttpCode, HttpStatus, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import { Roles, MYACCOUNT_ADMIN_ROLES, TenantService } from '@keeps-node-apis/@core';
import { ResendInvitationDto } from '../dtos/resend-invitation.dto';
import { ResendInvitationCommand } from '../../domain/commands/resend-invitation.command';
import { ResendInvitationUseCase } from '../../application/resend-invitation.use-case';

@ApiTags('Invitations')
@Controller('invitations')
@Roles(MYACCOUNT_ADMIN_ROLES)
export class NotificationsController {
  constructor(
    private readonly resendInvitationUseCase: ResendInvitationUseCase,
    private readonly tenantService: TenantService,
  ) {}

  @Post('resend-invitation')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Resend invitation to a specific user in the current workspace',
    description: 'Sends an access invitation to the specified user within the workspace context',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Invitation resent successfully',
  })
  async resendInvitation(@Body() body: ResendInvitationDto): Promise<void> {
    const workspaceId = this.tenantService.getTenantId();
    await this.resendInvitationUseCase.execute(new ResendInvitationCommand(body.userId, workspaceId));
  }
}
