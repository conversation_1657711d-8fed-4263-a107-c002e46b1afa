import { Injectable } from '@nestjs/common';
import { UserDataDto } from '../../users/dtos/create-user.dto';
import { EmployeeInfoCreateDto } from '../../users/dtos/employee-info-create.dto';
import { plainToInstance } from 'class-transformer';

@Injectable()
export class UserDataNormalizerService {
  normalizeUserData(userData: any): UserDataDto {
    const normalizedData = this.convertKeysToCamelCase(userData);

    if (!normalizedData.profile) {
      normalizedData.profile = {};
    }

    const userDto = plainToInstance(UserDataDto, {});
    const profileDto = plainToInstance(EmployeeInfoCreateDto, {});

    const userProperties = this.getAllPropertyNames(userDto);
    const profileProperties = this.getAllPropertyNames(profileDto);

    Object.keys(normalizedData).forEach((key) => {
      if (key === 'profile') return;

      if (
        (profileProperties.includes(key) && !userProperties.includes(key)) ||
        (profileProperties.includes(key) && this.propertyExistsInExampleProfile(key))
      ) {
        normalizedData.profile[key] = normalizedData[key];
        delete normalizedData[key];
      }
    });

    return normalizedData;
  }

  private propertyExistsInExampleProfile(key: string): boolean {
    const exampleProfileProps = ['director', 'manager', 'areaOfActivity', 'job'];

    return exampleProfileProps.includes(key);
  }

  private getAllPropertyNames(obj: any): string[] {
    if (!obj) return [];

    const properties = new Set<string>();

    Object.getOwnPropertyNames(obj).forEach((prop) => {
      if (prop !== 'constructor' && typeof obj[prop] !== 'function') {
        properties.add(prop);
      }
    });

    const metadataProperties = Reflect.getMetadataKeys?.(obj) || [];
    metadataProperties.forEach((prop) => {
      if (typeof prop === 'string') {
        properties.add(prop);
      }
    });

    let proto = Object.getPrototypeOf(obj);
    while (proto && proto !== Object.prototype) {
      Object.getOwnPropertyNames(proto).forEach((prop) => {
        if (prop !== 'constructor' && typeof proto[prop] !== 'function') {
          properties.add(prop);
        }
      });
      proto = Object.getPrototypeOf(proto);
    }

    return Array.from(properties);
  }

  private convertKeysToCamelCase(data: any): any {
    if (data === null || typeof data !== 'object') {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.convertKeysToCamelCase(item));
    }

    const result: any = {};

    Object.keys(data).forEach((key) => {
      const camelKey = this.toCamelCase(key);
      result[camelKey] = this.convertKeysToCamelCase(data[key]);
    });

    return result;
  }

  private toCamelCase(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }
}
