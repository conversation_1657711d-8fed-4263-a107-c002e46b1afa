import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { LanguageDto } from './language.dto';
import { RoleSimpleDto } from './role.dto';
import { EmployeeInfoDto } from './employee-info.dto';

export class UserDetailResponseDto {
  @ApiProperty({
    type: String,
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    type: String,
    description: 'Full name of the user',
    example: '<PERSON>',
  })
  @Expose()
  name: string;

  @ApiProperty({
    type: String,
    description: "User's nickname/display name",
    example: 'Johnny',
  })
  @Expose()
  nickname: string;

  @ApiProperty({
    type: String,
    description: 'Primary email address',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiProperty({
    type: String,
    description: 'Secondary email address',
    example: '<EMAIL>',
  })
  @Expose()
  secondaryEmail: string;

  @ApiProperty({
    type: String,
    description: 'Phone number',
    example: '+****************',
  })
  @Expose()
  phone: string;

  @ApiProperty({
    type: String,
    description: "URL of the user's avatar/profile picture",
    example: 'https://example.com/avatars/user123.jpg',
  })
  @Expose()
  avatar: string;

  @ApiProperty({
    type: String,
    description: "User's gender",
    example: 'Male',
  })
  @Expose()
  gender: string;

  @ApiProperty({
    type: String,
    description: 'Job title/role',
    example: 'Senior Software Engineer',
  })
  @Expose()
  job: string;

  @ApiProperty({
    type: String,
    description: 'Date of birth (YYYY-MM-DD)',
    example: '1990-01-01',
  })
  @Expose()
  birthday: string;

  @ApiProperty({
    type: String,
    description: 'Full address',
    example: '123 Main St, City, Country',
  })
  @Expose()
  address: string;

  @ApiProperty({
    type: String,
    description: 'Country of residence',
    example: 'United States',
  })
  @Expose()
  country: string;

  @ApiProperty({
    type: Boolean,
    description: 'User status (active/inactive)',
    example: true,
  })
  @Expose()
  status: boolean;

  @ApiProperty({
    type: String,
    description: "User's timezone",
    example: 'America/New_York',
  })
  @Expose()
  timeZone: string;

  @ApiProperty({ type: String, description: 'CPF' })
  @Expose()
  cpf: string;

  @ApiProperty({ type: String, description: 'Employee Enterprise ID' })
  @Expose()
  ein: string;

  @ApiProperty({ type: String, description: 'Education' })
  @Expose()
  education: string;

  @ApiProperty({ type: String, description: 'Ethnicity' })
  @Expose()
  ethnicity: string;

  @ApiProperty({ type: String, description: 'Marital status' })
  @Expose()
  maritalStatus: string;

  @ApiProperty({ type: String, description: 'Hierarchical level' })
  @Expose()
  hierarchicalLevel: string;

  @ApiProperty({ type: String, description: 'Contract type' })
  @Expose()
  contractType: string;

  @ApiProperty({ type: String, description: 'Admission date (YYYY-MM-DD)' })
  @Expose()
  admissionDate: string;

  @ApiProperty({
    type: [RoleSimpleDto],
    description: 'Simplified list of user roles and permissions',
  })
  @Expose()
  @Type(() => RoleSimpleDto)
  roles: RoleSimpleDto[];

  @ApiProperty({
    type: EmployeeInfoDto,
    description: 'Employee Info',
  })
  @Expose()
  @Type(() => EmployeeInfoDto)
  employeeInfo: EmployeeInfoDto;

  @ApiProperty({
    type: LanguageDto,
    description: 'Detailed language preference',
  })
  @Expose()
  @Type(() => LanguageDto)
  language: LanguageDto;
}
