import { Repository, SelectQueryBuilder } from 'typeorm';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import UserRolesRepository from './user-roles.repository';
import { Injectable } from '@nestjs/common';
import { UserRoleListParamsDto } from '../dtos/user-role-list-params.dto';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class UserRolesTypeORMRepository implements UserRolesRepository {
  constructor(@InjectRepository(UserRoleWorkspace) readonly _repository: Repository<UserRoleWorkspace>) {}

  private joinEntities(queryBuilder: SelectQueryBuilder<UserRoleWorkspace>): SelectQueryBuilder<UserRoleWorkspace> {
    return queryBuilder
      .leftJoinAndMapOne('UserRoleWorkspace.user', 'UserRoleWorkspace.user', 'user')
      .leftJoinAndMapOne('UserRoleWorkspace.workspace', 'UserRoleWorkspace.workspace', 'workspace')
      .leftJoinAndMapOne('UserRoleWorkspace.role', 'UserRoleWorkspace.role', 'role');
  }

  findAllowed(workspaceId: string, filters?: UserRoleListParamsDto): SelectQueryBuilder<UserRoleWorkspace> {
    const queryBuilder = this.repository
      .createQueryBuilder()
      .select()
      .where('workspace_id = :workspaceId', { workspaceId });

    this.joinEntities(queryBuilder);

    if (filters?.roleId) {
      queryBuilder.andWhere('role_id = :roleId', { roleId: filters.roleId });
    }
    if (filters?.userStatus != undefined) {
      queryBuilder.andWhere('user.status = :userStatus', {
        userStatus: filters.userStatus,
      });
    }
    return queryBuilder;
  }

  async userHasRoleOnWorkspace(userId: string, workspaceId: string): Promise<boolean> {
    const queryBuilder = this.repository
      .createQueryBuilder()
      .where('user_id = :userId', { userId })
      .andWhere('workspace_id = :workspaceId', { workspaceId });
    return queryBuilder.getExists();
  }

  async deleteUserWorkspace(userId: string, workspaceId: string) {
    return this.repository.delete({ userId, workspaceId });
  }

  userHasRoleOnApplication(userId: string, workspaceId: string, applicationId: string): Promise<boolean> {
    return this.repository
      .createQueryBuilder()
      .select()
      .where('user_id = :userId', { userId })
      .andWhere('workspace_id = :workspaceId', { workspaceId })
      .leftJoinAndMapOne('UserRoleWorkspace.role', 'UserRoleWorkspace.role', 'role')
      .andWhere('role.application_id = :applicationId', { applicationId })
      .getExists();
  }

  userHasAnyRole(userId: string, workspaceId: string, roleIds: string[]): Promise<boolean> {
    return this.repository
      .createQueryBuilder()
      .select()
      .where('user_id = :userId', { userId })
      .andWhere('workspace_id = :workspaceId', { workspaceId })
      .andWhere('role_id IN (:...roleIds)', { roleIds })
      .getExists();
  }

  get repository(): Repository<UserRoleWorkspace> {
    return this._repository;
  }
}
