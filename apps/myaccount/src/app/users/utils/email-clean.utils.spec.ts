import { cleanEmail } from './email-clean.utils';
import { InvalidEmailException } from '../exceptions/invalid-email.exception';

describe('cleanEmail', () => {
  it('should return cleaned email when email is valid', () => {
    const validEmail = '<EMAIL>';
    const result = cleanEmail(validEmail);
    expect(result).toBe('<EMAIL>');
  });

  it('should remove invalid characters while maintaining valid structure', () => {
    const emailWithInvalidChars = 'u$er#name@exa!mple.com';
    const result = cleanEmail(emailWithInvalidChars);
    expect(result).toBe('<EMAIL>');
  });

  it('should throw exception when cleaning results in invalid email', () => {
    expect(() => cleanEmail('u$er@n@me@exa!mple.com')).toThrow(InvalidEmailException);
  });

  it('should throw exception when email is undefined', () => {
    expect(() => cleanEmail(undefined)).toThrow(InvalidEmailException);
  });

  it('should throw exception when email is empty', () => {
    expect(() => cleanEmail('')).toThrow(InvalidEmailException);
  });

  it('should throw exception when email does not contain @', () => {
    expect(() => cleanEmail('userexample.com')).toThrow(InvalidEmailException);
  });

  it('should throw exception when email contains multiple @', () => {
    expect(() => cleanEmail('user@<EMAIL>')).toThrow(InvalidEmailException);
  });

  it('should throw exception when local part (before @) is missing', () => {
    expect(() => cleanEmail('@example.com')).toThrow(InvalidEmailException);
  });

  it('should throw exception when domain (after @) is missing', () => {
    expect(() => cleanEmail('user@')).toThrow(InvalidEmailException);
  });

  it('should preserve valid special characters like +, ., - and _', () => {
    const emailWithSpecialChars = '<EMAIL>';
    const result = cleanEmail(emailWithSpecialChars);
    expect(result).toBe('<EMAIL>');
  });
});
