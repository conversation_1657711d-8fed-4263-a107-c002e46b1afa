import { Test, TestingModule } from '@nestjs/testing';
import { UserRolesService } from './user-roles.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import UserRolesRepository from '../repositories/user-roles.repository';
import { UserRoleListParamsDto } from '../dtos/user-role-list-params.dto';
import { plainToInstance } from 'class-transformer';
import { UserRoleListDto } from '../dtos/user-role-list.dto';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { NotAllowedException, PageDto } from '@keeps-node-apis/@core';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import { Role } from '../../entities/role.entity';
import { RoleDto } from '../dtos/role.dto';
import { UserRoleCreateDto } from '../dtos/user-role-create.dto';

describe('UserRolesService', () => {
  let service: UserRolesService;
  let mockUserRolesWorkspaceRepository: jest.Mocked<Partial<UserRolesRepository>>;
  let mockSelectQueryBuilder: jest.Mocked<Partial<SelectQueryBuilder<UserRoleWorkspace>>>;
  let mockUserRolesRepository = Repository<Role>;

  beforeEach(async () => {
    mockUserRolesWorkspaceRepository = {
      userHasRoleOnWorkspace: jest.fn(),
      findAllowed: jest.fn(),
      repository: {
        create: jest.fn(),
        findOneOrFail: jest.fn(),
        delete: jest.fn(),
        findBy: jest.fn(),
        save: jest.fn(),
      } as unknown as Repository<UserRoleWorkspace>,
    };
    mockSelectQueryBuilder = {
      orderBy: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getCount: jest.fn().mockReturnValue(10),
      getRawAndEntities: jest.fn(),
    };
    mockUserRolesRepository = { findOneBy: jest.fn() } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserRolesService,
        {
          provide: UserRolesRepository,
          useValue: mockUserRolesWorkspaceRepository,
        },
        {
          provide: getRepositoryToken(Role),
          useValue: mockUserRolesRepository,
        },
      ],
    }).compile();

    service = module.get<UserRolesService>(UserRolesService);
  });

  describe('verifyUserPermission', () => {
    it('should throw NotAllowedException when user does not have permission', async () => {
      mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace.mockResolvedValue(false);
      await expect(service.verifyUserPermission('userId', 'workspaceId')).rejects.toThrow(NotAllowedException);
    });

    it('should not throw when user has permission', async () => {
      mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace.mockResolvedValue(true);
      await expect(service.verifyUserPermission('userId', 'workspaceId')).resolves.not.toThrow();
    });
  });

  describe('findAllowed', () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const mockFilters: UserRoleListParamsDto = {
      page: 0,
      perPage: 10,
      skip: 0,
    };
    const mockUserRoles = [
      {
        id: 'role1',
        userId: '124',
        user: { name: 'user-bob' },
        roleId: '1',
        workspaceId: '4123',
        selfSignUp: true,
      },
      {
        id: 'role2',
        userId: '125',
        user: { name: 'user-mar' },
        roleId: '2',
        workspaceId: '123',
        selfSignUp: true,
      },
    ] as UserRoleWorkspace[];

    it('should retrieve and return user roles after verifying permissions', async () => {
      const mockPaginatedRoles = {
        items: mockUserRoles,
        page: 0,
        total: 10,
        hasPreviousPage: false,
        hasNextPage: true,
      };

      mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace.mockResolvedValue(true);
      mockUserRolesWorkspaceRepository.findAllowed.mockReturnValue(mockSelectQueryBuilder as any);
      mockSelectQueryBuilder.getRawAndEntities.mockResolvedValue({
        entities: mockUserRoles,
        raw: [],
      });

      const result = await service.findAllowed(userId, workspaceId, mockFilters);

      expect(mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace).toHaveBeenCalledWith(userId, workspaceId);
      expect(mockUserRolesWorkspaceRepository.findAllowed).toHaveBeenCalledWith(workspaceId, mockFilters);

      expect(result).toEqual(
        plainToInstance(PageDto<UserRoleListDto>, {
          ...mockPaginatedRoles,
          items: plainToInstance(UserRoleListDto, mockPaginatedRoles.items),
        }),
      );
    });

    it('should throw NotAllowedException when user does not have permission', async () => {
      mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace.mockResolvedValue(false);

      await expect(service.findAllowed(userId, workspaceId, mockFilters)).rejects.toThrow(NotAllowedException);

      expect(mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace).toHaveBeenCalledWith(userId, workspaceId);
      expect(mockUserRolesWorkspaceRepository.findAllowed).not.toHaveBeenCalled();
    });
  });

  describe('checkAllowedAddUserRole', () => {
    it('should return true when user has keeps_admin role', async () => {
      const userRoles = [
        {
          role: { key: 'keeps_admin' },
        },
      ] as UserRoleWorkspace[];

      const roleToAddOrRemove = {
        role: { key: 'company_admin' },
      } as UserRoleWorkspace;

      const result = await service.checkAllowedAddUserRole(userRoles, roleToAddOrRemove);
      expect(result).toBe(true);
    });

    it('should return true when user has company_admin role and tries to add non-keeps_admin role', async () => {
      const userRoles = [
        {
          role: { key: 'company_admin' },
        },
      ] as UserRoleWorkspace[];

      const roleToAddOrRemove = {
        role: { key: 'regular_user' },
      } as UserRoleWorkspace;

      const result = await service.checkAllowedAddUserRole(userRoles, roleToAddOrRemove);
      expect(result).toBe(true);
    });

    it('should return false when company_admin tries to add keeps_admin role', async () => {
      const userRoles = [
        {
          role: { key: 'company_admin' },
        },
      ] as UserRoleWorkspace[];

      const roleToAddOrRemove = {
        role: { key: 'keeps_admin' },
      } as UserRoleWorkspace;

      const result = await service.checkAllowedAddUserRole(userRoles, roleToAddOrRemove);
      expect(result).toBe(false);
    });
  });

  describe('deleteUserRole', () => {
    const mockUserRoleId = 'user-role-id';
    const mockUserLoggedId = 'logged-user-id';

    it('should delete role directly when isIntegration is true', async () => {
      (mockUserRolesWorkspaceRepository.repository.delete as jest.Mock).mockResolvedValue({ affected: 1, raw: {} });

      await service.deleteUserRole(mockUserRoleId, mockUserLoggedId, true);

      expect(mockUserRolesWorkspaceRepository.repository.delete).toHaveBeenCalledWith(mockUserRoleId);
      expect(mockUserRolesWorkspaceRepository.repository.findOneOrFail).not.toHaveBeenCalled();
    });

    it('should delete role when user has sufficient permissions', async () => {
      const mockUserRole = {
        id: mockUserRoleId,
        workspaceId: 'workspace-1',
        role: { key: 'regular_user' },
      } as UserRoleWorkspace;

      const mockLoggedUserRoles = [
        {
          role: { key: 'keeps_admin' },
        },
      ] as UserRoleWorkspace[];

      (mockUserRolesWorkspaceRepository.repository.findOneOrFail as jest.Mock).mockResolvedValue(mockUserRole);
      (mockUserRolesWorkspaceRepository.repository.findBy as jest.Mock).mockResolvedValue(mockLoggedUserRoles);
      (mockUserRolesWorkspaceRepository.repository.delete as jest.Mock).mockResolvedValue({ affected: 1, raw: {} });

      await service.deleteUserRole(mockUserRoleId, mockUserLoggedId, false);

      expect(mockUserRolesWorkspaceRepository.repository.findOneOrFail).toHaveBeenCalledWith({
        where: { id: mockUserRoleId },
      });
      expect(mockUserRolesWorkspaceRepository.repository.delete).toHaveBeenCalledWith(mockUserRoleId);
    });
  });

  describe('createUserRole', () => {
    const mockUserRoleCreateDto = {
      user: { id: 'user-1' },
      role: { id: 'role-1' },
      workspace: { id: 'workspace-1' },
      self_sign_up: true,
    } as UserRoleCreateDto;

    const mockCreatedRole = {
      id: 'created-role-1',
      userId: 'user-1',
      roleId: 'role-1',
      workspaceId: 'workspace-1',
      selfSignUp: true,
      status: 'ACTIVE',
      createdDate: new Date(),
      updatedDate: new Date(),
    } as unknown as UserRoleWorkspace;

    const mockRoleWithRelations = {
      ...mockCreatedRole,
      role: { id: 'role-1', key: 'regular_user' },
      user: { id: 'user-1', name: 'Test User' },
      workspace: { id: 'workspace-1', name: 'Test Workspace' },
    } as UserRoleWorkspace;

    it('should create user role and return role with relations', async () => {
      (mockUserRolesWorkspaceRepository.repository.create as jest.Mock).mockReturnValue(mockCreatedRole);
      (mockUserRolesWorkspaceRepository.repository.save as jest.Mock).mockResolvedValue(mockCreatedRole);
      (mockUserRolesWorkspaceRepository.repository.findOneOrFail as jest.Mock).mockResolvedValue(mockRoleWithRelations);

      const result = await service.createUserRole(mockUserRoleCreateDto);

      expect(mockUserRolesWorkspaceRepository.repository.create as jest.Mock).toHaveBeenCalledWith(
        mockUserRoleCreateDto,
      );
      expect(mockUserRolesWorkspaceRepository.repository.findOneOrFail as jest.Mock).toHaveBeenCalledWith({
        where: { id: mockCreatedRole.id },
        relations: ['role', 'user', 'workspace'],
      });
      expect(result).toEqual(plainToInstance(RoleDto, mockRoleWithRelations));
    });

    it('should throw error when role creation fails', async () => {
      const error = new Error('Creation failed');
      (mockUserRolesWorkspaceRepository.repository.create as jest.Mock).mockImplementation(() => {
        throw error;
      });

      await expect(service.createUserRole(mockUserRoleCreateDto)).rejects.toThrow('Creation failed');
    });
  });

  describe('findUserWorkspace', () => {
    const userId = 'user-id';
    const workspaceId = 'workspace-id';
    const userAuthId = 'user-auth-id';
    const mockUserRole = {
      id: 'role1',
      userId: 'user-id',
      workspaceId: 'workspace-id',
      roleId: 'role-id',
    } as any;

    beforeEach(() => {
      mockUserRolesWorkspaceRepository.repository.findBy = jest.fn(); // Define como mock antes dos testes
    });

    it('should return user role after verifying permissions', async () => {
      mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace.mockResolvedValueOnce(true);
      (mockUserRolesWorkspaceRepository.repository.findBy as jest.Mock).mockResolvedValueOnce(mockUserRole);

      const result = await service.findUserWorkspace(userId, workspaceId, userAuthId);

      expect(mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace).toHaveBeenCalledWith(userAuthId, workspaceId);
      expect(mockUserRolesWorkspaceRepository.repository.findBy).toHaveBeenCalledWith({ userId, workspaceId });
      expect(result).toEqual(plainToInstance(RoleDto, mockUserRole));
    });

    it('should throw NotAllowedException when userAuthId does not have permission', async () => {
      mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace.mockResolvedValueOnce(false);

      await expect(service.findUserWorkspace(userId, workspaceId, userAuthId)).rejects.toThrow(NotAllowedException);

      expect(mockUserRolesWorkspaceRepository.userHasRoleOnWorkspace).toHaveBeenCalledWith(userAuthId, workspaceId);
      expect(mockUserRolesWorkspaceRepository.repository.findBy).not.toHaveBeenCalled();
    });
  });
});
