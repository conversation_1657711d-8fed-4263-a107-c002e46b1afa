import { registerAs } from '@nestjs/config';
import { CONFIG_CONSTANTS } from '../constants';

export default registerAs('app', () => ({
  nodeEnv: process.env.NODE_ENV,
  port: parseInt(process.env.PORT),
  gamificationUrl: process.env.GAMIFICATION_URL,
  s3: {
    region: process.env.AWS_S3_REGION,
    bucket: process.env.AWS_S3_BUCKET,
    basePath: process.env.AWS_S3_BASE_PATH,
    cdnUrl: process.env.AWS_S3_CDN_URL,
    credentials: {
      accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_S3_SECRET_ACCESS_KEY,
    },
  },
  avatar: {
    storage: {
      allowedTypes: Object.keys(CONFIG_CONSTANTS.ALLOWED_IMAGES),
      maxSizeInBytes: 5242880, // 5MB
      basePath: 'myaccount/user-avatar',
      cdnUrl: process.env.AWS_S3_CDN_URL,
    },
    image: {
      defaultSize: 200,
      quality: 80,
      format: 'png',
    },
  },
  applicationsWebUlrs: {
    myaccount: process.env.MYACCOUNT_WEB_URL,
  },
}));
