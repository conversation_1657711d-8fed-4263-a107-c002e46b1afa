import { Provider } from '@nestjs/common';
import { WorkspacesFilterSettingsRepository } from './domain/repositories/workspaces-filter-settings.repository';
import { WorkspacesFilterSettingsTypeOrmRepository } from './infrastructure/workspaces-filter-settings-type-orm.repository';
import { FindAllWorkspaceFilterSettingsUseCase } from './application/use-cases/find-all-workspace-filter-settings.use-case';
import { ToggleStatusWorkspaceFilterSettingUseCase } from './application/use-cases/toggle-status-workspace-filter-setting-use-case.service';

export const WORKSPACE_FILTER_SETTINGS_PROVIDERS: Provider[] = [
  FindAllWorkspaceFilterSettingsUseCase,
  ToggleStatusWorkspaceFilterSettingUseCase,
  { provide: WorkspacesFilterSettingsRepository, useClass: WorkspacesFilterSettingsTypeOrmRepository },
];

export const EXPORTED_PROVIDERS = [FindAllWorkspaceFilterSettingsUseCase];
