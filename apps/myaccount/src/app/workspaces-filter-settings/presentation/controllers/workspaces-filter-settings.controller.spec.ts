import { WorkspacesFilterSettingsController } from './workspaces-filter-settings.controller';
import { FindAllWorkspaceFilterSettingsUseCase } from '../../application/use-cases/find-all-workspace-filter-settings.use-case';
import { ToggleStatusWorkspaceFilterSettingUseCase } from '../../application/use-cases/toggle-status-workspace-filter-setting-use-case.service';
import { TenantService } from '@keeps-node-apis/@core';
import { Chance } from 'chance';
import { WorkspacesFilterSettingsResponseDto } from '../dtos/workspaces-filter-settings.response.dto';
import { WorkspaceFilterSettingType } from '../../domain/value-objects/workspace-filter-setting-type';

describe('WorkspacesFilterSettingsController', () => {
  const chance = new Chance();
  const tenantId = chance.guid();

  let controller: WorkspacesFilterSettingsController;
  let findAllWorkspaceFilterSettingsUseCase: jest.Mocked<FindAllWorkspaceFilterSettingsUseCase>;
  let toggleStatusWorkspaceFilterSettingUseCase: jest.Mocked<ToggleStatusWorkspaceFilterSettingUseCase>;
  let tenantService: jest.Mocked<TenantService>;

  beforeEach(async () => {
    findAllWorkspaceFilterSettingsUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<FindAllWorkspaceFilterSettingsUseCase>;

    toggleStatusWorkspaceFilterSettingUseCase = {
      execute: jest.fn(),
    } as unknown as jest.Mocked<ToggleStatusWorkspaceFilterSettingUseCase>;

    tenantService = { getTenantId: jest.fn().mockReturnValue(tenantId) } as unknown as jest.Mocked<TenantService>;

    controller = new WorkspacesFilterSettingsController(
      tenantService,
      findAllWorkspaceFilterSettingsUseCase,
      toggleStatusWorkspaceFilterSettingUseCase,
    );
  });

  it('should fetch the workspace filter settings list', async () => {
    const mockResults = [
      { id: chance.guid(), isEnabled: true, filterType: WorkspaceFilterSettingType.COMPLETED },
    ] as WorkspacesFilterSettingsResponseDto[];
    findAllWorkspaceFilterSettingsUseCase.execute.mockResolvedValueOnce(mockResults);

    await controller.findAllFilterSettings();

    expect(findAllWorkspaceFilterSettingsUseCase.execute).toHaveBeenCalledWith(tenantId);
  });

  it('should toggle an workspace filter setting enabled status', async () => {
    const filterSettingId = chance.guid();

    await controller.toggleStatus(filterSettingId, { isEnabled: true });

    expect(toggleStatusWorkspaceFilterSettingUseCase.execute).toHaveBeenCalledWith(tenantId, filterSettingId, true);
  });
});
