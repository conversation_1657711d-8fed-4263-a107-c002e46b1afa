import { Body, Controller, Get, HttpCode, HttpStatus, Param, Patch } from '@nestjs/common';
import { FindAllWorkspaceFilterSettingsUseCase } from '../../application/use-cases/find-all-workspace-filter-settings.use-case';
import { KONQUEST_ADMIN_ROLES, Roles, Serialize, TenantService } from '@keeps-node-apis/@core';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WorkspacesFilterSettingsResponseDto } from '../dtos/workspaces-filter-settings.response.dto';
import { ToggleWorkspaceFilterSettingDto } from '../dtos/toggle-workspace-filter-setting.dto';
import { ToggleStatusWorkspaceFilterSettingUseCase } from '../../application/use-cases/toggle-status-workspace-filter-setting-use-case.service';

@ApiTags('Workspace Filter Settings')
@Controller('workspaces-filter-settings')
@Roles(KONQUEST_ADMIN_ROLES)
export class WorkspacesFilterSettingsController {
  constructor(
    private readonly tenantService: TenantService,
    private readonly findAllWorkspaceFilterSettingsUseCase: FindAllWorkspaceFilterSettingsUseCase,
    private readonly toggleStatusWorkspaceFilterSettingUseCase: ToggleStatusWorkspaceFilterSettingUseCase,
  ) {}

  @Get()
  @Serialize(WorkspacesFilterSettingsResponseDto)
  @ApiOperation({ summary: 'Fetch the current workspace filter settings' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The list with the current workspace filter settings',
    type: WorkspacesFilterSettingsResponseDto,
  })
  findAllFilterSettings() {
    const tenantId = this.tenantService.getTenantId();
    return this.findAllWorkspaceFilterSettingsUseCase.execute(tenantId);
  }

  @Patch(':id/toggle-status')
  @Serialize(WorkspacesFilterSettingsResponseDto)
  @ApiOperation({ summary: 'Toggle a workspace filter setting enabled status' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'The filter enabled status was successfully updated',
    type: WorkspacesFilterSettingsResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'When the provided filter setting could not be found',
  })
  toggleStatus(@Param('id') id: string, @Body() toggleDto: ToggleWorkspaceFilterSettingDto) {
    const tenantId = this.tenantService.getTenantId();
    return this.toggleStatusWorkspaceFilterSettingUseCase.execute(tenantId, id, toggleDto.isEnabled);
  }
}
