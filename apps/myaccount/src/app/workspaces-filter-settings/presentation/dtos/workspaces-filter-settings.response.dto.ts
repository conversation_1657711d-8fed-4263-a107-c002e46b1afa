import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { WorkspaceFilterSettingType } from '../../domain/value-objects/workspace-filter-setting-type';
import { WorkspaceFilterSettings } from '../../../entities/workspace-filter-settings.entity';

export class WorkspacesFilterSettingsResponseDto {
  static fromEntity(entity: WorkspaceFilterSettings, statusCode: string) {
    return { id: entity.id, isEnabled: entity.isEnabled, filterType: statusCode as WorkspaceFilterSettingType };
  }

  @ApiProperty({ example: '88a876e1-5845-40a9-84ed-0fdabfa73271' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'The status code of the related filter', enum: WorkspaceFilterSettingType })
  @Expose()
  filterType: WorkspaceFilterSettingType;

  @ApiProperty({ description: 'Whether the filter is enabled or not', example: true })
  @Expose()
  isEnabled: boolean;
}
