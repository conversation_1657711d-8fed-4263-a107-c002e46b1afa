import { WorkspacesFilterSettingsRepository } from '../domain/repositories/workspaces-filter-settings.repository';
import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { WorkspaceFilterSettings } from '../../entities/workspace-filter-settings.entity';
import { WorkspacesFilterSettingsResponseDto } from '../presentation/dtos/workspaces-filter-settings.response.dto';

@Injectable()
export class WorkspacesFilterSettingsTypeOrmRepository implements WorkspacesFilterSettingsRepository {
  private readonly repository: Repository<WorkspaceFilterSettings>;

  constructor(readonly dataSource: DataSource) {
    this.repository = dataSource.getRepository(WorkspaceFilterSettings);
  }

  async findAll(workspaceId: string): Promise<WorkspacesFilterSettingsResponseDto[]> {
    const entities = await this.repository
      .createQueryBuilder('workspace_filter_settings')
      .leftJoinAndSelect('workspace_filter_settings.enrollmentStatusFilter', 'enrollment_status_filter')
      .where('workspace_filter_settings.workspace_id = :workspaceId', { workspaceId })
      .getMany();

    return entities.map((item) =>
      WorkspacesFilterSettingsResponseDto.fromEntity(item, item.enrollmentStatusFilter.statusCode),
    );
  }

  async toggleStatus(workspaceId: string, workspaceFilterSettingId: string, isEnabled: boolean): Promise<void> {
    const entity = await this.repository.findOneByOrFail({ id: workspaceFilterSettingId, workspaceId });
    entity.isEnabled = isEnabled;
    await this.repository.save(entity);
  }
}
