import { Injectable } from '@nestjs/common';
import { WorkspacesFilterSettingsRepository } from '../../domain/repositories/workspaces-filter-settings.repository';

@Injectable()
export class FindAllWorkspaceFilterSettingsUseCase {
  constructor(private readonly workspacesFilterSettingsRepository: WorkspacesFilterSettingsRepository) {}

  async execute(workspaceId: string) {
    return this.workspacesFilterSettingsRepository.findAll(workspaceId);
  }
}
