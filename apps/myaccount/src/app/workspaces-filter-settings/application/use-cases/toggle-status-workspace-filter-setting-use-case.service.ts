import { Injectable } from '@nestjs/common';
import { WorkspacesFilterSettingsRepository } from '../../domain/repositories/workspaces-filter-settings.repository';

@Injectable()
export class ToggleStatusWorkspaceFilterSettingUseCase {
  constructor(private readonly workspacesFilterSettingsRepository: WorkspacesFilterSettingsRepository) {}

  async execute(workspaceId: string, workspaceFilterSettingId: string, isEnabled: boolean) {
    return this.workspacesFilterSettingsRepository.toggleStatus(workspaceId, workspaceFilterSettingId, isEnabled);
  }
}
