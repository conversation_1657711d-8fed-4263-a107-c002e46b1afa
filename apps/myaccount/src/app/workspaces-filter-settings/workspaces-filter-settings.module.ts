import { Module } from '@nestjs/common';
import { WorkspacesFilterSettingsController } from './presentation/controllers/workspaces-filter-settings.controller';
import { EXPORTED_PROVIDERS, WORKSPACE_FILTER_SETTINGS_PROVIDERS } from './workspace-filter-settings.providers';

@Module({
  controllers: [WorkspacesFilterSettingsController],
  providers: WORKSPACE_FILTER_SETTINGS_PROVIDERS,
  exports: EXPORTED_PROVIDERS,
})
export class WorkspacesFilterSettingsModule {}
