import { plainToInstance } from 'class-transformer';
import { WorkspaceBannerModeResponseDto } from './workspace-banner-mode-response.dto';
import { BannerMode } from '../types/banner-mode.enum';

describe('WorkspaceBannerModeResponseDto', () => {
  describe('transformation', () => {
    it('should transform plain object to WorkspaceBannerModeResponseDto with MANUAL banner mode', () => {
      const plainObject = {
        bannerMode: BannerMode.MANUAL,
      };

      const dto = plainToInstance(WorkspaceBannerModeResponseDto, plainObject);

      expect(dto).toBeInstanceOf(WorkspaceBannerModeResponseDto);
      expect(dto.bannerMode).toBe(BannerMode.MANUAL);
    });

    it('should transform plain object to WorkspaceBannerModeResponseDto with RECOMMENDATION banner mode', () => {
      const plainObject = {
        bannerMode: BannerMode.RECOMMENDATION,
      };

      const dto = plainToInstance(WorkspaceBannerModeResponseDto, plainObject);

      expect(dto).toBeInstanceOf(WorkspaceBannerModeResponseDto);
      expect(dto.bannerMode).toBe(BannerMode.RECOMMENDATION);
    });

    it('should expose bannerMode property', () => {
      const plainObject = {
        bannerMode: BannerMode.MANUAL,
        extraProperty: 'should not be included',
      };

      const dto = plainToInstance(WorkspaceBannerModeResponseDto, plainObject, {
        excludeExtraneousValues: true,
      });

      expect(dto.bannerMode).toBe(BannerMode.MANUAL);
      expect((dto as any).extraProperty).toBeUndefined();
    });

    it('should handle undefined bannerMode', () => {
      const plainObject = {};

      const dto = plainToInstance(WorkspaceBannerModeResponseDto, plainObject);

      expect(dto).toBeInstanceOf(WorkspaceBannerModeResponseDto);
      expect(dto.bannerMode).toBeUndefined();
    });

    it('should handle null bannerMode', () => {
      const plainObject = {
        bannerMode: null,
      };

      const dto = plainToInstance(WorkspaceBannerModeResponseDto, plainObject);

      expect(dto).toBeInstanceOf(WorkspaceBannerModeResponseDto);
      expect(dto.bannerMode).toBeNull();
    });
  });
});
