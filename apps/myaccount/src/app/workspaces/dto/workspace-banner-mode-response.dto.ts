import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BannerMode } from '../types/banner-mode.enum';

/**
 * Data Transfer Object for workspace banner mode response.
 */
export class WorkspaceBannerModeResponseDto {
  @ApiProperty({
    enum: BannerMode,
    description: 'The banner mode for the workspace',
    example: BannerMode.RECOMMENDATION,
  })
  @Expose()
  bannerMode: BannerMode;
}
