import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { BannerMode } from '../types/banner-mode.enum';

/**
 * Data Transfer Object representing a full workspace entity.
 */
export class WorkspaceDto {
  @ApiProperty({ type: <PERSON><PERSON><PERSON>, description: 'Indicates if the workspace uses a dark theme' })
  @Expose()
  themeDark: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if the workspace uses its own SMTP server' })
  @Expose()
  useOwnSmtp: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if email notifications are enabled' })
  @Expose()
  enableEmailNotifications: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if SMTP rejects unauthorized connections' })
  @Expose()
  smtpRejectUnauthorized: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if public categories can be listed' })
  @Expose()
  allowListPublicCategories: boolean;

  @ApiProperty({ type: <PERSON>olean, description: 'Indicates if public channels can be listed' })
  @Expose()
  allowListPublicChannel: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if public channels can be created' })
  @Expose()
  allowCreatePublicChannel: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if paid channels can be listed' })
  @Expose()
  allowListPaidChannel: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if paid channels can be created' })
  @Expose()
  allowCreatePaidChannel: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if channel creation requires approval' })
  @Expose()
  needApproveChannel: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if public missions can be listed' })
  @Expose()
  allowListPublicMission: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if public missions can be created' })
  @Expose()
  allowCreatePublicMission: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if paid missions can be listed' })
  @Expose()
  allowListPaidMission: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if paid missions can be created' })
  @Expose()
  allowCreatePaidMission: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if mission creation requires approval' })
  @Expose()
  needApproveMission: boolean;

  @ApiProperty({ type: Number, description: 'Minimum performance required for a certificate' })
  @Expose()
  minPerformanceCertificate: number;

  @ApiProperty({ type: String, description: 'The unique identifier of the workspace' })
  @Expose()
  id: string;

  @ApiProperty({ type: String, description: 'The creation date of the workspace' })
  @Expose()
  createdDate: string;

  @ApiProperty({ type: String, description: 'The last update date of the workspace' })
  @Expose()
  updatedDate: string;

  @ApiProperty({ type: String, description: 'The name of the workspace' })
  @Expose()
  name: string;

  @ApiProperty({ type: String, description: 'The DUNS number of the workspace' })
  @Expose()
  dunsNumber: string;

  @ApiProperty({ type: String, description: 'The document number of the workspace' })
  @Expose()
  docNumber: string;

  @ApiProperty({ type: String, description: 'A description of the workspace' })
  @Expose()
  description: string;

  @ApiProperty({ type: Boolean, description: 'The status of the workspace (active/inactive)' })
  @Expose()
  status: boolean;

  @ApiProperty({ type: String, description: 'The address of the workspace' })
  @Expose()
  address: string;

  @ApiProperty({ type: String, description: 'The city of the workspace' })
  @Expose()
  city: string;

  @ApiProperty({ type: String, description: 'The state of the workspace' })
  @Expose()
  state: string;

  @ApiProperty({ type: String, description: 'The postal code of the workspace' })
  @Expose()
  postCode: string;

  @ApiProperty({ type: String, description: 'The country of the workspace' })
  @Expose()
  country: string;

  @ApiProperty({ type: String, description: 'The URL of the workspace icon' })
  @Expose()
  iconUrl: string;

  @ApiProperty({ type: String, description: 'The URL of the workspace SVG icon' })
  @Expose()
  iconSvgUrl: string;

  @ApiProperty({ type: String, description: 'The URL of the workspace logo' })
  @Expose()
  logoUrl: string;

  @ApiProperty({ type: String, description: 'The ID of the workspace theme' })
  @Expose()
  themeId: string;

  @ApiProperty({ type: String, description: 'The SMTP sender email for the workspace' })
  @Expose()
  smtpSenderEmail: string;

  @ApiProperty({ type: String, description: 'The ID of the company associated with the workspace' })
  @Expose()
  companyId: string;

  @ApiProperty({ type: String, description: 'The logout URL for the workspace' })
  @Expose()
  logoutUrl: string;

  @ApiProperty({ type: String, description: 'The custom color for the workspace' })
  @Expose()
  customColor: string;

  @ApiProperty({ type: String })
  @Expose()
  hashId: string;

  @ApiProperty({ type: Number })
  @Expose()
  enrollmentGoalDurationDays: number;

  @ApiProperty({ type: Boolean })
  @Expose()
  blockReenrollment: boolean;

  @ApiProperty({ enum: BannerMode, description: 'The banner mode for the workspace' })
  @Expose()
  bannerMode: BannerMode;
}
