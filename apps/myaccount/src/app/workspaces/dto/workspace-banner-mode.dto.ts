import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty } from 'class-validator';
import { BannerMode } from '../types/banner-mode.enum';

/**
 * Data Transfer Object for updating a workspace's banner mode.
 */
export class WorkspaceBannerModeDto {
  @ApiProperty({
    enum: BannerMode,
    description: 'The banner mode for the workspace',
    example: 'RECOMMENDATION',
    required: true,
  })
  @IsEnum(BannerMode)
  @IsNotEmpty()
  bannerMode: BannerMode;
}
