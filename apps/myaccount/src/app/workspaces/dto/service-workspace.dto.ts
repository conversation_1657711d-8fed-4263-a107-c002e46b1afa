import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { WorkspaceDto } from '../../workspaces/dto/workspace.dto';
import { ServiceDto } from '../../applications/dtos/service.dto';

export class ServiceWorkspaceDto {
  @ApiProperty({ description: 'Unique identifier', example: '123e4567-e89b-12d3-a456-426614174000' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'Status of the service workspace', example: true })
  @Expose()
  status: boolean;

  @ApiProperty({ description: 'Workspace identifier', example: '123e4567-e89b-12d3-a456-426614174000' })
  @Expose()
  workspaceId: string;

  @ApiProperty({ description: 'Service identifier', example: '123e4567-e89b-12d3-a456-426614174000' })
  @Expose()
  serviceId: string;

  @ApiProperty({ description: 'Custom URL for the service', example: 'https://custom.service.com', required: false })
  @Expose()
  customUrl: string | null;

  @ApiProperty({ type: () => ServiceDto })
  @Expose()
  @Type(() => ServiceDto)
  service: ServiceDto;

  @ApiProperty({ type: () => WorkspaceDto })
  @Expose()
  @Type(() => WorkspaceDto)
  workspace: WorkspaceDto;

  @ApiProperty({ type: Date })
  @Expose()
  createdDate: Date;

  @ApiProperty({ type: Date })
  @Expose()
  updatedDate: Date;
}
