import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsEnum, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { BannerMode } from '../types/banner-mode.enum';
/**
 * Data Transfer Object for creating a new workspace.
 */
export class CreateWorkspaceDto {
  @ApiProperty({ type: String, description: 'The name of the workspace', required: true })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ type: String, description: 'The DUNS number of the workspace', required: false })
  @IsString()
  @IsOptional()
  dunsNumber?: string;

  @ApiProperty({ type: String, description: 'The document number of the workspace', required: false })
  @IsString()
  @IsOptional()
  docNumber?: string;

  @ApiProperty({ type: String, description: 'A description of the workspace', required: false })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ type: <PERSON>olean, description: 'The status of the workspace (active/inactive)', required: false })
  @IsBoolean()
  @IsOptional()
  status = true;

  @ApiProperty({ type: String, description: 'The address of the workspace', required: false })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({ type: String, description: 'The city of the workspace', required: false })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({ type: String, description: 'The state of the workspace', required: false })
  @IsString()
  @IsOptional()
  state?: string;

  @ApiProperty({ type: String, description: 'The postal code of the workspace', required: false })
  @IsString()
  @IsOptional()
  postCode?: string;

  @ApiProperty({ type: String, description: 'The country of the workspace', required: false })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({ type: String, description: 'The URL of the workspace icon', required: false })
  @IsString()
  @IsOptional()
  iconUrl?: string;

  @ApiProperty({ type: String, description: 'The URL of the workspace SVG icon', required: false })
  @IsString()
  @IsOptional()
  iconSvgUrl?: string;

  @ApiProperty({ type: String, description: 'The URL of the workspace logo', required: false })
  @IsString()
  @IsOptional()
  logoUrl?: string;

  @ApiProperty({ type: String, description: 'The ID of the workspace theme', required: false })
  @IsString()
  @IsOptional()
  themeId?: string;

  @ApiProperty({ type: Boolean, description: 'Indicates if the workspace uses a dark theme', required: false })
  @IsBoolean()
  @IsOptional()
  themeDark: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if the workspace uses its own SMTP server', required: false })
  @IsBoolean()
  @IsOptional()
  useOwnSmtp: boolean;

  @ApiProperty({ type: Boolean, description: 'Indicates if email notifications are enabled', required: false })
  @IsBoolean()
  @IsOptional()
  enableEmailNotifications: boolean;

  @ApiProperty({ type: String, description: 'The SMTP host for the workspace', required: false })
  @IsString()
  @IsOptional()
  smtpHost?: string;

  @ApiProperty({ type: Number, description: 'The SMTP port for the workspace', required: false })
  @IsNotEmpty()
  @IsOptional()
  smtpPort?: number;

  @ApiProperty({ type: Boolean, description: 'Indicates if SMTP uses a secure connection', required: false })
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  smtpSecure?: boolean;

  @ApiProperty({ type: String, description: 'The SMTP authentication username', required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  smtpAuthUser?: string;

  @ApiProperty({ type: String, description: 'The SMTP authentication password', required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  smtpAuthPass?: string;

  @ApiProperty({ type: String, description: 'The SMTP sender email', required: false })
  @IsString()
  @IsNotEmpty()
  @IsOptional()
  smtpSenderEmail?: string;

  @ApiProperty({ type: Boolean, description: 'Indicates if SMTP rejects unauthorized connections', required: false })
  @IsBoolean()
  @IsNotEmpty()
  @IsOptional()
  smtpRejectUnauthorized?: boolean;

  @ApiProperty({ type: String, description: 'The ID of the company associated with the workspace', required: false })
  @IsUUID()
  @IsNotEmpty()
  @IsOptional()
  companyId: string;

  @ApiProperty({ type: String, description: 'The logout URL for the workspace', required: false })
  @IsString()
  @IsOptional()
  logoutUrl?: string;

  @ApiProperty({ type: String, description: 'The custom color for the workspace', required: false })
  @IsString()
  @IsOptional()
  customColor?: string;

  @ApiProperty({ type: String, description: 'The hash ID for the workspace', required: false })
  @IsString()
  @IsOptional()
  hashId?: string;

  @IsNumber()
  @IsOptional()
  enrollmentGoalDurationDays?: number;

  @IsNumber()
  @IsOptional()
  minPerformanceCertificate?: number;

  @IsBoolean()
  @IsOptional()
  blockReenrollment?: boolean;

  @ApiProperty({
    enum: BannerMode,
    description: 'The banner mode for the workspace',
    required: false,
    default: BannerMode.RECOMMENDATION,
  })
  @IsEnum(BannerMode)
  @IsOptional()
  bannerMode?: BannerMode;
}
