import { Test, TestingModule } from '@nestjs/testing';
import { WorkspacesController } from './workspaces.controller';
import { WorkspacesService } from '../services/workspaces.service';
import { UsersService } from '../../users/services/users.service';
import { AuthUser } from '@keeps-node-apis/@core';
import { ImageEditorService } from '../../utils/image.service';
import { S3Uploader } from '@keeps-node-apis/@core';
import { BadRequestException, HttpStatus } from '@nestjs/common';
import 'multer';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { WorkspaceBannerModeDto } from '../dto/workspace-banner-mode.dto';
import { WorkspaceBannerModeResponseDto } from '../dto/workspace-banner-mode-response.dto';
import { BannerMode } from '../types/banner-mode.enum';
import { WorkspaceDto } from '../dto/workspace.dto';

describe('WorkspacesController', () => {
  let controller: WorkspacesController;
  let workspacesService: WorkspacesService;
  let imageEditorService: ImageEditorService;
  let s3Uploader: S3Uploader;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WorkspacesController],
      providers: [
        {
          provide: WorkspacesService,
          useValue: {
            create: jest.fn(),
            getBannerMode: jest.fn(),
            updateBannerMode: jest.fn(),
          },
        },
        {
          provide: UsersService,
          useValue: {},
        },
        {
          provide: ImageEditorService,
          useValue: {
            handleFileUpload: jest.fn(),
          },
        },
        {
          provide: S3Uploader,
          useValue: {
            uploadFilePath: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<WorkspacesController>(WorkspacesController);
    workspacesService = module.get<WorkspacesService>(WorkspacesService);
    imageEditorService = module.get<ImageEditorService>(ImageEditorService);
    s3Uploader = module.get<S3Uploader>(S3Uploader);
  });

  describe('uploadLogo', () => {
    it('should upload a logo and return success response', async () => {
      const mockFile = {
        originalname: 'logo.png',
        buffer: Buffer.from('mock-file-buffer'),
        mimetype: 'image/png',
      } as Express.Multer.File;

      const uploadResponse = {
        name: 'workspace-logo/mock-logo.png',
        url: 'https://cdn.example.com/workspace-logo/mock-logo.png',
      };

      jest.spyOn(imageEditorService, 'handleFileUpload').mockResolvedValue(uploadResponse);

      const response = await controller.uploadLogo(mockFile);

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.data).toEqual(uploadResponse);
    });

    it('should throw error if the file type is unsupported', async () => {
      const mockFile = {
        originalname: 'logo.txt',
        buffer: Buffer.from('mock-file-buffer'),
        mimetype: 'text/plain',
      } as Express.Multer.File;

      jest
        .spyOn(imageEditorService, 'handleFileUpload')
        .mockRejectedValue(new BadRequestException('Unsupported image type'));

      try {
        await controller.uploadLogo(mockFile);
      } catch (error) {
        expect(error.response.message).toBe('Unsupported image type');
      }
    });
  });

  describe('uploadIcon', () => {
    it('should upload an icon and return success response', async () => {
      const mockFile = {
        originalname: 'icon.png',
        buffer: Buffer.from('mock-file-buffer'),
        mimetype: 'image/png',
      } as Express.Multer.File;

      const uploadResponse = {
        name: 'workspace-icon/mock-icon.png',
        url: 'https://cdn.example.com/workspace-icon/mock-icon.png',
      };

      jest.spyOn(imageEditorService, 'handleFileUpload').mockResolvedValue(uploadResponse);

      const response = await controller.uploadIcon(mockFile);

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.data).toEqual(uploadResponse);
    });

    it('should throw error if the file type is unsupported', async () => {
      const mockFile = {
        originalname: 'icon.txt',
        buffer: Buffer.from('mock-file-buffer'),
        mimetype: 'text/plain',
      } as Express.Multer.File;

      jest
        .spyOn(imageEditorService, 'handleFileUpload')
        .mockRejectedValue(new BadRequestException('Unsupported image type'));

      try {
        await controller.uploadIcon(mockFile);
      } catch (error) {
        expect(error.response.message).toBe('Unsupported image type');
      }
    });
  });

  describe('uploadIconSvg', () => {
    it('should upload an SVG icon and return success response', async () => {
      const mockFile = {
        originalname: 'icon.svg',
        buffer: Buffer.from('mock-file-buffer'),
        mimetype: 'image/svg+xml',
      } as Express.Multer.File;

      const uploadResponse = {
        name: 'workspace-svg/mock-icon.svg',
        url: 'https://cdn.example.com/workspace-svg/mock-icon.svg',
      };

      const mockCreateWorkspaceDto: Partial<CreateWorkspaceDto> = {
        name: 'Test Workspace',
      };

      const result = await controller.create(mockCreateWorkspaceDto as any, { sub: '123456' } as AuthUser);
      jest.spyOn(imageEditorService, 'handleFileUpload').mockResolvedValue(uploadResponse);

      const response = await controller.uploadIconSvg(mockFile);

      expect(response.statusCode).toBe(HttpStatus.OK);
      expect(response.data).toEqual(uploadResponse);
    });

    it('should throw error if the file type is unsupported', async () => {
      const mockFile = {
        originalname: 'icon.txt',
        buffer: Buffer.from('mock-file-buffer'),
        mimetype: 'text/plain',
      } as Express.Multer.File;

      jest
        .spyOn(imageEditorService, 'handleFileUpload')
        .mockRejectedValue(new BadRequestException('Unsupported image type'));

      try {
        await controller.uploadIconSvg(mockFile);
      } catch (error) {
        expect(error.response.message).toBe('Unsupported image type');
      }
    });
  });

  describe('getBannerMode', () => {
    it('should get banner mode successfully', async () => {
      const workspaceId = 'workspace-id';
      const expectedResponse: WorkspaceBannerModeResponseDto = {
        bannerMode: BannerMode.MANUAL,
      };

      jest.spyOn(workspacesService, 'getBannerMode').mockResolvedValue(expectedResponse);

      const result = await controller.getBannerMode(workspaceId);

      expect(workspacesService.getBannerMode).toHaveBeenCalledWith(workspaceId);
      expect(result).toEqual(expectedResponse);
    });

    it('should get banner mode with RECOMMENDATION value', async () => {
      const workspaceId = 'workspace-id';
      const expectedResponse: WorkspaceBannerModeResponseDto = {
        bannerMode: BannerMode.RECOMMENDATION,
      };

      jest.spyOn(workspacesService, 'getBannerMode').mockResolvedValue(expectedResponse);

      const result = await controller.getBannerMode(workspaceId);

      expect(workspacesService.getBannerMode).toHaveBeenCalledWith(workspaceId);
      expect(result).toEqual(expectedResponse);
    });

    it('should propagate service errors', async () => {
      const workspaceId = 'non-existent-id';
      const error = new Error('Workspace not found');

      jest.spyOn(workspacesService, 'getBannerMode').mockRejectedValue(error);

      await expect(controller.getBannerMode(workspaceId)).rejects.toThrow('Workspace not found');
      expect(workspacesService.getBannerMode).toHaveBeenCalledWith(workspaceId);
    });
  });

  describe('updateBannerMode', () => {
    it('should update banner mode successfully', async () => {
      const workspaceId = 'workspace-id';
      const bannerModeDto: WorkspaceBannerModeDto = {
        bannerMode: BannerMode.MANUAL,
      };

      jest.spyOn(workspacesService, 'updateBannerMode').mockResolvedValue(undefined);

      const result = await controller.updateBannerMode(workspaceId, bannerModeDto);

      expect(workspacesService.updateBannerMode).toHaveBeenCalledWith(workspaceId, bannerModeDto);
      expect(result).toBeUndefined();
    });

    it('should update banner mode to RECOMMENDATION', async () => {
      const workspaceId = 'workspace-id';
      const bannerModeDto: WorkspaceBannerModeDto = {
        bannerMode: BannerMode.RECOMMENDATION,
      };

      jest.spyOn(workspacesService, 'updateBannerMode').mockResolvedValue(undefined);

      const result = await controller.updateBannerMode(workspaceId, bannerModeDto);

      expect(workspacesService.updateBannerMode).toHaveBeenCalledWith(workspaceId, bannerModeDto);
      expect(result).toBeUndefined();
    });

    it('should propagate service errors', async () => {
      const workspaceId = 'workspace-id';
      const bannerModeDto: WorkspaceBannerModeDto = {
        bannerMode: BannerMode.MANUAL,
      };

      const error = new Error('Service error');
      jest.spyOn(workspacesService, 'updateBannerMode').mockRejectedValue(error);

      await expect(controller.updateBannerMode(workspaceId, bannerModeDto)).rejects.toThrow('Service error');
      expect(workspacesService.updateBannerMode).toHaveBeenCalledWith(workspaceId, bannerModeDto);
    });
  });
});
