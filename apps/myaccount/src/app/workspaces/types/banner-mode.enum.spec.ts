import { BannerMode } from './banner-mode.enum';

describe('BannerMode', () => {
  it('should have MANUAL value', () => {
    expect(BannerMode.MANUAL).toBe('MANUAL');
  });

  it('should have RECOMMENDATION value', () => {
    expect(BannerMode.RECOMMENDATION).toBe('RECOMMENDATION');
  });

  it('should have exactly 2 enum values', () => {
    const enumValues = Object.values(BannerMode);
    expect(enumValues).toHaveLength(2);
    expect(enumValues).toContain('MANUAL');
    expect(enumValues).toContain('RECOMMENDATION');
  });

  it('should have string values for all enum members', () => {
    Object.values(BannerMode).forEach((value) => {
      expect(typeof value).toBe('string');
    });
  });

  it('should be able to check enum membership', () => {
    expect(Object.values(BannerMode)).toContain(BannerMode.MANUAL);
    expect(Object.values(BannerMode)).toContain(BannerMode.RECOMMENDATION);
    expect(Object.values(BannerMode)).not.toContain('INVALID_MODE');
  });
});
