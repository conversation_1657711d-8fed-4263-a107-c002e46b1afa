import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Not, Repository } from 'typeorm';
import { CreateWorkspaceDto } from '../dto/create-workspace.dto';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { Workspace } from '../../entities/workspace.entity';
import { CompaniesService } from '../../companies/companies.service';
import { Service } from '../../entities/service.entity';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';
import IWorkspacesRepository from '../repositories/workspaces-repository.interface';
import { plainToInstance } from 'class-transformer';
import { WorkspaceListDto } from '../dto/workspace-list.dto';
import { WorkspaceDto } from '../dto/workspace.dto';
import { Company } from '../../entities/company.entity';
import slugify from 'slugify';
import { UserRolesService } from '../../users/services/user-roles.service';
import { WorkspaceThemeIdDto } from '../dto/workspace-theme-id.dto';
import { WorkspaceCustomLoginUrlDto } from '../dto/workspace-custom-login-url.dto';
import { CryptoService, KpCacheService } from '@keeps-node-apis/@core';
import { CompanyBillingPlan } from '../../entities/company-billing-plan.entity';
import { WorkspaceBannerModeDto } from '../dto/workspace-banner-mode.dto';
import { WorkspaceBannerModeResponseDto } from '../dto/workspace-banner-mode-response.dto';

const WORKSPACE_HASH_TTL_30_MIN = 30 * 60 * 1000;

/**
 * Service for managing workspace-related operations.
 */
@Injectable()
export class WorkspacesService {
  constructor(
    @InjectRepository(Workspace)
    private workspaceRepository: IWorkspacesRepository,
    @InjectRepository(Service) private serviceRepository: Repository<Service>,
    @InjectRepository(ServiceWorkspace)
    private ServiceWorkspaceRepository: Repository<ServiceWorkspace>,
    @InjectRepository(Company)
    private readonly companyRepository: Repository<Company>,
    @InjectRepository(CompanyBillingPlan)
    private readonly companyBillingPlanRepository: Repository<CompanyBillingPlan>,
    private readonly companiesService: CompaniesService,
    private readonly cryptoService: CryptoService,
    private readonly userRolesService: UserRolesService,
    private readonly cache: KpCacheService,
  ) {}

  /**
   * Creates a default company for a workspace if no company ID is provided.
   *
   * @param {string} workspaceName - The name of the workspace.
   * @returns {Promise<Company>} The created company entity.
   */
  async createDefaultCompany(workspaceName: string): Promise<Company> {
    const company = this.companyRepository.create({ name: workspaceName, status: true });
    return await this.companyRepository.save(company);
  }

  /**
   * Generates a random hash for a workspace.
   *
   * @returns {Promise<string>} A random hash string.
   */
  async generateWorkspaceHash(): Promise<string> {
    return Math.random().toString(36).substring(2, 10);
  }

  /**
   * Creates a new workspace and assigns the creating user as an admin.
   *
   * @param {CreateWorkspaceDto} createWorkspaceDto - The data for the new workspace.
   * @param {string} userId - The ID of the user creating the workspace.
   * @returns {Promise<WorkspaceDto>} The created workspace.
   */
  async create(createWorkspaceDto: CreateWorkspaceDto, userId: string): Promise<WorkspaceDto> {
    if (!createWorkspaceDto.companyId) {
      const createdCompany = await this.createDefaultCompany(createWorkspaceDto.name);
      createWorkspaceDto.companyId = createdCompany.id;
    }

    const hashFromDto = createWorkspaceDto.hashId ? createWorkspaceDto.hashId : null;
    createWorkspaceDto.hashId = await this.generateWorkspaceHash();

    const workspace = this.workspaceRepository.create({
      aluraIntegrationActive: false,
      blockReenrollment: false,
      ...createWorkspaceDto,
    });

    if (hashFromDto) {
      workspace.hashId = await this.formatWorkspaceHashId(hashFromDto, workspace);
    }

    workspace.smtpAuthPass = createWorkspaceDto.smtpAuthPass
      ? this.cryptoService.encrypt(createWorkspaceDto.smtpAuthPass)
      : null;

    if (!workspace.companyId) {
      const createdCompany = await this.companiesService.create({
        name: createWorkspaceDto.name,
        description: null,
      });
      workspace.companyId = createdCompany.id;
    }
    const createdWorkspace = await this.workspaceRepository.save(workspace);
    await this.userRolesService.createAdminUser(userId, createdWorkspace.id);
    return plainToInstance(WorkspaceDto, createdWorkspace);
  }

  /**
   * Retrieves all workspaces a user is allowed to access.
   *
   * @param {string} userId - The ID of the user.
   * @returns {Promise<WorkspaceListDto[]>} A list of accessible workspaces.
   */
  async findAllowed(userId: string): Promise<WorkspaceListDto[]> {
    return this.workspaceRepository.findAllUserWorkspaces(userId);
  }

  /**
   * Retrieves a single workspace by its ID.
   *
   * @param {string} id - The ID of the workspace.
   * @returns {Promise<WorkspaceDto>} The workspace details.
   * @throws {NotFoundException} If the workspace is not found.
   */
  findOne(id: string) {
    return this.workspaceRepository.findOneByOrFail({ id });
  }

  /**
   * Updates a workspace with the provided data.
   *
   * @param {string} id - The ID of the workspace.
   * @param {UpdateWorkspaceDto} updateWorkspaceDto - The updated workspace data.
   * @returns {Promise<WorkspaceDto>} The updated workspace.
   * @throws {NotFoundException} If the workspace is not found.
   */
  async update(id: string, updateWorkspaceDto: UpdateWorkspaceDto): Promise<WorkspaceDto> {
    updateWorkspaceDto.smtpAuthPass = updateWorkspaceDto.smtpAuthPass
      ? this.cryptoService.encrypt(updateWorkspaceDto.smtpAuthPass)
      : null;
    await this.workspaceRepository.update(id, { ...updateWorkspaceDto });
    const workspace = await this.workspaceRepository.findOneByOrFail({ id });
    return plainToInstance(WorkspaceDto, workspace);
  }

  /**
   * Deletes a workspace by its ID.
   *
   * @param {string} id - The ID of the workspace.
   * @returns {Promise<void>} Nothing is returned on success.
   */
  remove(id: string) {
    return this.workspaceRepository.delete({ id });
  }

  /**
   * Adds a service to a workspace and creates a billing plan if needed.
   * If the service is already associated with the workspace, returns the existing association.
   * If the service was previously soft-deleted, it will be reactivated.
   *
   * @param {string} workspaceId - The ID of the workspace.
   * @param {string} serviceId - The ID of the service.
   * @returns {Promise<ServiceWorkspace>} The service-workspace association.
   * @throws {NotFoundException} If the service or workspace is not found.
   */
  async addServiceInWorkspace(workspaceId: string, serviceId: string): Promise<ServiceWorkspace> {
    const service = await this.serviceRepository.findOneByOrFail({ id: serviceId });
    const workspace = await this.workspaceRepository.findOneByOrFail({ id: workspaceId });

    const existingServiceWorkspace = await this.ServiceWorkspaceRepository.findOne({
      where: { serviceId, workspaceId },
    });

    if (existingServiceWorkspace) {
      if (existingServiceWorkspace.status === false) {
        existingServiceWorkspace.status = true;
        return await this.ServiceWorkspaceRepository.save(existingServiceWorkspace);
      }
      return existingServiceWorkspace;
    }

    const serviceWorkspace = this.ServiceWorkspaceRepository.create({
      serviceId: serviceId,
      workspaceId: workspaceId,
      status: true,
    });
    const savedServiceWorkspace = await this.ServiceWorkspaceRepository.save(serviceWorkspace);
    await this.createBillingCompanyPlan(service.applicationId, workspace.companyId);
    return savedServiceWorkspace;
  }

  /**
   * Formats a workspace hash ID to ensure uniqueness.
   *
   * @param {string} hash - The desired hash ID.
   * @param {Workspace} workspace - The workspace entity.
   * @returns {Promise<string>} A unique hash ID.
   */
  async formatWorkspaceHashId(hash: string, workspace: Workspace): Promise<string> {
    hash = slugify(hash || '', { lower: true }).replace(/[^a-z0-9\\-]+/g, '');
    const existingWorkspaces = await this.workspaceRepository.find({
      where: { hashId: hash, id: workspace.id ? Not(workspace.id) : undefined },
    });

    if (existingWorkspaces.length > 0) {
      let suffix = '-lxp-1';
      let i = 1;
      while (await this.workspaceRepository.findOne({ where: { hashId: workspace.hashId + suffix } })) {
        i += 1;
        suffix = `-lxp-${i}`;
      }
      return workspace.hashId + suffix;
    }
    return hash;
  }

  /**
   * Retrieves the theme ID of a workspace.
   *
   * @param {string} workspaceId - The ID of the workspace.
   * @returns {Promise<WorkspaceThemeIdDto>} The workspace theme ID.
   */
  async findWorkspaceThemeId(workspaceId: string): Promise<WorkspaceThemeIdDto> {
    const workspace = await this.findOne(workspaceId);
    return plainToInstance(WorkspaceThemeIdDto, workspace);
  }

  /**
   * Retrieves a custom login URL by workspace hash ID.
   *
   * @param {string} hashId - The hash ID of the workspace.
   * @returns {Promise<WorkspaceCustomLoginUrlDto>} The custom login URL.
   * @throws {NotFoundException} If the workspace is not found.
   */
  async findLoginUrlByHashId(hashId: string): Promise<WorkspaceCustomLoginUrlDto> {
    const key = ['loginUrl', hashId];
    const cachedDto = await this.cache.get<WorkspaceCustomLoginUrlDto>(key);

    if (cachedDto) {
      return plainToInstance(WorkspaceCustomLoginUrlDto, cachedDto);
    }

    const workspace = await this.workspaceRepository.findOneBy({ hashId });
    if (!workspace) {
      throw new NotFoundException('Workspace not found');
    }

    const dto = plainToInstance(WorkspaceCustomLoginUrlDto, workspace);
    await this.cache.set(key, dto, { ttl: WORKSPACE_HASH_TTL_30_MIN });
    return dto;
  }

  /**
   * Creates or retrieves a billing plan for a company and application.
   *
   * @param {string} applicationId - The ID of the application.
   * @param {string} companyId - The ID of the company.
   * @returns {Promise<CompanyBillingPlan>} The billing plan entity.
   */
  async createBillingCompanyPlan(applicationId: string, companyId: string): Promise<CompanyBillingPlan> {
    let billingCompanyPlan = await this.companyBillingPlanRepository.findOne({
      where: { applicationId, companyId },
    });
    if (!billingCompanyPlan) {
      billingCompanyPlan = this.companyBillingPlanRepository.create({
        applicationId,
        companyId,
        currentPlan: 50,
      });
      await this.companyBillingPlanRepository.save(billingCompanyPlan);
    }
    return billingCompanyPlan;
  }

  /**
   * Soft deletes a service from a workspace by setting status to false.
   *
   * @param {string} workspaceId - The ID of the workspace.
   * @param {string} serviceId - The ID of the service to remove.
   * @returns {Promise<void>} Nothing is returned on success.
   * @throws {NotFoundException} If the service-workspace association is not found.
   */
  async removeServiceFromWorkspace(workspaceId: string, serviceId: string): Promise<void> {
    const serviceWorkspace = await this.ServiceWorkspaceRepository.findOneOrFail({
      where: { workspaceId, serviceId },
    });
    serviceWorkspace.status = false;
    await this.ServiceWorkspaceRepository.save(serviceWorkspace);
  }

  /**
   * Retrieves the banner mode for a specific workspace.
   *
   * @param {string} id - The ID of the workspace.
   * @returns {Promise<WorkspaceBannerModeResponseDto>} The workspace banner mode.
   * @throws {NotFoundException} If the workspace is not found.
   */
  async getBannerMode(id: string): Promise<WorkspaceBannerModeResponseDto> {
    const workspace = await this.workspaceRepository.findOneBy({ id });
    if (!workspace) {
      throw new NotFoundException(`Workspace with ID ${id} not found`);
    }
    return plainToInstance(WorkspaceBannerModeResponseDto, { bannerMode: workspace.bannerMode });
  }

  /**
   * Updates the banner mode for a specific workspace.
   *
   * @param {string} id - The ID of the workspace.
   * @param {WorkspaceBannerModeDto} bannerModeDto - The banner mode data.
   * @returns {Promise<void>} Nothing is returned on success.
   * @throws {NotFoundException} If the workspace is not found.
   */
  async updateBannerMode(id: string, bannerModeDto: WorkspaceBannerModeDto): Promise<void> {
    const workspace = await this.workspaceRepository.findOneBy({ id });
    if (!workspace) {
      throw new NotFoundException(`Workspace with ID ${id} not found`);
    }
    workspace.bannerMode = bannerModeDto.bannerMode;
    await this.workspaceRepository.save(workspace);
  }
}
