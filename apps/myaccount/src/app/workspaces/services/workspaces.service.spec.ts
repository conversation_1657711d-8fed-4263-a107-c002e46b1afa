import { Test, TestingModule } from '@nestjs/testing';
import { Not, Repository } from 'typeorm';
import { WorkspacesService } from './workspaces.service';
import { CompaniesService } from '../../companies/companies.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { UpdateWorkspaceDto } from '../dto/update-workspace.dto';
import { Service } from '../../entities/service.entity';
import { Workspace } from '../../entities/workspace.entity';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';
import { Company } from '../../entities/company.entity';
import { UserRolesService } from '../../users/services/user-roles.service';
import { CryptoService, KpCacheService } from '@keeps-node-apis/@core';
import { CompanyBillingPlan } from '../../entities/company-billing-plan.entity';
import { WorkspaceBannerModeDto } from '../dto/workspace-banner-mode.dto';
import { WorkspaceBannerModeResponseDto } from '../dto/workspace-banner-mode-response.dto';
import { BannerMode } from '../types/banner-mode.enum';
import { NotFoundException } from '@nestjs/common';

describe('WorkspacesService', () => {
  let workspacesService: WorkspacesService;
  let mockedWorkspaceRepository: Partial<jest.Mocked<Repository<Workspace>>>;
  let mockedServiceRepository: Partial<jest.Mocked<Repository<Service>>>;
  let mockedWorkspaceServiceRepository: Partial<jest.Mocked<Repository<ServiceWorkspace>>>;
  let mockedCompaniesService: Partial<jest.Mocked<CompaniesService>>;
  let mockedCryptoService: Partial<jest.Mocked<CryptoService>>;
  let mockCacheService: jest.Mocked<KpCacheService>;

  beforeEach(async () => {
    mockCacheService = { get: jest.fn(), set: jest.fn() } as unknown as jest.Mocked<KpCacheService>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspacesService,
        {
          provide: CompaniesService,
          useValue: {
            create: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Workspace),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            find: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Company),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Service),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(ServiceWorkspace),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(CompanyBillingPlan),
          useValue: {
            create: jest.fn(),
            save: jest.fn(),
            findOne: jest.fn(),
          },
        },
        {
          provide: CryptoService,
          useValue: {
            encrypt: jest.fn(),
          },
        },
        {
          provide: CompaniesService,
          useValue: {
            create: jest.fn(),
          },
        },
        {
          provide: UserRolesService,
          useValue: {
            createAdminUser: jest.fn(),
          },
        },
        { provide: KpCacheService, useValue: mockCacheService },
      ],
    }).compile();

    workspacesService = module.get<WorkspacesService>(WorkspacesService);
    mockedWorkspaceRepository = module.get(getRepositoryToken(Workspace));
    mockedCompaniesService = module.get(CompaniesService);
    mockedCryptoService = module.get(CryptoService);
    mockedWorkspaceServiceRepository = module.get(getRepositoryToken(ServiceWorkspace));
    mockedServiceRepository = module.get(getRepositoryToken(Service));
  });
  it('should create a workspace with encrypted smtpAuthPass and return it', async () => {
    const mockCreateWorkspaceDto: any = {
      name: 'Test Workspace',
      smtpAuthPass: 'password',
    };

    const mockSavedWorkspace = {
      id: 'workspace-id',
      ...mockCreateWorkspaceDto,
      smtpAuthPass: 'encrypted-password',
      companyId: 'mock-company-id',
    };

    mockedCryptoService.encrypt = jest.fn().mockReturnValue('encrypted-password');
    mockedWorkspaceRepository.create = jest.fn().mockReturnValue(mockCreateWorkspaceDto);
    mockedWorkspaceRepository.save = jest.fn().mockResolvedValue(mockSavedWorkspace);
    workspacesService.createDefaultCompany = jest.fn().mockResolvedValue({ id: 'mock-company-id' });

    const result = await workspacesService.create(mockCreateWorkspaceDto, 'user-id');

    expect(result).toEqual(mockSavedWorkspace);
    expect(workspacesService.createDefaultCompany).toHaveBeenCalledWith('Test Workspace');
    expect(mockedCryptoService.encrypt).toHaveBeenCalledWith('password');
  });

  it('should create a workspace without encrypted smtpAuthPass and return it', async () => {
    const mockCreateWorkspaceDto: any = {
      name: 'Test Workspace',
    };

    const mockSavedWorkspace = {
      id: 'workspace-id',
      ...mockCreateWorkspaceDto,
      companyId: 'mock-company-id',
    };

    mockedCryptoService.encrypt = jest.fn().mockReturnValue('encrypted-password');
    mockedWorkspaceRepository.create = jest.fn().mockReturnValue(mockCreateWorkspaceDto);
    mockedWorkspaceRepository.save = jest.fn().mockResolvedValue(mockSavedWorkspace);
    workspacesService.createDefaultCompany = jest.fn().mockResolvedValue({ id: 'mock-company-id' });

    const result = await workspacesService.create(mockCreateWorkspaceDto, 'user-id');

    expect(result).toEqual(mockSavedWorkspace);
    expect(workspacesService.createDefaultCompany).toHaveBeenCalledWith('Test Workspace');
    expect(mockedCryptoService.encrypt).not.toHaveBeenCalled();
  });

  it('should update a workspace and return the updated workspace', async () => {
    const mockUpdateWorkspaceDto: UpdateWorkspaceDto = {
      name: 'Updated Workspace',
      smtpAuthPass: 'new-password',
    };
    const mockWorkspaceId = 'workspace-id';
    const mockUpdatedWorkspace: Partial<Workspace> = {
      id: mockWorkspaceId,
      ...mockUpdateWorkspaceDto,
      companyId: 'company-id',
    };
    mockedCryptoService.encrypt = jest.fn().mockReturnValue('encrypted-new-password');
    mockedWorkspaceRepository.update = jest.fn();
    mockedWorkspaceRepository.findOneByOrFail = jest.fn().mockResolvedValue(mockUpdatedWorkspace);

    const result = await workspacesService.update(mockWorkspaceId, mockUpdateWorkspaceDto);

    expect(result).toEqual(mockUpdatedWorkspace);
    expect(mockedCryptoService.encrypt).toHaveBeenCalledWith('new-password');
    expect(mockedWorkspaceRepository.update).toHaveBeenCalledWith(mockWorkspaceId, {
      ...mockUpdateWorkspaceDto,
      smtpAuthPass: 'encrypted-new-password',
    });
    expect(mockedWorkspaceRepository.findOneByOrFail).toHaveBeenCalledWith({
      id: mockWorkspaceId,
    });
  });

  it('should update a workspace without encrypting smtpAuthPass', async () => {
    const mockUpdateWorkspaceDto: UpdateWorkspaceDto = {
      name: 'Updated Workspace',
    };
    const mockWorkspaceId = 'workspace-id';
    const mockUpdatedWorkspace: Partial<Workspace> = {
      id: mockWorkspaceId,
      ...mockUpdateWorkspaceDto,
      companyId: 'company-id',
    };
    mockedWorkspaceRepository.update = jest.fn();
    mockedWorkspaceRepository.findOneByOrFail = jest.fn().mockResolvedValue(mockUpdatedWorkspace);

    const result = await workspacesService.update(mockWorkspaceId, mockUpdateWorkspaceDto);

    expect(result).toEqual(mockUpdatedWorkspace);
    expect(mockedCryptoService.encrypt).not.toHaveBeenCalled();
    expect(mockedWorkspaceRepository.update).toHaveBeenCalledWith(mockWorkspaceId, mockUpdateWorkspaceDto);
    expect(mockedWorkspaceRepository.findOneByOrFail).toHaveBeenCalledWith({
      id: mockWorkspaceId,
    });
  });

  it('should add a service to a workspace and return the created workspace service when it does not exist', async () => {
    const mockWorkspaceId = 'workspace-id';
    const mockServiceId = 'service-id';
    const mockWorkspaceService: Partial<ServiceWorkspace> = {
      id: 'workspace-service-id',
      workspaceId: mockWorkspaceId,
      serviceId: mockServiceId,
      status: true,
    };
    mockedServiceRepository.findOneByOrFail = jest.fn().mockResolvedValue({ id: mockServiceId });
    mockedWorkspaceRepository.findOneByOrFail = jest.fn().mockResolvedValue({ id: mockWorkspaceId });
    mockedWorkspaceServiceRepository.findOne = jest.fn().mockResolvedValue(null); // Service doesn't exist yet
    mockedWorkspaceServiceRepository.create = jest.fn().mockReturnValue(mockWorkspaceService);
    mockedWorkspaceServiceRepository.save = jest.fn().mockResolvedValue(mockWorkspaceService);

    const result = await workspacesService.addServiceInWorkspace(mockWorkspaceId, mockServiceId);

    expect(result).toEqual(mockWorkspaceService);
    expect(mockedServiceRepository.findOneByOrFail).toHaveBeenCalledWith({
      id: mockServiceId,
    });
    expect(mockedWorkspaceRepository.findOneByOrFail).toHaveBeenCalledWith({
      id: mockWorkspaceId,
    });
    expect(mockedWorkspaceServiceRepository.findOne).toHaveBeenCalledWith({
      where: { serviceId: mockServiceId, workspaceId: mockWorkspaceId },
    });
    expect(mockedWorkspaceServiceRepository.create).toHaveBeenCalledWith({
      serviceId: mockServiceId,
      workspaceId: mockWorkspaceId,
      status: true,
    });
    expect(mockedWorkspaceServiceRepository.save).toHaveBeenCalledWith(mockWorkspaceService);
  });

  it('should return existing service-workspace association when it already exists', async () => {
    const mockWorkspaceId = 'workspace-id';
    const mockServiceId = 'service-id';
    const existingWorkspaceService: Partial<ServiceWorkspace> = {
      id: 'existing-workspace-service-id',
      workspaceId: mockWorkspaceId,
      serviceId: mockServiceId,
      status: true,
    };

    mockedServiceRepository.findOneByOrFail = jest.fn().mockResolvedValue({ id: mockServiceId });
    mockedWorkspaceRepository.findOneByOrFail = jest.fn().mockResolvedValue({ id: mockWorkspaceId });
    mockedWorkspaceServiceRepository.findOne = jest.fn().mockResolvedValue(existingWorkspaceService);

    const result = await workspacesService.addServiceInWorkspace(mockWorkspaceId, mockServiceId);

    expect(result).toEqual(existingWorkspaceService);
    expect(mockedServiceRepository.findOneByOrFail).toHaveBeenCalledWith({
      id: mockServiceId,
    });
    expect(mockedWorkspaceRepository.findOneByOrFail).toHaveBeenCalledWith({
      id: mockWorkspaceId,
    });
    expect(mockedWorkspaceServiceRepository.findOne).toHaveBeenCalledWith({
      where: { serviceId: mockServiceId, workspaceId: mockWorkspaceId },
    });
    expect(mockedWorkspaceServiceRepository.create).not.toHaveBeenCalled();
    expect(mockedWorkspaceServiceRepository.save).not.toHaveBeenCalled();
  });
  it('should format and return a unique workspace hash ID', async () => {
    const mockWorkspace: Partial<Workspace> = {
      id: 'workspace-1',
      name: 'Test Workspace',
    };

    const inputHash = 'test-workspace';
    mockedWorkspaceRepository.find = jest.fn().mockResolvedValue([]);

    const result = await workspacesService.formatWorkspaceHashId(inputHash, mockWorkspace as Workspace);

    expect(result).toBe('test-workspace');
    expect(mockedWorkspaceRepository.find).toHaveBeenCalledWith({
      where: {
        hashId: 'test-workspace',
        id: Not('workspace-1'),
      },
    });
  });

  it('should handle special characters and convert to lowercase', async () => {
    const mockWorkspace: Partial<Workspace> = {
      id: 'workspace-1',
      name: 'Test Workspace',
    };

    // Testing multiple special characters
    const inputHash = 'Test Work$pace & Company!!!';
    mockedWorkspaceRepository.find = jest.fn().mockResolvedValue([]);

    const result = await workspacesService.formatWorkspaceHashId(inputHash, mockWorkspace as Workspace);

    // Verify that it's lowercase and special characters are handled according to slugify's behavior
    expect(result).toBe('test-workdollarpace-and-company');
  });

  it('should append suffix for duplicate hash IDs', async () => {
    const mockWorkspace: Partial<Workspace> = {
      id: 'workspace-2',
      name: 'Test Workspace',
      hashId: 'test-workspace',
    };

    // Mock that original hash exists
    mockedWorkspaceRepository.find = jest.fn().mockResolvedValue([{ id: 'workspace-1', hashId: 'test-workspace' }]);

    // Mock that first suffix attempt also exists
    mockedWorkspaceRepository.findOne = jest
      .fn()
      .mockResolvedValueOnce({ id: 'workspace-3', hashId: 'test-workspace-lxp-1' })
      .mockResolvedValueOnce(null);

    const result = await workspacesService.formatWorkspaceHashId('test-workspace', mockWorkspace as Workspace);

    expect(result).toBe('test-workspace-lxp-2');
    expect(mockedWorkspaceRepository.findOne).toHaveBeenCalledWith({
      where: { hashId: 'test-workspace-lxp-1' },
    });
  });

  it('should handle empty or null input hash', async () => {
    const mockWorkspace: Partial<Workspace> = {
      id: 'workspace-1',
      name: 'Test Workspace',
    };

    mockedWorkspaceRepository.find = jest.fn().mockResolvedValue([]);

    const resultEmpty = await workspacesService.formatWorkspaceHashId('', mockWorkspace as Workspace);
    const resultNull = await workspacesService.formatWorkspaceHashId(null, mockWorkspace as Workspace);

    expect(resultEmpty).toBe('');
    expect(resultNull).toBe('');
  });

  it('should not check for duplicates when workspace has no ID', async () => {
    const mockWorkspace: Partial<Workspace> = {
      name: 'Test Workspace',
    };

    const inputHash = 'test-workspace';
    mockedWorkspaceRepository.find = jest.fn().mockResolvedValue([]);

    const result = await workspacesService.formatWorkspaceHashId(inputHash, mockWorkspace as Workspace);

    expect(result).toBe('test-workspace');
    expect(mockedWorkspaceRepository.find).toHaveBeenCalledWith({
      where: {
        hashId: 'test-workspace',
        id: undefined,
      },
    });
  });

  it('should soft delete a service from a workspace by setting status to false', async () => {
    const mockWorkspaceId = 'workspace-id';
    const mockServiceId = 'service-id';
    const mockServiceWorkspace: Partial<ServiceWorkspace> = {
      id: 'service-workspace-id',
      workspaceId: mockWorkspaceId,
      serviceId: mockServiceId,
      status: true,
    };

    mockedWorkspaceServiceRepository.findOneOrFail = jest.fn().mockResolvedValue(mockServiceWorkspace);
    mockedWorkspaceServiceRepository.save = jest.fn().mockResolvedValue({
      ...mockServiceWorkspace,
      status: false,
    });

    await workspacesService.removeServiceFromWorkspace(mockWorkspaceId, mockServiceId);

    expect(mockedWorkspaceServiceRepository.findOneOrFail).toHaveBeenCalledWith({
      where: { workspaceId: mockWorkspaceId, serviceId: mockServiceId },
    });
    expect(mockedWorkspaceServiceRepository.save).toHaveBeenCalledWith({
      ...mockServiceWorkspace,
      status: false,
    });
  });

  it('should reactivate a soft-deleted service when adding it to a workspace', async () => {
    const mockWorkspaceId = 'workspace-id';
    const mockServiceId = 'service-id';
    const mockServiceWorkspace: Partial<ServiceWorkspace> = {
      id: 'service-workspace-id',
      workspaceId: mockWorkspaceId,
      serviceId: mockServiceId,
      status: false, // Service was previously soft-deleted
    };

    mockedServiceRepository.findOneByOrFail = jest
      .fn()
      .mockResolvedValue({ id: mockServiceId, applicationId: 'app-id' });
    mockedWorkspaceRepository.findOneByOrFail = jest
      .fn()
      .mockResolvedValue({ id: mockWorkspaceId, companyId: 'company-id' });
    mockedWorkspaceServiceRepository.findOne = jest.fn().mockResolvedValue(mockServiceWorkspace);
    mockedWorkspaceServiceRepository.save = jest.fn().mockResolvedValue({
      ...mockServiceWorkspace,
      status: true,
    });

    const result = await workspacesService.addServiceInWorkspace(mockWorkspaceId, mockServiceId);

    expect(mockedWorkspaceServiceRepository.findOne).toHaveBeenCalledWith({
      where: { serviceId: mockServiceId, workspaceId: mockWorkspaceId },
    });
    expect(mockedWorkspaceServiceRepository.save).toHaveBeenCalledWith({
      ...mockServiceWorkspace,
      status: true,
    });
    expect(result).toEqual({
      ...mockServiceWorkspace,
      status: true,
    });
  });

  describe('getBannerMode', () => {
    it('should get banner mode successfully when workspace exists', async () => {
      const workspaceId = 'workspace-id';
      const mockWorkspace: Partial<Workspace> = {
        id: 'workspace-id',
        name: 'Test Workspace',
        bannerMode: BannerMode.MANUAL,
      };

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(mockWorkspace);

      const result = await workspacesService.getBannerMode(workspaceId);

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
      expect(result).toEqual({ bannerMode: BannerMode.MANUAL });
    });

    it('should get banner mode with RECOMMENDATION value', async () => {
      const workspaceId = 'workspace-id';
      const mockWorkspace: Partial<Workspace> = {
        id: 'workspace-id',
        name: 'Test Workspace',
        bannerMode: BannerMode.RECOMMENDATION,
      };

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(mockWorkspace);

      const result = await workspacesService.getBannerMode(workspaceId);

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
      expect(result).toEqual({ bannerMode: BannerMode.RECOMMENDATION });
    });

    it('should throw NotFoundException when workspace is not found', async () => {
      const workspaceId = 'non-existent-id';

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(null);

      await expect(workspacesService.getBannerMode(workspaceId)).rejects.toThrow(NotFoundException);
      await expect(workspacesService.getBannerMode(workspaceId)).rejects.toThrow(
        `Workspace with ID ${workspaceId} not found`,
      );

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
    });

    it('should throw NotFoundException when workspace is undefined', async () => {
      const workspaceId = 'undefined-workspace-id';

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(undefined);

      await expect(workspacesService.getBannerMode(workspaceId)).rejects.toThrow(NotFoundException);
      await expect(workspacesService.getBannerMode(workspaceId)).rejects.toThrow(
        `Workspace with ID ${workspaceId} not found`,
      );

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
    });
  });

  describe('updateBannerMode', () => {
    it('should update banner mode successfully when workspace exists', async () => {
      const workspaceId = 'workspace-id';
      const bannerModeDto: WorkspaceBannerModeDto = {
        bannerMode: BannerMode.MANUAL,
      };

      const mockWorkspace: Partial<Workspace> = {
        id: 'workspace-id',
        name: 'Test Workspace',
        bannerMode: BannerMode.RECOMMENDATION,
      };

      const updatedWorkspace: Partial<Workspace> = {
        ...mockWorkspace,
        bannerMode: BannerMode.MANUAL,
      };

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(mockWorkspace);
      mockedWorkspaceRepository.save = jest.fn().mockResolvedValue(updatedWorkspace);

      const result = await workspacesService.updateBannerMode(workspaceId, bannerModeDto);

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
      expect(mockedWorkspaceRepository.save).toHaveBeenCalledWith({
        ...mockWorkspace,
        bannerMode: BannerMode.MANUAL,
      });
      expect(result).toBeUndefined();
    });

    it('should update banner mode to RECOMMENDATION', async () => {
      const workspaceId = 'workspace-id';
      const bannerModeDto: WorkspaceBannerModeDto = {
        bannerMode: BannerMode.RECOMMENDATION,
      };

      const mockWorkspace: Partial<Workspace> = {
        id: 'workspace-id',
        name: 'Test Workspace',
        bannerMode: BannerMode.MANUAL,
      };

      const updatedWorkspace: Partial<Workspace> = {
        ...mockWorkspace,
        bannerMode: BannerMode.RECOMMENDATION,
      };

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(mockWorkspace);
      mockedWorkspaceRepository.save = jest.fn().mockResolvedValue(updatedWorkspace);

      const result = await workspacesService.updateBannerMode(workspaceId, bannerModeDto);

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
      expect(mockedWorkspaceRepository.save).toHaveBeenCalledWith({
        ...mockWorkspace,
        bannerMode: BannerMode.RECOMMENDATION,
      });
      expect(result).toBeUndefined();
    });

    it('should throw NotFoundException when workspace is not found', async () => {
      const workspaceId = 'non-existent-id';
      const bannerModeDto: WorkspaceBannerModeDto = {
        bannerMode: BannerMode.MANUAL,
      };

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(null);

      await expect(workspacesService.updateBannerMode(workspaceId, bannerModeDto)).rejects.toThrow(NotFoundException);
      await expect(workspacesService.updateBannerMode(workspaceId, bannerModeDto)).rejects.toThrow(
        `Workspace with ID ${workspaceId} not found`,
      );

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
      expect(mockedWorkspaceRepository.save).not.toHaveBeenCalled();
    });

    it('should throw NotFoundException when workspace is undefined', async () => {
      const workspaceId = 'undefined-workspace-id';
      const bannerModeDto: WorkspaceBannerModeDto = {
        bannerMode: BannerMode.MANUAL,
      };

      mockedWorkspaceRepository.findOneBy = jest.fn().mockResolvedValue(undefined);

      await expect(workspacesService.updateBannerMode(workspaceId, bannerModeDto)).rejects.toThrow(NotFoundException);
      await expect(workspacesService.updateBannerMode(workspaceId, bannerModeDto)).rejects.toThrow(
        `Workspace with ID ${workspaceId} not found`,
      );

      expect(mockedWorkspaceRepository.findOneBy).toHaveBeenCalledWith({ id: workspaceId });
      expect(mockedWorkspaceRepository.save).not.toHaveBeenCalled();
    });
  });
});
