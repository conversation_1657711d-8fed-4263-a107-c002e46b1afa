import { AuthGuard, ResourceGuard } from 'nest-keycloak-connect';
import { ExceptionI18nInterceptor, TenantGuard } from '@keeps-node-apis/@core';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { MyAccountRolesGuard } from './@core/common/guards/my-account-roles.guard';

export const APP_PROVIDERS = [
  {
    provide: APP_GUARD,
    useClass: ResourceGuard,
  },
  {
    provide: APP_GUARD,
    useClass: AuthGuard,
  },
  {
    provide: APP_GUARD,
    useClass: TenantGuard,
  },
  {
    provide: APP_GUARD,
    useClass: MyAccountRolesGuard,
  },
  {
    provide: APP_INTERCEPTOR,
    useClass: ExceptionI18nInterceptor,
  },
];
