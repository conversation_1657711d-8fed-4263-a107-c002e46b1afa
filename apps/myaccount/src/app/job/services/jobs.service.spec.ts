import { JobsService } from './jobs.service';
import { JobsRepository } from '../interfaces/jobs.repository';
import { TenantService } from '@keeps-node-apis/@core';
import { Logger } from '@nestjs/common';
import { Chance } from 'chance';
import { JobCreateDto } from '../presentation/dtos/job-create.dto';
import { JobNameAlreadyExistsDomainException } from '../domain/exceptions/job.domain.exceptions';
import { Job } from '../../entities/job.entity';

describe('JobsService', () => {
  let service: JobsService;
  let mockJobRepository: jest.Mocked<JobsRepository>;
  let mockTenantService: jest.Mocked<TenantService>;
  const chance = new Chance();
  const tenantId = chance.guid();

  beforeEach(() => {
    // Suppress logger during tests
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);

    mockJobRepository = {
      list: jest.fn().mockResolvedValue({}),
      create: jest.fn().mockResolvedValue({}),
      update: jest.fn().mockResolvedValue({}),
      delete: jest.fn().mockResolvedValue(null),
      findOneByName: jest.fn().mockResolvedValue(undefined),
      findOrCreate: jest.fn(),
    };

    mockTenantService = {
      getTenantId: jest.fn().mockReturnValue(tenantId),
    } as unknown as jest.Mocked<TenantService>;

    service = new JobsService(mockJobRepository, mockTenantService);
  });

  describe('list', () => {
    it('should return the job list', async () => {
      const mockSearch = chance.name();

      await service.list(mockSearch);

      expect(mockJobRepository.list).toHaveBeenCalledWith(tenantId, mockSearch);
    });
  });

  describe('create', () => {
    it('should create a new job ', async () => {
      const createDto: JobCreateDto = { name: chance.name() };

      await service.create(createDto);

      expect(mockJobRepository.create).toHaveBeenCalledWith(tenantId, createDto);
    });

    it('should throw JobNameAlreadyExistsDomainException when there is a job with the same name', async () => {
      const name = chance.name();
      const createDto: JobCreateDto = { name };
      mockJobRepository.findOneByName.mockResolvedValueOnce({ name, id: chance.guid() } as Job);

      await expect(service.create(createDto)).rejects.toThrow(JobNameAlreadyExistsDomainException);
    });
  });

  describe('update', () => {
    it('should update a job ', async () => {
      const jobId = chance.guid();
      const jobName = chance.name();
      const updateDto: JobCreateDto = { name: jobName };
      mockJobRepository.findOneByName.mockResolvedValueOnce({
        name: jobName,
        id: jobId,
      } as Job);

      await service.update(jobId, updateDto);

      expect(mockJobRepository.update).toHaveBeenCalledWith(tenantId, jobId, updateDto);
    });

    it('should throw JobNameAlreadyExistsDomainException when there is a job with the same name and a different id', async () => {
      const name = chance.name();
      const jobId = chance.guid();
      const updateDto: JobCreateDto = { name };
      mockJobRepository.findOneByName.mockResolvedValueOnce({ name, id: chance.guid() } as Job);

      await expect(service.update(jobId, updateDto)).rejects.toThrow(JobNameAlreadyExistsDomainException);
    });
  });

  describe('delete', () => {
    it('should delete a job ', async () => {
      const jobId = chance.guid();

      await service.delete(jobId);

      expect(mockJobRepository.delete).toHaveBeenCalledWith(tenantId, jobId);
    });
  });
});
