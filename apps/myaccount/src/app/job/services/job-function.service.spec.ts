import { JobFunctionsService } from './job-function.service';
import { JobFunctionsRepository } from '../interfaces/job-functions.repository';
import { TenantService } from '@keeps-node-apis/@core';
import { Logger } from '@nestjs/common';
import { Chance } from 'chance';
import { JobFunctionCreateDto } from '../presentation/dtos/job-function-create.dto';
import { JobFunctionNameAlreadyExistsDomainException } from '../domain/exceptions/job-function.domain.exceptions';
import { JobFunction } from '../../entities/job-function.entity';

describe('JobFunctionsService', () => {
  let service: JobFunctionsService;
  let mockJobFunctionRepository: jest.Mocked<JobFunctionsRepository>;
  let mockTenantService: jest.Mocked<TenantService>;
  const chance = new Chance();
  const tenantId = chance.guid();

  beforeEach(() => {
    // Suppress logger during tests
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => undefined);

    mockJobFunctionRepository = {
      list: jest.fn().mockResolvedValue({}),
      create: jest.fn().mockResolvedValue({}),
      update: jest.fn().mockResolvedValue({}),
      delete: jest.fn().mockResolvedValue(null),
      findOneByName: jest.fn().mockResolvedValue(undefined),
      findOrCreate: jest.fn().mockResolvedValue({}),
    };

    mockTenantService = {
      getTenantId: jest.fn().mockReturnValue(tenantId),
    } as unknown as jest.Mocked<TenantService>;

    service = new JobFunctionsService(mockJobFunctionRepository, mockTenantService);
  });

  describe('list', () => {
    it('should return the job list', async () => {
      const mockSearch = chance.name();

      await service.list(mockSearch);

      expect(mockJobFunctionRepository.list).toHaveBeenCalledWith(tenantId, mockSearch);
    });
  });

  describe('create', () => {
    it('should create a new job function', async () => {
      const createDto: JobFunctionCreateDto = { name: chance.name() };

      await service.create(createDto);

      expect(mockJobFunctionRepository.create).toHaveBeenCalledWith(tenantId, createDto);
    });

    it('should throw JobFunctionNameAlreadyExistsDomainException when there is a job function with the same name', async () => {
      const name = chance.name();
      const createDto: JobFunctionCreateDto = { name };
      mockJobFunctionRepository.findOneByName.mockResolvedValueOnce({ name, id: chance.guid() } as JobFunction);

      await expect(service.create(createDto)).rejects.toThrow(JobFunctionNameAlreadyExistsDomainException);
    });
  });

  describe('update', () => {
    it('should update a job function', async () => {
      const jobFunctionId = chance.guid();
      const jobFunctionName = chance.name();
      const updateDto: JobFunctionCreateDto = { name: jobFunctionName };
      mockJobFunctionRepository.findOneByName.mockResolvedValueOnce({
        name: jobFunctionName,
        id: jobFunctionId,
      } as JobFunction);

      await service.update(jobFunctionId, updateDto);

      expect(mockJobFunctionRepository.update).toHaveBeenCalledWith(tenantId, jobFunctionId, updateDto);
    });

    it('should throw JobFunctionNameAlreadyExistsDomainException when there is a job function with the same name and a different id', async () => {
      const name = chance.name();
      const jobFunctionId = chance.guid();
      const updateDto: JobFunctionCreateDto = { name };
      mockJobFunctionRepository.findOneByName.mockResolvedValueOnce({ name, id: chance.guid() } as JobFunction);

      await expect(service.update(jobFunctionId, updateDto)).rejects.toThrow(
        JobFunctionNameAlreadyExistsDomainException,
      );
    });
  });

  describe('delete', () => {
    it('should delete a job function', async () => {
      const jobFunctionId = chance.guid();

      await service.delete(jobFunctionId);

      expect(mockJobFunctionRepository.delete).toHaveBeenCalledWith(tenantId, jobFunctionId);
    });
  });
});
