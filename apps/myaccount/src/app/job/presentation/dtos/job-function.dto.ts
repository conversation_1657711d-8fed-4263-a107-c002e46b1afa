import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class JobFunctionDto {
  @ApiProperty({
    description: 'ID for the job function',
    example: 'a0eeb17b-779b-4424-b4f5-e44b6b7c3eb4',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Name of the job function',
    example: 'Back-end Developer',
    nullable: true,
  })
  @Expose()
  name: string | null;
}
