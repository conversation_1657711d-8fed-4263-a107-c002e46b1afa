import { Body, Controller, Delete, Get, HttpStatus, Param, Patch, Post, Query } from '@nestjs/common';
import { KONQUEST_ADMIN_ROLES, MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { JobFunctionDto } from '../dtos/job-function.dto';
import { ApiNoContentResponse, ApiOkResponse, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JobFunctionsService } from '../../services/job-function.service';
import { JobFunctionCreateDto } from '../dtos/job-function-create.dto';

/**
 * Controller responsible for handling job function related API endpoints.
 * Restricted to users with that company and Konquest Admins
 */
@ApiTags('Job Functions')
@Controller('job-functions')
@Roles([...MYACCOUNT_ADMIN_ROLES, ...KONQUEST_ADMIN_ROLES])
export class JobFunctionsController {
  constructor(private readonly jobFunctionsService: JobFunctionsService) {}

  /**
   * Retrieves a list of allowed job functions for a given workspace.
   * @param searchName Optional query parameter to filter job functions by name
   * @returns A list of job functions in the JobResponseDto format
   * @throws {HttpStatus.FORBIDDEN} When the user lacks sufficient permissions
   */
  @Get()
  @Serialize(JobFunctionDto)
  @ApiOperation({ summary: 'Retrieve all allowed job functions for a workspace' })
  @ApiOkResponse({ description: 'Job functions retrieved successfully', type: JobFunctionDto, isArray: true })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  async list(@Query('searchName') searchName?: string): Promise<JobFunctionDto[]> {
    return this.jobFunctionsService.list(searchName);
  }

  /**
   * Create a new job function
   * @param createDto The job function creation dto
   * @returns The created job function
   * @throws JobFunctionNameAlreadyExistsDomainException When the provided name already exists and belongs to another job function
   * @throws HttpStatus.FORBIDDEN When the user lacks sufficient permissions
   */
  @Post()
  @Serialize(JobFunctionDto)
  @ApiOperation({ summary: 'Create a new job function for a workspace' })
  @ApiOkResponse({ description: 'Job function created successfully', type: JobFunctionDto })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Duplicated name provided',
  })
  async create(@Body() createDto: JobFunctionCreateDto): Promise<JobFunctionDto> {
    return this.jobFunctionsService.create(createDto);
  }

  /**
   * Update an existing job function
   * @param id The id of the job function to update
   * @param updateDto The update dto
   * @returns The updated job function
   * @throws JobFunctionNameAlreadyExistsDomainException When the provided name already exists and belongs to another job function
   * @throws HttpStatus.FORBIDDEN When the user lacks sufficient permissions
   */
  @Patch('/:id')
  @Serialize(JobFunctionDto)
  @ApiOperation({ summary: 'Update an existing job function in a workspace' })
  @ApiOkResponse({ description: 'Job function updated successfully', type: JobFunctionDto })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Duplicated name provided',
  })
  async update(@Param('id') id: string, @Body() updateDto: JobFunctionCreateDto): Promise<JobFunctionDto> {
    return this.jobFunctionsService.update(id, updateDto);
  }

  /**
   * Delete an existing job function
   * @param id The id of the job function to delete
   * @throws HttpStatus.FORBIDDEN When the user lacks sufficient permissions
   */
  @Delete('/:id')
  @ApiOperation({ summary: 'Delete a job function in a workspace' })
  @ApiNoContentResponse({ description: 'Job function deleted successfully' })
  @ApiResponse({ status: HttpStatus.FORBIDDEN, description: 'Insufficient permissions' })
  async delete(@Param('id') id: string): Promise<void> {
    return this.jobFunctionsService.delete(id);
  }
}
