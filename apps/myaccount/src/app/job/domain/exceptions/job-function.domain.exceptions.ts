import { DomainException } from '../../../@core/common/exceptions/base.exception';

export class JobFunctionDomainException extends DomainException {
  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message, `JOB_FUNCTION_${errorCode}`, details);
  }
}

export class JobFunctionNameAlreadyExistsDomainException extends JobFunctionDomainException {
  constructor(duplicateName: string) {
    super(`Job function with the following name already exists: ${duplicateName}`, 'DUPLICATE_NAME');
  }
}
