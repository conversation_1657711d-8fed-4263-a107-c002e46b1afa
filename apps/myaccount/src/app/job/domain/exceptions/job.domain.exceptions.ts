import { DomainException } from '../../../@core/common/exceptions/base.exception';

export class JobDomainException extends DomainException {
  constructor(message: string, errorCode: string, details?: Record<string, any>) {
    super(message, `JOB_${errorCode}`, details);
  }
}

export class JobNameAlreadyExistsDomainException extends JobDomainException {
  constructor(duplicateName: string) {
    super(`Job with the following name already exists: ${duplicateName}`, 'DUPLICATE_NAME');
  }
}
