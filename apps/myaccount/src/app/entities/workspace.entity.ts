import { <PERSON>umn, <PERSON><PERSON><PERSON>, Index, Join<PERSON><PERSON><PERSON>n, ManyToOne, OneToMany } from 'typeorm';
import { GamificationRankingWorkspace } from './gamification-ranking-workspace.entity';
import { IdpWorkspace } from './idp-workspace.entity';
import { Job } from './job.entity';
import { JobFunction } from './job-function.entity';
import { ServiceWorkspace } from './service-workspace.entity';
import { EmployeeInfo } from './employee-info.entity';
import { UserRoleWorkspace } from './user-role-workspace.entity';
import { Company } from './company.entity';
import { BaseEntity } from './base-entity';
import { WorkspaceFilterSettings } from './workspace-filter-settings.entity';
import { BannerMode } from '../workspaces/types/banner-mode.enum';

@Index('workspace_company_id_d3181235', ['companyId'], {})
@Index('workspace_hash_id_key', ['hashId'], { unique: true })
@Index('workspace_hash_id_4cb6f623_like', ['hashId'], {})
@Index('company_pkey', ['id'], { unique: true })
@Entity('workspace', { schema: 'public' })
export class Workspace extends BaseEntity {
  @Column('character varying', { name: 'name', length: 200 })
  name: string;

  @Column('character varying', {
    name: 'duns_number',
    nullable: true,
    length: 200,
  })
  dunsNumber: string | null;

  @Column('character varying', {
    name: 'doc_number',
    nullable: true,
    length: 200,
  })
  docNumber: string | null;

  @Column('text', { name: 'description', nullable: true })
  description: string | null;

  @Column('boolean', { name: 'status' })
  status: boolean;

  @Column('text', { name: 'address', nullable: true })
  address: string | null;

  @Column('character varying', { name: 'city', nullable: true, length: 200 })
  city: string | null;

  @Column('character varying', { name: 'country', nullable: true, length: 200 })
  country: string | null;

  @Column('text', { name: 'icon_url', nullable: true })
  iconUrl: string | null;

  @Column('text', { name: 'logo_url', nullable: true })
  logoUrl: string | null;

  @Column('character varying', {
    name: 'post_code',
    nullable: true,
    length: 200,
  })
  postCode: string | null;

  @Column('character varying', { name: 'state', nullable: true, length: 200 })
  state: string | null;

  @Column('text', { name: 'theme_id', nullable: true })
  themeId: string | null;

  @Column('text', { name: 'icon_svg_url', nullable: true })
  iconSvgUrl: string | null;

  @Column('boolean', { name: 'theme_dark' })
  themeDark = false;

  @Column('uuid', { name: 'company_id' })
  companyId: string;

  @Column('boolean', { name: 'enable_email_notifications' })
  enableEmailNotifications = true;

  @Column('character varying', {
    name: 'smtp_auth_pass',
    nullable: true,
    length: 1000,
  })
  smtpAuthPass: string | null;

  @Column('character varying', {
    name: 'smtp_auth_user',
    nullable: true,
    length: 400,
  })
  smtpAuthUser: string | null;

  @Column('character varying', {
    name: 'smtp_host',
    nullable: true,
    length: 400,
  })
  smtpHost: string | null;

  @Column('integer', { name: 'smtp_port', nullable: true })
  smtpPort: number | null;

  @Column('boolean', { name: 'smtp_reject_unauthorized', nullable: true })
  smtpRejectUnauthorized: boolean | null;

  @Column('boolean', { name: 'smtp_secure', nullable: true })
  smtpSecure: boolean | null;

  @Column('character varying', {
    name: 'smtp_sender_email',
    nullable: true,
    length: 400,
  })
  smtpSenderEmail: string | null;

  @Column('boolean', { name: 'use_own_smtp' })
  useOwnSmtp = false;

  @Column('text', { name: 'logout_url', nullable: true })
  logoutUrl: string | null;

  @Column('character varying', {
    name: 'custom_color',
    nullable: true,
    length: 16,
  })
  customColor: string | null;

  @Column('boolean', { name: 'allow_create_paid_channel' })
  allowCreatePaidChannel = false;

  @Column('boolean', { name: 'allow_create_paid_mission' })
  allowCreatePaidMission = false;

  @Column('boolean', { name: 'allow_create_public_channel' })
  allowCreatePublicChannel = false;

  @Column('boolean', { name: 'allow_create_public_mission' })
  allowCreatePublicMission = false;

  @Column('boolean', { name: 'allow_list_paid_channel' })
  allowListPaidChannel = false;

  @Column('boolean', { name: 'allow_list_paid_mission' })
  allowListPaidMission = false;

  @Column('boolean', { name: 'allow_list_public_categories' })
  allowListPublicCategories = true;

  @Column('boolean', { name: 'allow_list_public_channel' })
  allowListPublicChannel = true;

  @Column('boolean', { name: 'allow_list_public_mission' })
  allowListPublicMission = true;

  @Column('double precision', {
    name: 'min_performance_certificate',
    precision: 53,
  })
  minPerformanceCertificate = 0;

  @Column('boolean', { name: 'need_approve_channel' })
  needApproveChannel = false;

  @Column('boolean', { name: 'need_approve_mission' })
  needApproveMission = false;

  @Column('text', { name: 'custom_login_url', nullable: true })
  customLoginUrl: string | null;

  @Column('character varying', {
    name: 'hash_id',
    nullable: true,
    unique: true,
    length: 125,
  })
  hashId: string | null;

  @Column('boolean', { name: 'notify_slack' })
  notifySlack = false;

  @Column('boolean', { name: 'notify_teams' })
  notifyTeams = false;

  @Column('integer', { name: 'enrollment_goal_duration_days' })
  enrollmentGoalDurationDays = 30;

  @Column('boolean', { name: 'alura_integration_active' })
  aluraIntegrationActive = false;

  @Column('character varying', {
    name: 'default_federated_identity_provider_alias',
    nullable: true,
    length: 100,
  })
  defaultFederatedIdentityProviderAlias: string | null;

  @Column('boolean', { name: 'block_reenrollment' })
  blockReenrollment: boolean;

  @Column({
    type: 'enum',
    name: 'banner_mode',
    nullable: true,
    default: BannerMode.RECOMMENDATION,
    enum: BannerMode,
  })
  bannerMode: BannerMode = BannerMode.RECOMMENDATION;

  @OneToMany(
    () => GamificationRankingWorkspace,
    (gamificationRankingWorkspace) => gamificationRankingWorkspace.workspace,
  )
  gamificationRankingWorkspaces: GamificationRankingWorkspace[];

  @OneToMany(() => IdpWorkspace, (idpWorkspace) => idpWorkspace.workspace)
  idpWorkspaces: IdpWorkspace[];

  @OneToMany(() => Job, (job) => job.workspace)
  jobs: Job[];

  @OneToMany(() => JobFunction, (jobFunction) => jobFunction.workspace)
  jobFunctions: JobFunction[];

  @OneToMany(() => ServiceWorkspace, (serviceWorkspace) => serviceWorkspace.workspace)
  serviceWorkspaces: ServiceWorkspace[];

  @OneToMany(() => EmployeeInfo, (employeeInfo) => employeeInfo.workspace)
  employeeInfos: EmployeeInfo[];

  @OneToMany(() => UserRoleWorkspace, (userRoleWorkspace) => userRoleWorkspace.workspace)
  userRoleWorkspaces: UserRoleWorkspace[];

  @ManyToOne(() => Company, (company) => company.workspaces)
  @JoinColumn([{ name: 'company_id', referencedColumnName: 'id' }])
  company: Company;

  @OneToMany(() => WorkspaceFilterSettings, (workspaceFilterSettings) => workspaceFilterSettings.workspace)
  workspaceFilterSettings: WorkspaceFilterSettings[];
}
