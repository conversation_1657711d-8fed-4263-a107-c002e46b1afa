import { Type } from 'class-transformer';
import { IsArray, ValidateNested } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { BatchResultDto } from '../../users/dtos/create-user.dto';

/**
 * DTO for gRPC user creation response
 * Uses the existing BatchResultDto to maintain consistency
 */
export class CreateUsersGrpcResponseDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BatchResultDto)
  @ApiProperty({ type: [BatchResultDto] })
  users: BatchResultDto[];
}
