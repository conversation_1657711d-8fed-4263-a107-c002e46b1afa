import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, ValidateNested, IsString, ArrayNotEmpty } from 'class-validator';
import { Type } from 'class-transformer';
import { UserDataWithMoreDto } from '../../users/dtos/create-user.dto';
import { MYACCOUNT_ADMIN_ROLES } from '@keeps-node-apis/@core';

export class CreateUsersDto {
  @ApiProperty({
    type: () => [UserDataWithMoreDto],
    description: 'List of users to be created with their respective information',
    example: [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
      },
    ],
  })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => UserDataWithMoreDto)
  users: UserDataWithMoreDto[];

  @ApiPropertyOptional({
    type: [String],
    description: 'List of permissions to be assigned to the users (optional)',
    example: [Object.values(MYACCOUNT_ADMIN_ROLES).join(', ')],
  })
  @IsArray()
  @IsString({ each: true })
  permissions?: string[];
}
