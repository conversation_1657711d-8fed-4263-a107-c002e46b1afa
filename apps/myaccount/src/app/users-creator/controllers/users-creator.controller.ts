import { EmptyToNullInterceptor, MYACCOUNT_ADMIN_ROLES, Roles, Serialize, TenantService } from '@keeps-node-apis/@core';
import { Body, Controller, Post, UseInterceptors } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiSecurity, ApiTags } from '@nestjs/swagger';
import { CreateUsersDto } from '../dtos/create-users.dto';
import { BatchResultDto } from '../../users/dtos/create-user.dto';
import { UserBatchService } from '../services/user-batch.service';
import { CreateUserDto, CreateUserResultDto } from '../dtos/create-user.dto';
import { UserCreationService } from '../services/user-creation.service';

@ApiTags('Users')
@ApiBearerAuth()
@ApiSecurity('x-client')
@Controller('users')
@UseInterceptors(EmptyToNullInterceptor)
export class UsersCreatorController {
  constructor(
    private readonly userBatchService: UserBatchService,
    private readonly userCreateService: UserCreationService,
    private readonly tenantService: TenantService,
  ) {}

  @Post('batch-create')
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiOperation({
    summary: 'Create new users in batch',
    description: 'Creates one or more new users in the system with specified roles and permissions',
  })
  @ApiResponse({
    status: 200,
    description: 'Users successfully created',
    type: BatchResultDto,
    isArray: true,
  })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to create users' })
  @Serialize(BatchResultDto)
  async createUserInBatch(@Body() createUsersDto: CreateUsersDto) {
    const workspaceId = this.tenantService.getTenantId();
    return await this.userBatchService.saveUsers(createUsersDto, workspaceId);
  }

  @Post()
  @Roles(MYACCOUNT_ADMIN_ROLES)
  @ApiOperation({
    summary: 'Create new user',
  })
  @ApiResponse({
    status: 200,
    description: 'User successfully created',
    type: CreateUserResultDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request - Invalid input data' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Invalid or missing authentication token' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions to create users' })
  @Serialize(CreateUserResultDto)
  @Post()
  async createUser(@Body() createUserDto: CreateUserDto) {
    const workspaceId = this.tenantService.getTenantId();
    const result = await this.userCreateService.upsertUserWithPermissions(
      createUserDto,
      createUserDto.permissions,
      workspaceId,
    );
    return result.user;
  }
}
