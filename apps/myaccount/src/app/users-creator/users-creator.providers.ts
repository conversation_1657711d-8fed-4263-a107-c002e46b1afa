import { Provider } from '@nestjs/common';
import { UsersTypeOrmRepository } from '../users/repositories/users.repository-typeorm';
import { UserBatchService } from './services/user-batch.service';
import { UserCreationService } from './services/user-creation.service';
import { UserJobService } from './services/user-job.service';
import { UserLanguageService } from './services/user-language.service';
import { UserFormatterService } from './services/user-formatter.service';
import { UpdateUsersService } from '../users/services/update-users.service';
import { UserDataService } from './services/user-data.service';
import { UserNotificationService } from './services/user-notification.service';
import { WorkspacePermissionService } from './services/workspace-permission.service';
import { UserIdentityService } from './services/user-identity.service';
import { PasswordService } from './services/password.service';
import { getDataSourceToken, getRepositoryToken } from '@nestjs/typeorm';
import { EmployeeInfo } from '../entities/employee-info.entity';
import EmployeeInfosRepository from '../users/repositories/employee-infos.repository';
import { DataSource } from 'typeorm';
import { UserCreateGrpcService } from './services/user-create-grpc.service';

export const EXPORT_REPOSITORY = [
  {
    provide: getRepositoryToken(EmployeeInfo),
    useFactory(datasource: DataSource) {
      return new EmployeeInfosRepository(datasource);
    },
    inject: [getDataSourceToken()],
  },
];

const REPOSITORIES: Provider[] = [...EXPORT_REPOSITORY];

export default [
  PasswordService,
  UserIdentityService,
  WorkspacePermissionService,
  UserNotificationService,
  UserDataService,
  UpdateUsersService,
  UsersTypeOrmRepository,
  UserCreationService,
  UserJobService,
  UserLanguageService,
  UserFormatterService,
  UserBatchService,
  UserCreateGrpcService,
  ...REPOSITORIES,
];
