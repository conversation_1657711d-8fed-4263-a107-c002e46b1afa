import { Test, TestingModule } from '@nestjs/testing';
import { UserJobService } from './user-job.service';
import { JobsRepository } from '../../job/interfaces/jobs.repository';
import { JobFunctionsRepository } from '../../job/interfaces/job-functions.repository';
import { JobFunction } from '../../entities/job-function.entity';
import { Job } from '../../entities/job.entity';
import { UserDataDto } from '../../users/dtos/create-user.dto';
import { v4 as uuidv4 } from 'uuid';
import { EmployeeInfoCreateDto } from '../../users/dtos/employee-info-create.dto';

const mockJobRepository = {
  findOrCreate: jest.fn(),
};

const mockJobFunctionRepository = {
  findOrCreate: jest.fn(),
};

describe('UserJobService', () => {
  let service: UserJobService;
  let jobRepository: JobsRepository;
  let jobFunctionRepository: JobFunctionsRepository;

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserJobService,
        {
          provide: JobsRepository,
          useValue: mockJobRepository,
        },
        {
          provide: JobFunctionsRepository,
          useValue: mockJobFunctionRepository,
        },
      ],
    }).compile();

    service = module.get<UserJobService>(UserJobService);
    jobRepository = module.get<JobsRepository>(JobsRepository);
    jobFunctionRepository = module.get<JobFunctionsRepository>(JobFunctionsRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('setJobData', () => {
    const workspaceId = uuidv4();
    const baseUserData: UserDataDto = {
      email: '<EMAIL>',
      name: 'Test User',
      profile: new EmployeeInfoCreateDto(),
    };

    it('should find or create Job and JobFunction and update UserDataDto profile', async () => {
      const userData: UserDataDto = {
        ...baseUserData,
        job: '  Developer  ',
        jobFunction: ' Backend Developer ',
        profile: { ...baseUserData.profile },
      };

      const mockJob: Job = {
        id: uuidv4(),
        name: 'DEVELOPER',
        workspaceId,
      } as JobFunction;
      const mockJobFunction: JobFunction = {
        id: uuidv4(),
        name: 'BACKEND DEVELOPER',
        workspaceId,
      } as JobFunction;

      (jobRepository.findOrCreate as jest.Mock).mockResolvedValue(mockJob);
      (jobFunctionRepository.findOrCreate as jest.Mock).mockResolvedValue(mockJobFunction);

      const result = await service.setJobData(userData, workspaceId);

      expect(jobRepository.findOrCreate).toHaveBeenCalledTimes(1);
      expect(jobRepository.findOrCreate).toHaveBeenCalledWith('DEVELOPER', workspaceId); // Check for trim and uppercase
      expect(jobFunctionRepository.findOrCreate).toHaveBeenCalledTimes(1);
      expect(jobFunctionRepository.findOrCreate).toHaveBeenCalledWith('BACKEND DEVELOPER', workspaceId); // Check for trim and uppercase

      expect(result).toBe(userData); // Should return the same object instance
      expect(result.profile?.jobPositionId).toBe(mockJob.id);
      expect(result.profile?.jobFunctionId).toBe(mockJobFunction.id);
    });

    it('should only find or create Job if only job name is provided', async () => {
      const userData: UserDataDto = {
        ...baseUserData,
        job: 'Manager',
        profile: { ...baseUserData.profile },
      };

      const mockJob: Job = {
        id: uuidv4(),
        name: 'MANAGER',
        workspaceId,
      } as Job;

      (jobRepository.findOrCreate as jest.Mock).mockResolvedValue(mockJob);

      const result = await service.setJobData(userData, workspaceId);

      expect(jobRepository.findOrCreate).toHaveBeenCalledTimes(1);
      expect(jobRepository.findOrCreate).toHaveBeenCalledWith('MANAGER', workspaceId);
      expect(jobFunctionRepository.findOrCreate).not.toHaveBeenCalled();

      expect(result.profile?.jobPositionId).toBe(mockJob.id);
      expect(result.profile?.jobFunctionId).toBeUndefined();
    });

    it('should only find or create JobFunction if only job function name is provided', async () => {
      const userData: UserDataDto = {
        ...baseUserData,
        jobFunction: ' Frontend ',
        profile: { ...baseUserData.profile },
      };

      const mockJobFunction: JobFunction = {
        id: uuidv4(),
        name: 'FRONTEND',
        workspaceId,
      } as JobFunction;

      (jobFunctionRepository.findOrCreate as jest.Mock).mockResolvedValue(mockJobFunction);

      const result = await service.setJobData(userData, workspaceId);

      expect(jobRepository.findOrCreate).not.toHaveBeenCalled();
      expect(jobFunctionRepository.findOrCreate).toHaveBeenCalledTimes(1);
      expect(jobFunctionRepository.findOrCreate).toHaveBeenCalledWith('FRONTEND', workspaceId); // Check for trim and uppercase

      expect(result.profile?.jobPositionId).toBeUndefined();
      expect(result.profile?.jobFunctionId).toBe(mockJobFunction.id);
    });

    it('should not call repositories if job and jobFunction names are not provided', async () => {
      const userData: UserDataDto = {
        ...baseUserData,
        profile: { ...baseUserData.profile },
      };

      const result = await service.setJobData(userData, workspaceId);

      expect(jobRepository.findOrCreate).not.toHaveBeenCalled();
      expect(jobFunctionRepository.findOrCreate).not.toHaveBeenCalled();

      expect(result.profile?.jobPositionId).toBeUndefined();
      expect(result.profile?.jobFunctionId).toBeUndefined();
    });

    it('should not call repositories if job and jobFunction names are empty strings', async () => {
      const userData: UserDataDto = {
        ...baseUserData,
        job: '',
        jobFunction: '',
        profile: { ...baseUserData.profile },
      };

      const result = await service.setJobData(userData, workspaceId);

      expect(jobRepository.findOrCreate).not.toHaveBeenCalled();
      expect(jobFunctionRepository.findOrCreate).not.toHaveBeenCalled();

      expect(result.profile?.jobPositionId).toBeUndefined();
      expect(result.profile?.jobFunctionId).toBeUndefined();
    });

    it('should handle existing profile data without overwriting unrelated fields', async () => {
      const existingProfileData = {
        ...baseUserData.profile,
        id: uuidv4(),
        director: 'Existing Director',
      };
      const userData: UserDataDto = {
        ...baseUserData,
        job: 'Tester',
        jobFunction: 'QA',
        profile: existingProfileData,
      };

      const mockJob: Job = {
        id: uuidv4(),
        name: 'TESTER',
        workspaceId,
      } as Job;
      const mockJobFunction: JobFunction = {
        id: uuidv4(),
        name: 'QA',
        workspaceId,
      } as JobFunction;

      (jobRepository.findOrCreate as jest.Mock).mockResolvedValue(mockJob);
      (jobFunctionRepository.findOrCreate as jest.Mock).mockResolvedValue(mockJobFunction);

      const result = await service.setJobData(userData, workspaceId);

      expect(jobRepository.findOrCreate).toHaveBeenCalledWith('TESTER', workspaceId);
      expect(jobFunctionRepository.findOrCreate).toHaveBeenCalledWith('QA', workspaceId);

      expect(result.profile?.jobPositionId).toBe(mockJob.id);
      expect(result.profile?.jobFunctionId).toBe(mockJobFunction.id);

      expect(result.profile?.id).toBe(existingProfileData.id);
      expect(result.profile?.director).toBe('Existing Director');
    });
  });
});
