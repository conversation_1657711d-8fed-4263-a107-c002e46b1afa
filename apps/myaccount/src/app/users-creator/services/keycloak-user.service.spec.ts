import { Test, TestingModule } from '@nestjs/testing';
import { KeycloakUserService } from './keycloak-user.service';
import { LANG_MAP } from './lang-map';
import { UserDataDto } from '../../users/dtos/create-user.dto';
import { User } from '../../entities/user.entity';
import { KeycloakRepository, KeycloakCreateUserDTO, KeycloakUser, Locale } from '@keeps-node-apis/@core';

describe('KeycloakUserService', () => {
  let service: KeycloakUserService;
  let keycloakRepository: jest.Mocked<KeycloakRepository>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        KeycloakUserService,
        {
          provide: KeycloakRepository,
          useValue: {
            createUser: jest.fn(),
            getUserByEmail: jest.fn(),
            updateUser: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<KeycloakUserService>(KeycloakUserService);
    keycloakRepository = module.get(KeycloakRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createKeycloakUser', () => {
    it('should create a Keycloak user and return it', async () => {
      const userData: UserDataDto = {
        email: '<EMAIL>',
        name: 'Test User',
      };

      const keycloakUser = {
        email: userData.email,
        name: userData.name,
        locale: LANG_MAP[userData.languageId],
      };

      const createdUser = { id: '123', ...keycloakUser };

      keycloakRepository.createUser.mockResolvedValue(undefined);
      keycloakRepository.getUserByEmail.mockResolvedValue(createdUser);

      const result = await service.createKeycloakUser(userData);

      expect(keycloakRepository.createUser).toHaveBeenCalledWith(
        new KeycloakCreateUserDTO(userData.email, userData.name, LANG_MAP[userData.languageId]),
      );
      expect(keycloakRepository.getUserByEmail).toHaveBeenCalledWith(userData.email);
      expect(result).toEqual(createdUser);
    });
  });

  describe('updateKeycloakUser', () => {
    it('should update a Keycloak user', async () => {
      const user = {
        email: '<EMAIL>',
        name: 'Test User',
      } as User;

      const keycloakUser: KeycloakUser = {
        username: user.email,
        email: user.email,
        firstName: user.name,
        attributes: {
          locale: Locale[LANG_MAP[user.languageId]],
        },
      };

      keycloakRepository.updateUser.mockResolvedValue(undefined);

      await service.updateKeycloakUser(user);

      expect(keycloakRepository.updateUser).toHaveBeenCalledWith(user.id, keycloakUser);
    });
  });
});
