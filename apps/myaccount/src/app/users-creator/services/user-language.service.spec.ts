import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserLanguageService } from './user-language.service';
import { LanguagePreference } from '../../entities/language-preference.entity';
import { DEFAULT_USER_LANGUAGE_PREFERENCE } from '../../constants/language.constant';

describe('UserLanguageService', () => {
  let service: UserLanguageService;
  let repository: Repository<LanguagePreference>;

  const mockDefaultLanguage: LanguagePreference = {
    id: 'default-uuid',
    name: DEFAULT_USER_LANGUAGE_PREFERENCE,
    status: true,
    createdDate: new Date(),
    updatedDate: new Date(),
    users: [],
  } as LanguagePreference;

  const mockEnglishLanguage: LanguagePreference = {
    id: 'english-uuid',
    name: 'English',
    status: true,
    createdDate: new Date(),
    updatedDate: new Date(),
    users: [],
  } as LanguagePreference;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserLanguageService,
        {
          provide: getRepositoryToken(LanguagePreference),
          useValue: {
            findOne: jest.fn(),
            findOneOrFail: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<UserLanguageService>(UserLanguageService);
    repository = module.get<Repository<LanguagePreference>>(getRepositoryToken(LanguagePreference));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getLanguagePreferenceId', () => {
    it('should return default language ID when no name is provided', async () => {
      jest.spyOn(repository, 'findOneOrFail').mockResolvedValueOnce(mockDefaultLanguage);

      const result = await service.getLanguagePreferenceId(null);

      expect(repository.findOneOrFail).toHaveBeenCalledWith({
        where: { name: DEFAULT_USER_LANGUAGE_PREFERENCE },
      });
      expect(result).toEqual('default-uuid');
    });

    it('should return language ID when name is provided and mapped correctly', async () => {
      jest.spyOn(repository, 'findOneOrFail').mockResolvedValueOnce(mockEnglishLanguage);

      const result = await service.getLanguagePreferenceId('english');

      expect(repository.findOneOrFail).toHaveBeenCalledWith({
        where: { name: 'en' },
      });
      expect(result).toEqual('english-uuid');
    });

    it('should return language ID when name is provided with different case', async () => {
      jest.spyOn(repository, 'findOneOrFail').mockResolvedValueOnce(mockEnglishLanguage);

      const result = await service.getLanguagePreferenceId('ENGLISH');

      expect(repository.findOneOrFail).toHaveBeenCalledWith({
        where: { name: 'en' },
      });
      expect(result).toEqual('english-uuid');
    });

    it('should use default language when provided name is not in LANGUAGE_MAP', async () => {
      Object.defineProperty(global, 'LANGUAGE_MAP', { value: {} });

      jest.spyOn(repository, 'findOneOrFail').mockResolvedValueOnce(mockDefaultLanguage);

      const result = await service.getLanguagePreferenceId('unknown');

      expect(repository.findOneOrFail).toHaveBeenCalledWith({
        where: { name: DEFAULT_USER_LANGUAGE_PREFERENCE },
      });
      expect(result).toEqual('default-uuid');
    });
  });
});
