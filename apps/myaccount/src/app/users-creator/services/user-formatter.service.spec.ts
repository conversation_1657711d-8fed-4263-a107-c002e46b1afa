import { Test, TestingModule } from '@nestjs/testing';
import { UserFormatterService } from './user-formatter.service';
import { User } from '../../entities/user.entity';
import { UserDataDto } from '../../users/dtos/create-user.dto';
import { InvalidEmailException } from '../../users/exceptions/invalid-email.exception';
import { UsersRepository } from '../../users/repositories/users.repository';

describe('UserFormatterService', () => {
  let service: UserFormatterService;
  let mockUsersRepository: jest.Mocked<Partial<UsersRepository>>;

  beforeEach(async () => {
    mockUsersRepository = {
      findByEmail: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserFormatterService,
        {
          provide: UsersRepository,
          useValue: mockUsersRepository,
        },
      ],
    }).compile();

    service = module.get<UserFormatterService>(UserFormatterService);
  });

  describe('formatUserData', () => {
    it('should format user data correctly', async () => {
      const userData = {
        email: '  <EMAIL>  ',
        cpf: '12345678909',
        phone: '(11) 98765-4321',
        name: '  John Doe  ',
        relatedUserLeader: '<EMAIL>', // Use relatedUserLeader here
      };

      mockUsersRepository.findByEmail.mockResolvedValue({ id: 'leader-id' } as User);

      const result = await service.formatUserData(userData);

      expect(result.email).toBe('<EMAIL>');
      expect(result.cpf).toBe('12345678909');
      expect(result.phone).toBe('11987654321');
      expect(result.name).toBe('John Doe');
      expect(result.relatedUserLeaderId).toBe('leader-id');
    });

    it('should throw InvalidEmailException for invalid email', async () => {
      const userData = {
        email: 'invalid-email',
      } as UserDataDto;

      await expect(service.formatUserData(userData)).rejects.toThrow(InvalidEmailException);
    });

    it('should handle null birthday', async () => {
      const userData = {
        email: '<EMAIL>',
        birthday: null,
      } as UserDataDto;

      const result = await service.formatUserData(userData);

      expect(result.birthday).toBeNull();
    });
  });

  describe('formatPhoneNumber', () => {
    it('should format phone number correctly', () => {
      const phone = '(11) 98765-4321';
      const formattedPhone = service.formatPhoneNumber(phone);

      expect(formattedPhone).toBe('11987654321');
    });

    it('should handle number input', () => {
      const phone = 11987654321;
      const formattedPhone = service.formatPhoneNumber(phone);

      expect(formattedPhone).toBe('11987654321');
    });
  });

  describe('formatGender', () => {
    it('should format gender correctly', () => {
      const gender = 'M';
      const formattedGender = service.formatGender(gender);

      expect(formattedGender).toBe('MALE');
    });

    it('should return undefined for undefined gender', () => {
      const formattedGender = service.formatGender(undefined);

      expect(formattedGender).toBeNull();
    });
  });

  describe('formatRelatedUserLeader', () => {
    it('should return leader id when leader email is provided', async () => {
      const leaderEmail = '<EMAIL>';
      mockUsersRepository.findByEmail.mockResolvedValue({ id: 'leader-id' } as User);

      const result = await service.formatRelatedUserLeader(leaderEmail);

      expect(result).toBe('leader-id');
      expect(mockUsersRepository.findByEmail).toHaveBeenCalledWith(leaderEmail);
    });

    it('should return null when leader email is not found', async () => {
      const leaderEmail = '<EMAIL>';
      mockUsersRepository.findByEmail.mockResolvedValue(null);

      const result = await service.formatRelatedUserLeader(leaderEmail);

      expect(result).toBeNull();
    });

    it('should return the same value when leader id is provided', async () => {
      const leaderId = 'leader-id';

      const result = await service.formatRelatedUserLeader(leaderId);

      expect(result).toBe(null);
      expect(mockUsersRepository.findByEmail).not.toHaveBeenCalled();
    });
  });
});
