import { Test, TestingModule } from '@nestjs/testing';
import { UserIdentityService } from './user-identity.service';
import { IdentityProviderDto } from '../../users/dtos/create-user.dto';
import { KeycloakRepository } from '@keeps-node-apis/@core';

describe('UserIdentityService', () => {
  let service: UserIdentityService;
  let keycloakRepository: jest.Mocked<KeycloakRepository>;

  beforeEach(async () => {
    const mockKeycloakRepository = {
      addFederatedIdentity: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserIdentityService,
        {
          provide: KeycloakRepository,
          useValue: mockKeycloakRepository,
        },
      ],
    }).compile();

    service = module.get<UserIdentityService>(UserIdentityService);
    keycloakRepository = module.get(KeycloakRepository);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('linkIdentityProvider', () => {
    it('should not call repository if provider is null', async () => {
      const userId = 'test-user-id';
      const email = '<EMAIL>';
      const provider = null;

      await service.linkIdentityProvider(userId, email, provider);

      expect(keycloakRepository.addFederatedIdentity).not.toHaveBeenCalled();
    });

    it('should call repository with correct parameters when provider is valid', async () => {
      const userId = 'test-user-id';
      const email = '<EMAIL>';
      const provider: IdentityProviderDto = {
        alias: 'google',
        userId: 'google-user-id',
        userName: 'google-username',
      };

      await service.linkIdentityProvider(userId, email, provider);

      expect(keycloakRepository.addFederatedIdentity).toHaveBeenCalledWith(userId, {
        identityProvider: provider.alias,
        userId,
        userName: email,
      });
    });

    it('should throw error if repository call fails', async () => {
      const userId = 'test-user-id';
      const email = '<EMAIL>';
      const provider: IdentityProviderDto = {
        alias: 'google',
      };
      const expectedError = new Error('Repository error');

      keycloakRepository.addFederatedIdentity.mockRejectedValueOnce(expectedError);

      await expect(service.linkIdentityProvider(userId, email, provider)).rejects.toThrow(expectedError);
    });
  });
});
