import { Injectable } from '@nestjs/common';
import { LANG_MAP } from './lang-map';
import { UserDataDto } from '../../users/dtos/create-user.dto';
import { User } from '../../entities/user.entity';
import { KeycloakCreateUserDTO, KeycloakRepository, KeycloakUser, Locale } from '@keeps-node-apis/@core';

@Injectable()
export class KeycloakUserService {
  constructor(private readonly keycloakRepository: KeycloakRepository) {}

  async createKeycloakUser(userData: UserDataDto): Promise<KeycloakUser> {
    const keycloakUser = new KeycloakCreateUserDTO(userData.email, userData.name, LANG_MAP[userData.languageId]);
    await this.keycloakRepository.createUser(keycloakUser);
    return this.keycloakRepository.getUserByEmail(userData.email);
  }

  async updateKeycloakUser(user: User): Promise<void> {
    const keycloakUser: KeycloakUser = {
      username: user.email,
      email: user.email,
      firstName: user.name,
      attributes: {
        locale: Locale[LANG_MAP[user.languageId]],
      },
    };
    await this.keycloakRepository.updateUser(user.id, keycloakUser);
  }
}
