import { Test, TestingModule } from '@nestjs/testing';
import { WorkspacePermissionService } from './workspace-permission.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';

describe('WorkspacePermissionService', () => {
  let service: WorkspacePermissionService;
  let serviceWorkspaceRepository: jest.Mocked<Repository<ServiceWorkspace>>;

  const mockEnv = {
    MYACCOUNT_DEFAULT_ROLE: 'myaccount_role',
    KONQUEST_ID: 'konquest-id',
    LEARNING_ANALYTICS_ID: 'analytics-id',
    KONQUEST_DEFAULT_ROLE: 'konquest_role',
    ANALYTICS_DEFAULT_ROLE: 'analytics_role',
  };

  beforeEach(async () => {
    const mockServiceWorkspaceRepository = {
      find: jest.fn(),
    };

    process.env = { ...process.env, ...mockEnv };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WorkspacePermissionService,
        {
          provide: getRepositoryToken(ServiceWorkspace),
          useValue: mockServiceWorkspaceRepository,
        },
      ],
    }).compile();

    service = module.get<WorkspacePermissionService>(WorkspacePermissionService);
    serviceWorkspaceRepository = module.get(getRepositoryToken(ServiceWorkspace));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getDefaultPermissions', () => {
    it('should remove MYACCOUNT_DEFAULT_ROLE from permissions', async () => {
      serviceWorkspaceRepository.find.mockResolvedValue([]);
      const initialPermissions = ['permission1', 'myaccount_role', 'permission2'];

      const result = await service.getDefaultPermissions('workspace-id', initialPermissions);

      expect(result).not.toContain('myaccount_role');
      expect(result).toContain('permission1');
      expect(result).toContain('permission2');
    });

    it('should add Konquest role for enabled Konquest service', async () => {
      serviceWorkspaceRepository.find.mockResolvedValue([
        {
          status: true,
          service: {
            applicationId: 'konquest-id',
          },
        } as ServiceWorkspace,
      ]);

      const result = await service.getDefaultPermissions('workspace-id', []);

      expect(result).toContain('konquest_role');
    });

    it('should add Analytics role for enabled Learning Analytics service', async () => {
      serviceWorkspaceRepository.find.mockResolvedValue([
        {
          status: true,
          service: {
            applicationId: 'analytics-id',
          },
        } as ServiceWorkspace,
      ]);

      const result = await service.getDefaultPermissions('workspace-id', []);

      expect(result).toContain('analytics_role');
    });

    it('should handle multiple enabled services', async () => {
      serviceWorkspaceRepository.find.mockResolvedValue([
        {
          status: true,
          service: {
            applicationId: 'konquest-id',
          },
        } as ServiceWorkspace,
        {
          status: true,
          service: {
            applicationId: 'analytics-id',
          },
        } as ServiceWorkspace,
      ]);

      const result = await service.getDefaultPermissions('workspace-id', ['existing_permission']);

      expect(result).toContain('konquest_role');
      expect(result).toContain('analytics_role');
      expect(result).toContain('existing_permission');
    });

    it('should handle services without applicationId', async () => {
      serviceWorkspaceRepository.find.mockResolvedValue([
        {
          status: true,
          service: {},
        } as ServiceWorkspace,
      ]);

      const result = await service.getDefaultPermissions('workspace-id', ['permission1']);

      expect(result).toContain('permission1');
      expect(result).not.toContain('konquest_role');
      expect(result).not.toContain('analytics_role');
    });

    it('should handle missing environment variables', async () => {
      const originalEnv = { ...process.env };
      process.env = {};

      serviceWorkspaceRepository.find.mockResolvedValue([
        {
          status: true,
          service: {
            applicationId: 'konquest-id',
          },
        } as ServiceWorkspace,
      ]);

      const result = await service.getDefaultPermissions('workspace-id', ['permission1']);

      expect(result).toContain('permission1');
      process.env = originalEnv;
    });

    it('should maintain unique permissions', async () => {
      serviceWorkspaceRepository.find.mockResolvedValue([
        {
          status: true,
          service: {
            applicationId: 'konquest-id',
          },
        } as ServiceWorkspace,
      ]);

      const initialPermissions = ['konquest_role', 'permission1', 'konquest_role'];

      const result = await service.getDefaultPermissions('workspace-id', initialPermissions);

      expect(result.filter((p) => p === 'konquest_role')).toHaveLength(1);
    });
  });
});
