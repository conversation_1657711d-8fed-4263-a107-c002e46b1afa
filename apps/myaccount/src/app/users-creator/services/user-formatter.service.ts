import { Injectable } from '@nestjs/common';
import { GENDERS_DICT } from '../../constants/gender.constant';
import { UserDataWithMoreDto } from '../../users/dtos/create-user.dto';
import { InvalidEmailException } from '../../users/exceptions/invalid-email.exception';
import { cleanEmail } from '../../users/utils/email-clean.utils';
import { convertPossibleFloatToString } from '../../users/utils/convert-float.utils';
import { UsersRepository } from '../../users/repositories/users.repository';
import { validate as isUuid } from 'uuid';

@Injectable()
export class UserFormatterService {
  constructor(private readonly userRepository: UsersRepository) {}

  async formatUserData(userData: UserDataWithMoreDto): Promise<UserDataWithMoreDto> {
    const result = { ...userData };

    const cleanedEmail = cleanEmail(result.email);
    if (!cleanedEmail) {
      throw new InvalidEmailException();
    }
    result.email = cleanedEmail.toLowerCase().trim();

    if (result.ein) result.ein = convertPossibleFloatToString(result.ein);

    if (!result.birthday) {
      result.birthday = null;
    }
    if (result.phone) {
      result.phone = this.formatPhoneNumber(result.phone);
    }
    if (result.name) {
      result.name = result.name.trim();
    }

    if (!result.relatedUserLeaderId && result.relatedUserLeader) {
      result.relatedUserLeaderId = await this.formatRelatedUserLeader(result.relatedUserLeader);
      result.relatedUserLeader = null;
    }

    return result;
  }

  formatPhoneNumber(phone: string | number): string {
    const phoneString = String(phone);
    return phoneString.replace(/[(), .-]/g, '');
  }

  formatGender(gender?: string): string | null {
    if (!gender) {
      return null;
    }

    const normalizedGender = gender.toUpperCase();
    return GENDERS_DICT[normalizedGender];
  }

  async formatRelatedUserLeader(relatedUserLeader: string): Promise<string> {
    if (relatedUserLeader.includes('@')) {
      const leader = await this.userRepository.findByEmail(relatedUserLeader);
      if (leader) return leader.id;
    }

    if (isUuid(relatedUserLeader)) {
      return relatedUserLeader;
    }

    return null;
  }
}
