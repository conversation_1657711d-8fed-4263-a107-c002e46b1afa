import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ServiceWorkspace } from '../../entities/service-workspace.entity';

@Injectable()
export class WorkspacePermissionService {
  constructor(
    @InjectRepository(ServiceWorkspace)
    private readonly serviceWorkspaceRepository: Repository<ServiceWorkspace>,
  ) {}

  async getDefaultPermissions(workspaceId: string, permissions: string[]): Promise<string[]> {
    const filteredPermissions = new Set(permissions);
    const { MYACCOUNT_DEFAULT_ROLE } = process.env;

    if (MYACCOUNT_DEFAULT_ROLE) {
      filteredPermissions.delete(MYACCOUNT_DEFAULT_ROLE);
    }

    const enabledServices = await this.serviceWorkspaceRepository.find({
      where: { status: true, workspaceId },
    });

    for (const service of enabledServices) {
      const { KONQUEST_ID, LEARNING_ANALYTICS_ID, KONQUEST_DEFAULT_ROLE, ANALYTICS_DEFAULT_ROLE } = process.env;

      if (service.service?.applicationId === KONQUEST_ID) {
        filteredPermissions.add(KONQUEST_DEFAULT_ROLE);
      }
      if (service.service?.applicationId === LEARNING_ANALYTICS_ID) {
        filteredPermissions.add(ANALYTICS_DEFAULT_ROLE);
      }
    }

    return Array.from(filteredPermissions);
  }
}
