import { Test, TestingModule } from '@nestjs/testing';
import { UserDataService } from './user-data.service';
import { User } from '../../entities/user.entity';

describe('UserDataService', () => {
  let service: UserDataService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UserDataService],
    }).compile();

    service = module.get<UserDataService>(UserDataService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('prepareUserData', () => {
    it('should remove null and undefined values from user data', () => {
      const userData: Partial<User> = {
        name: '<PERSON>',
        email: '<EMAIL>',
        phone: null,
        address: undefined,
      };

      const result = service.prepareUserData(userData);

      expect(result).toEqual({
        name: '<PERSON>',
        email: '<EMAIL>',
      });
      expect(result.phone).toBeUndefined();
      expect(result.address).toBeUndefined();
    });

    it('should format EIN by removing decimal points', () => {
      const userData: Partial<User> = {
        name: 'John Doe',
        ein: '123.456.789',
      };

      const result = service.prepareUserData(userData);

      expect(result.ein).toBe('123');
    });

    it('should keep EIN as is when it does not contain numbers', () => {
      const userData: Partial<User> = {
        name: 'John Doe',
        ein: 'ABC-DEF',
      };

      const result = service.prepareUserData(userData);

      expect(result.ein).toBe('ABC-DEF');
    });

    it('should handle empty object', () => {
      const userData: Partial<User> = {};

      const result = service.prepareUserData(userData);

      expect(result).toEqual({});
    });

    it('should handle object with all null values', () => {
      const userData: Partial<User> = {
        name: null,
        email: null,
        phone: null,
      };

      const result = service.prepareUserData(userData);

      expect(result).toEqual({});
    });

    it('should handle EIN with mixed format', () => {
      const userData: Partial<User> = {
        ein: '123.ABC.456',
      };

      const result = service.prepareUserData(userData);

      expect(result.ein).toBe('123');
    });

    it('should preserve non-null falsy values', () => {
      const userData: Partial<User> = {
        name: '',
        active: false,
        score: 0,
        null_value: null,
      } as any;

      const result = service.prepareUserData(userData);

      expect(result).toEqual({
        name: '',
        active: false,
        score: 0,
      });
    });
  });
});
