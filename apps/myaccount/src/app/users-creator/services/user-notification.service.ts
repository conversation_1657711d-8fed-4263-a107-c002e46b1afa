import { NotificationDto, NotificationService, MobileNotificationStrategy } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';
import { UserRoleWorkspace } from '../../entities/user-role-workspace.entity';
import UserRolesRepository from '../../users/repositories/user-roles.repository';

interface MessageData {
  company_id: string;
  company_logo: string;
  company: string;
  user_email: string;
  user_login: string;
  user_name: string;
  user_phone: string;
  user_password?: string;
  app_web_link: string;
}

@Injectable()
export class UserNotificationService {
  private logger = new Logger(UserNotificationService.name);

  constructor(
    private readonly notificationService: NotificationService,
    private readonly configService: ConfigService,
    private readonly mobileNotificationStrategy: MobileNotificationStrategy,
    private readonly userRoleWorkspaceRepository: UserRolesRepository,
  ) {}

  async notifyUserWithRoles(
    userRoleWorkspaces: UserRoleWorkspace[],
    password?: string,
    isNewUser = true,
  ): Promise<void> {
    if (!userRoleWorkspaces || userRoleWorkspaces.length === 0) {
      return;
    }
    const userRoleWorkspace = await this.userRoleWorkspaceRepository.repository.findOne({
      where: { id: userRoleWorkspaces[0].id },
      relations: ['user', 'role', 'role.application', 'workspace'],
    });
    const { user, workspace } = userRoleWorkspace;
    const KONQUEST_ID = this.configService.get<string>('KONQUEST_ID');
    const SMARTZAP_ADMIN_ROLE = this.configService.get<string>('SMARTZAP_ADMIN_ROLE');
    const hasKonquestRole = userRoleWorkspaces.some((role) => role.role.applicationId === KONQUEST_ID);
    const hasSmartZapAdminRole = userRoleWorkspaces.some((role) => role.role.id === SMARTZAP_ADMIN_ROLE);

    if (!hasKonquestRole && !hasSmartZapAdminRole) {
      return;
    }

    const app_web_link = hasKonquestRole
      ? this.configService.get<string>('KONQUEST_WEB_URL')
      : this.configService.get<string>('SMARTZAP_WEB_URL');

    const messageData: MessageData = {
      company_id: workspace.id,
      company_logo: workspace.logoUrl,
      company: workspace.name,
      user_email: user.email,
      user_login: user.email,
      user_name: user.name,
      user_phone: user.phone,
      app_web_link: app_web_link,
      user_password: password,
    };
    if (isNewUser && password) {
      messageData.user_password = password;
    }
    const language = user.language?.name || 'pt-br';
    const messageType = isNewUser && password ? 'onboarding' : 'invite';
    const emailTemplate = hasKonquestRole ? `konquest_${messageType}.html` : `smartzap_${messageType}.html`;
    try {
      await this.sendNotification(messageData, workspace.id, user.email, emailTemplate, language);
    } catch (error) {
      this.logger.error('Failed to send notification email:', error);
    }
  }

  private async sendNotification(
    data: MessageData,
    workspaceId: string,
    receiverMail: string,
    template: string,
    language: string,
  ): Promise<void> {
    const isSmartZapTemplate = template.startsWith('smartzap_');
    const isOnboarding = template.includes('onboarding');

    const notification: NotificationDto = {
      subject: isSmartZapTemplate ? 'SMARTZAP_ACESS_EMAIL_SUBJECT' : 'KONQUEST_ACESS_EMAIL_SUBJECT',
      template: template,
      workspaceId: workspaceId,
      templateData: data,
      language: language === 'pt-br' ? 'pt-BR' : language,
    };

    await this.notificationService.notifyViaEmail(notification, receiverMail);

    if (data.user_phone) {
      await this.sendMobileNotification(data, workspaceId, language, isSmartZapTemplate, isOnboarding);
    } else {
      this.logger.log('Skipping mobile notification: User has no phone number');
    }
  }

  private async sendMobileNotification(
    data: MessageData,
    workspaceId: string,
    language: string,
    isSmartZapTemplate: boolean,
    isOnboarding: boolean,
  ): Promise<void> {
    try {
      let typeKey = '';
      if (isSmartZapTemplate) {
        typeKey = isOnboarding ? 'MYACCOUNT_SMARTZAP_ONBOARDING' : 'MYACCOUNT_SMARTZAP_INVITE';
      } else {
        typeKey = isOnboarding ? 'MYACCOUNT_KONQUEST_ONBOARDING' : 'MYACCOUNT_KONQUEST_INVITE';
      }
      const contentData: Record<string, string> = {
        '1': data.company,
        '2': data.user_login,
      };

      if (data.user_password) {
        contentData['3'] = data.user_password;
        contentData['4'] = data.app_web_link;
      } else {
        contentData['4'] = data.app_web_link;
      }
      await this.mobileNotificationStrategy.sendNotification(
        typeKey,
        data.user_phone,
        contentData,
        language === 'pt-br' ? 'pt-BR' : language,
        workspaceId,
      );

      this.logger.log(`Mobile notification sent to user with phone: ${data.user_phone}`);
    } catch (error) {
      this.logger.error(`Failed to send mobile notification: ${error.message}`);
    }
  }
}
