import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LanguagePreference } from '../../entities/language-preference.entity';
import { DEFAULT_USER_LANGUAGE_PREFERENCE, LANGUAGE_MAP } from '../../constants/language.constant';

@Injectable()
export class UserLanguageService {
  constructor(
    @InjectRepository(LanguagePreference)
    private readonly languagePreferenceRepository: Repository<LanguagePreference>,
  ) {}

  async getLanguagePreferenceId(name: string): Promise<string> {
    if (!name) {
      const defaultLanguage = await this.languagePreferenceRepository.findOneOrFail({
        where: { name: DEFAULT_USER_LANGUAGE_PREFERENCE },
      });
      return defaultLanguage.id;
    }

    const mappedName = LANGUAGE_MAP[name.toLowerCase()] || DEFAULT_USER_LANGUAGE_PREFERENCE;

    const languagePreference = await this.languagePreferenceRepository.findOneOrFail({
      where: { name: mappedName },
    });

    return languagePreference?.id;
  }
}
