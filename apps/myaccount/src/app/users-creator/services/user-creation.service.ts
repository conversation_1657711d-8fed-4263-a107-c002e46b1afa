import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';

import { PasswordService } from './password.service';
import { UserIdentityService } from './user-identity.service';
import { WorkspacePermissionService } from './workspace-permission.service';
import { UserNotificationService } from './user-notification.service';
import { LANG_MAP } from './lang-map';
import { DEFAULT_TIMEZONE } from '../../constants/timezone.constant';
import { UserDataDto, SaveUserResultDto, UserDataWithMoreDto } from '../../users/dtos/create-user.dto';
import { EmployeeInfoService } from '../../users/services/employee-info.service';
import { UserRolesService } from '../../users/services/user-roles.service';
import { IdpWorkspace } from '../../entities/idp-workspace.entity';
import { Repository } from 'typeorm';
import { UsersRepository } from '../../users/repositories/users.repository';
import { UserFormatterService } from './user-formatter.service';
import { UserLanguageService } from './user-language.service';
import { UserJobService } from './user-job.service';
import { User } from '../../entities/user.entity';
import { KeycloakCreateUserDTO, KeycloakRepository, KeycloakUser, Locale } from '@keeps-node-apis/@core';

@Injectable()
export class UserCreationService {
  constructor(
    private readonly userRepository: UsersRepository,
    private readonly userRolesService: UserRolesService,
    private readonly passwordService: PasswordService,
    private readonly userIdentityService: UserIdentityService,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly keycloakRepository: KeycloakRepository,
    private readonly workspacePermissionService: WorkspacePermissionService,
    private readonly userNotificationService: UserNotificationService,
    private readonly userFormatterService: UserFormatterService,
    private readonly userLanguageService: UserLanguageService,
    private readonly userJobService: UserJobService,
    @InjectRepository(IdpWorkspace)
    private readonly idpWorkspaceRepository: Repository<IdpWorkspace>,
  ) {}

  private formatEin(ein: string): string {
    return /^[0-9.]+/.test(ein) ? ein.split('.')[0] : ein;
  }

  private cleanObject(data: Record<string, any>): Record<string, any> {
    return Object.fromEntries(Object.entries(data).filter(([_, value]) => value != null));
  }

  private prepareUserData(data: Partial<User>): Partial<User> {
    const cleanData = this.cleanObject(data);
    if (cleanData.ein) {
      cleanData.ein = this.formatEin(cleanData.ein);
    }
    return cleanData;
  }

  async upsertUser(
    userData: UserDataWithMoreDto,
    workspaceId: string,
    options: { temporaryPassword?: boolean; selfRegister?: boolean; cleanObject?: boolean } = {},
  ): Promise<SaveUserResultDto> {
    const { temporaryPassword = true, selfRegister = false, cleanObject = false } = options;
    userData = await this.userJobService.setJobData(userData, workspaceId);
    userData = await this.userFormatterService.formatUserData(userData);

    const languageId = await this.getLanguagePreferenceId(userData);

    userData.languageId = languageId;
    const userDataFinal = userData as UserDataDto;
    const {
      job,
      profile: profileDto,
      employeeInfo: employeeInfoDto,
      identityProvider,
      password,
      ...data
    } = userDataFinal;
    const employeeInfo = employeeInfoDto || profileDto;

    const cleanData = cleanObject ? this.prepareUserData(data) : data;

    let user = await this.userRepository.findByEmail(userData.email);
    const finalPassword = password || (!user ? this.passwordService.generatePassword() : null);

    if (!user) {
      user = await this.createNewUser(userData, user, cleanData, languageId, workspaceId, selfRegister);
    } else {
      user = await this.updateUser(user, cleanData);
    }

    if (finalPassword) {
      await this.passwordService.updatePassword(user.id, finalPassword, temporaryPassword);
    }
    if (employeeInfo) {
      employeeInfo.userId = user.id;
      await this.employeeInfoService.upsert(user.id, workspaceId, employeeInfo);
    }
    await this.userIdentityService.linkIdentityProvider(user.id, userData.email, identityProvider);
    return { user, password: finalPassword };
  }

  private async updateUser(user: User, cleanData: Partial<User>) {
    const updatedUser = { ...user, ...cleanData };
    user = await this.userRepository.save(updatedUser as User);
    const keycloakUserData: KeycloakUser = {
      username: updatedUser.email,
      email: updatedUser.email,
      firstName: updatedUser.name,
      attributes: {
        locale: Locale[LANG_MAP[updatedUser.languageId]],
      },
    };
    await this.keycloakRepository.updateUser(user.id, keycloakUserData);
    return user;
  }

  private async createNewUser(
    userData: UserDataWithMoreDto,
    user: User,
    cleanData: Partial<User>,
    languageId: string,
    workspaceId: string,
    selfRegister: boolean,
  ): Promise<User> {
    const keyCloakUserDto = new KeycloakCreateUserDTO(userData.email, userData.name, LANG_MAP[userData.languageId]);
    let keycloakUser = await this.keycloakRepository.getUserByEmail(userData.email);
    if (keycloakUser) {
      await this.keycloakRepository.updateUser(keycloakUser.id, keyCloakUserDto);
    } else {
      await this.keycloakRepository.createUser(keyCloakUserDto);
    }
    keycloakUser = await this.keycloakRepository.getUserByEmail(userData.email);

    user = this.userRepository.create({
      ...cleanData,
      id: keycloakUser.id,
      status: true,
      emailVerified: false,
      timeZone: DEFAULT_TIMEZONE,
      isUserIntegration: false,
      languageId: languageId,
    });
    await this.userRepository.save(user);
    await this.userRolesService.addDefaultRole(workspaceId, user.id, selfRegister);
    return this.userRepository.findById(user.id, workspaceId);
  }

  private async getLanguagePreferenceId(userData: UserDataWithMoreDto) {
    const languageName = userData.language;
    if (userData.languageId) return userData.languageId;
    return this.userLanguageService.getLanguagePreferenceId(languageName);
  }

  async upsertUserWithPermissions(
    userData: UserDataWithMoreDto,
    permissions: string[],
    workspaceId: string,
    cleanObject = false,
  ): Promise<SaveUserResultDto> {
    const isSelfSignup = !!(userData.password || userData.identityProvider);
    const result = await this.upsertUser(userData, workspaceId, {
      selfRegister: isSelfSignup,
      temporaryPassword: !isSelfSignup,
      cleanObject: cleanObject,
    });
    const finalPermissions = await this.workspacePermissionService.getDefaultPermissions(workspaceId, permissions);
    const addedRoles = await this.userRolesService.addRoles(
      workspaceId,
      result.user.id,
      finalPermissions,
      isSelfSignup,
    );
    if (addedRoles.length > 0) {
      await this.userNotificationService.notifyUserWithRoles(addedRoles, result.password, isSelfSignup);
    }
    return { ...result, created: true };
  }
}
