import { Injectable } from '@nestjs/common';
import { User } from '../../entities/user.entity';

@Injectable()
export class UserDataService {
  private formatEin(ein: string): string {
    return /^[0-9.]+/.test(ein) ? ein.split('.')[0] : ein;
  }

  private cleanObject(data: Record<string, any>): Record<string, any> {
    return Object.fromEntries(Object.entries(data).filter(([_, value]) => value != null));
  }

  prepareUserData(data: Partial<User>): Partial<User> {
    const cleanData = this.cleanObject(data);
    if (cleanData.ein) {
      cleanData.ein = this.formatEin(cleanData.ein);
    }
    return cleanData;
  }
}
