import { Test, TestingModule } from '@nestjs/testing';
import { UserBatchService } from './user-batch.service';
import { UserCreationService } from './user-creation.service';
import { Logger } from '@nestjs/common';
import { CreateUsersDto } from '../dtos/create-users.dto';
import { User } from '../../entities/user.entity';

describe('UserBatchService', () => {
  let userBatchService: UserBatchService;
  let userCreatorService: UserCreationService;

  beforeEach(async () => {
    // Suppress error logs from the logger
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => undefined);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserBatchService,
        {
          provide: UserCreationService,
          useValue: {
            upsertUserWithPermissions: jest.fn(),
          },
        },
        {
          provide: Logger,
          useValue: {
            log: jest.fn(),
            error: jest.fn(),
          },
        },
      ],
    }).compile();

    userBatchService = module.get<UserBatchService>(UserBatchService);
    userCreatorService = module.get<UserCreationService>(UserCreationService);
  });

  describe('saveUsers', () => {
    it('should save users successfully and return results', async () => {
      const createUsersDto: CreateUsersDto = {
        users: [
          { email: '<EMAIL>', name: 'User One' },
          { email: '<EMAIL>', name: 'User Two' },
        ],
        permissions: ['permission1', 'permission2'],
      };

      const mockUser = { id: '1', email: '<EMAIL>', name: 'User One' } as User;
      jest.spyOn(userCreatorService, 'upsertUserWithPermissions').mockResolvedValue({ user: mockUser, created: true });

      const results = await userBatchService.saveUsers(createUsersDto, 'workspaceId');

      expect(results).toHaveLength(2);
      expect(results[0].id).toEqual(mockUser.id);
      expect(results[1].id).toEqual(mockUser.id);
    });

    it('should handle errors when saving users and return results with errors', async () => {
      const createUsersDto: CreateUsersDto = {
        users: [
          { email: '<EMAIL>', name: 'User One' },
          { email: '<EMAIL>', name: 'User Two' },
        ],
        permissions: ['permission1', 'permission2'],
      };

      const mockError = new Error('Failed to save user');
      jest.spyOn(userCreatorService, 'upsertUserWithPermissions').mockRejectedValue(mockError);

      const results = await userBatchService.saveUsers(createUsersDto, 'workspaceId');

      expect(results).toHaveLength(2);
      expect(results[0].error).toBe(mockError.message);
      expect(results[1].error).toBe(mockError.message);
    });
  });
});
