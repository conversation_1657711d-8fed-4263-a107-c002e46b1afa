import { Injectable } from '@nestjs/common';
import { IdentityProviderDto } from '../../users/dtos/create-user.dto';
import { KeycloakRepository } from '@keeps-node-apis/@core';

@Injectable()
export class UserIdentityService {
  constructor(private readonly keycloakRepository: KeycloakRepository) {}

  async linkIdentityProvider(userId: string, email: string, provider: IdentityProviderDto): Promise<void> {
    if (!provider) return;

    await this.keycloakRepository.addFederatedIdentity(userId, {
      identityProvider: provider.alias,
      userId,
      userName: email,
    });
  }
}
