CRYPTO_SECRET_KEY=AAAAAAAAAAAAAAAA
AUTH_URL=https://iam.keepsdev.com/auth
AUTH_REALM=keeps-dev
AUTH_CLIENT_ID=myaccount
AUTH_CLIENT_SECRET=
AUTH_REALM_PUBLIC_KEY=
DB_HOST=rds-postgres-stage.cpd3dmaosiyq.us-east-1.rds.amazonaws.com
DB_PORT=5432
DB_USER=postgres
DB_PASS=
DB_NAME=myaccount_dev_db

RABBITMQ_BROKER_URL=amqp://admin:admin@localhost:5672
QUEUE_SMARTZAP_COMPANY=smartzap-companies-stage
RABBITMQ_MOBILE_NOTIFICATIONS_QUEUE=mobile
MOBILE_NOTIFICATIONS_QUEUE_IS_DURABLE=true

NOTIFICATION_API_URL=https://learning-platform-api-stage.keepsdev.com/notification

MYACCOUNT_ID=ad7e5ad2-1552-43ab-a471-710954f0e66a
KONQUEST_ID=0abf08ea-d252-4d7c-ab45-ab3f9135c288
SMARTZAP_ID=84d6715e-9b75-436d-ad44-b74c5a7f6729
ANALYTICS_ID=c2928f23-a5a6-4f59-94a7-7e409cf1d4f4
SMARTZAP_ADMIN_ROLE=3d010792-7119-4e14-bea3-5258a31f1ddc
SMARTZAP_DEFAULT_ROLE=********-5e4e-48c6-91d7-dbeb360c7205

I18N_PATH=assets/i18n

KONQUEST_WEB_URL=https://konquest-stage.keepsdev.com/
SMARTZAP_WEB_URL=https://smartzap-stage.keepsdev.com/
MYACCOUNT_WEB_URL=https://myaccount-stage.keepsdev.com/

# AWS S3 Configuration
# Required variables - the system will not start without these
AWS_S3_ACCESS_KEY_ID=                  # Required
AWS_S3_SECRET_ACCESS_KEY=              # Required
AWS_S3_REGION=us-east-1                # Required

# Optional S3 variables - defaults will be used if not provided
AWS_S3_BUCKET=keeps-media-stage
AWS_S3_BUCKET_PATH=myaccount
AWS_S3_CDN_URL=https://media-stage.keepsdev.com

# Avatar Configuration
AVATAR_ALLOWED_TYPES=image/png,image/jpeg
AVATAR_MAX_SIZE_BYTES=5242880
AVATAR_DEFAULT_SIZE=200
AVATAR_IMAGE_QUALITY=80
AVATAR_IMAGE_FORMAT=png


# RABBIT
RABBITMQ_URI=amqp://admin:admin@localhost:5672
RABBITMQ_BELL_NOTIFICATIONS_QUEUE=bell
BELL_NOTIFICATIONS_QUEUE_IS_DURABLE=true

# REDIS
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=