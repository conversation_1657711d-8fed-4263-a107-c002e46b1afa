import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  nodeEnv: process.env.NODE_ENV,
  port: parseInt(process.env.PORT),
  konquestId: process.env.KONQUEST_ID,
  analyticsId: process.env.ANALYTICS_ID,
  redisHost: process.env.REDIS_HOST,
  redisPort: process.env.REDIS_PORT,
  redistTTLMs: parseInt(process.env.REDIS_TTL_MS),
  myAccountApiUrl: process.env.MYACCOUNT_API_URL,
}));
