import Joi from 'joi';

export default Joi.object({
  // app
  NODE_ENV: Joi.string().valid('development', 'stage', 'production').required(),
  ENV_SUFFIX: Joi.string().default('stage'),
  PORT: Joi.number().default(3000),
  KONQUEST_ID: Joi.string().default('0abf08ea-d252-4d7c-ab45-ab3f9135c288'),
  ANALYTICS_ID: Joi.string().default('c2928f23-a5a6-4f59-94a7-7e409cf1d4f4'),
  MYACCOUNT_API_URL: Joi.string().uri().default('https://learning-platform-api-stage.keepsdev.com/myaccount-v2'),

  // auth
  AUTH_URL: Joi.string().default('https://iam.keepsdev.com/auth/'),
  AUTH_REALM: Joi.string().valid('keeps-dev', 'keeps').required(),
  AUTH_CLIENT_ID: Joi.string().default('keeps-search-api-stage'),
  AUTH_CLIENT_SECRET: Joi.string().required(),
  AUTH_REALM_PUBLIC_KEY: Joi.string().required(),
  AUTH_DEBUG: Joi.boolean().default(false),

  // elastic server
  ELASTICSEARCH_SERVER: Joi.string().uri().default('https://keeps.es.us-east-1.aws.found.io'),
  ELASTICSEARCH_USER: Joi.string().default('elastic'),
  ELASTICSEARCH_PASSWORD: Joi.string().required(),

  // redis
  REDIS_HOST: Joi.string().default('redis'),
  REDIS_PORT: Joi.number().default(6379),
  REDIS_TTL_MS: Joi.number().default(10000),
});
