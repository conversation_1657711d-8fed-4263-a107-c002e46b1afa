import { ConfigModuleOptions } from '@nestjs/config';
import configSchema from './config.schema';
import appConfig from './modules/app.config';
import authConfig from './modules/auth.config';
import elasticConfig from './modules/elastic.config';

export { default as AppConfig } from './modules/app.config';
export { default as AuthConfig } from './modules/auth.config';
export { default as ElasticConfig } from './modules/elastic.config';

export const configOptions: ConfigModuleOptions = {
  isGlobal: true,
  ignoreEnvFile: true,
  cache: true,
  validationOptions: {
    allowUnknown: true,
    abortEarly: true,
  },
  validationSchema: configSchema,
  load: [appConfig, authConfig, elasticConfig],
};
