import {
  buildConfig,
  certificateElementStyleHelper,
  elementVisible,
  numberToPx,
  paginateContents,
} from './certificate-helpers';
import {
  CERTIFICATE_ELEMENT_TYPE,
  CertificateConfig,
  CertificateElementsStyling,
  CertificateElementStyling,
  CertificateTemplate,
} from '../models';
import { DEFAULT_MISSION_CONFIG, DEFAULT_TRAIL_CONFIG } from '../default-certificate-configs';
import { Certificate } from '../entities/certificate.entity';
import { Chance } from 'chance';
import { CertificateElement } from '../entities/certificate-element.entity';
import { HelperOptions } from 'handlebars';

describe('CertificateHelpers', () => {
  const chance = new Chance();

  describe('paginateContents', () => {
    it('should break the course contents into 15 elements arrays', () => {
      const contents = [...Array(50).keys()];
      const result = paginateContents<number>(contents);

      expect(result.length).toBe(4);
      expect(result.at(3).length).toBe(5);
    });

    it('should return an empty array if there are no elements', () => {
      expect(paginateContents(null).length).toBe(0);
    });
  });

  describe('certificateElementStyleHelper', () => {
    it('should return a style string for a html element with the left and top positions', () => {
      const elementConfig: CertificateElementStyling = {
        position: { x: 0.5, y: 0.5 },
        textColor: '#00FFDD',
      };

      const expectedStyle = 'style="left: 50%; top: 50%; color: #00FFDD"';

      expect(certificateElementStyleHelper(elementConfig)).toEqual(expectedStyle);
    });
  });

  describe('numberToPx', () => {
    it('should return the provided value as a pixel css value string', () => {
      const expectedValue = '101px';

      expect(numberToPx(101)).toEqual(expectedValue);
    });
  });

  describe('buildConfig', () => {
    it('should return the missions certificate default configuration using the override values', () => {
      const mockCertificate = {
        backgroundColor: '#E1E1E1',
        textColor: '#000',
        brandImage: chance.url(),
        backgroundImage: chance.url(),
        elements: [],
      } as Certificate;

      const expectedConfig: CertificateConfig = {
        ...DEFAULT_MISSION_CONFIG,
        backgroundImageUrl: mockCertificate.backgroundImage,
        backgroundColor: mockCertificate.backgroundColor,
        textColor: mockCertificate.textColor,
        brandImageUrl: mockCertificate.brandImage,
      };

      const result = buildConfig(CertificateTemplate.MISSION, false, 'pt-BR', mockCertificate);

      expect(result).toMatchObject(expectedConfig);
    });

    it('should return the trails certificate default configuration using the override values', () => {
      const mockCertificate = {
        backgroundColor: '#E1E1E1',
        textColor: '#000',
        brandImage: chance.url(),
        backgroundImage: chance.url(),
      } as Certificate;

      const expectedConfig: CertificateConfig = {
        ...DEFAULT_TRAIL_CONFIG,
        backgroundImageUrl: mockCertificate.backgroundImage,
        backgroundColor: mockCertificate.backgroundColor,
        textColor: mockCertificate.textColor,
        brandImageUrl: mockCertificate.brandImage,
      };
      const result = buildConfig(CertificateTemplate.TRAIL, false, 'pt-BR', mockCertificate);

      expect(result).toMatchObject(expectedConfig);
    });

    it('should return the configuration if no certificate data is provided', () => {
      const result = buildConfig();

      expect(result).toMatchObject(DEFAULT_MISSION_CONFIG);
    });

    it('should use the certificate elements position', () => {
      const certificateElements = [
        {
          type: CERTIFICATE_ELEMENT_TYPE.LOGO,
          yPos: 1,
          xPos: 1,
        },
        { type: CERTIFICATE_ELEMENT_TYPE.TITLE, yPos: 1, xPos: 1 },
        {
          type: CERTIFICATE_ELEMENT_TYPE.COURSE_NAME,
          yPos: 1,
          xPos: 1,
        },
      ] as CertificateElement[];
      const mockCertificate = {
        backgroundColor: '#E1E1E1',
        textColor: '#000',
        brandImage: chance.url(),
        backgroundImage: chance.url(),
        elements: certificateElements,
      } as Certificate;

      const expectedElementsConfig = {
        logo: { position: { x: 1, y: 1 } },
        title: {
          position: {
            x: 1,
            y: 1,
          },
        },
        courseName: {
          position: {
            x: 1,
            y: 1,
          },
        },
      } as CertificateElementsStyling;

      const expectedConfig: CertificateConfig = {
        ...DEFAULT_TRAIL_CONFIG,
        backgroundImageUrl: mockCertificate.backgroundImage,
        backgroundColor: mockCertificate.backgroundColor,
        textColor: mockCertificate.textColor,
        brandImageUrl: mockCertificate.brandImage,
        elementsConfig: expectedElementsConfig,
      };
      const result = buildConfig(CertificateTemplate.TRAIL, false, 'pt-BR', mockCertificate);

      expect(result).toMatchObject(expectedConfig);
    });

    it('should return the correct background image based on the provided language', () => {
      const mockCertificate = {
        backgroundImageEs: chance.url(),
        elements: [],
      } as Certificate;

      const expectedConfig: CertificateConfig = {
        ...DEFAULT_MISSION_CONFIG,
        backgroundImageUrl: mockCertificate.backgroundImageEs,
      };

      const result = buildConfig(CertificateTemplate.MISSION, false, 'es', mockCertificate);

      expect(result).toMatchObject(expectedConfig);
    });

    it('should return the default backgroundImage if there is none for the specified language', () => {
      const mockCertificate = {
        backgroundImage: chance.url(),
        elements: [],
      } as Certificate;

      const expectedConfig: CertificateConfig = {
        ...DEFAULT_MISSION_CONFIG,
        backgroundImageUrl: mockCertificate.backgroundImage,
      };

      const result = buildConfig(CertificateTemplate.MISSION, false, 'en', mockCertificate);

      expect(result).toMatchObject(expectedConfig);
    });
  });

  describe('elementVisible', () => {
    it('should render the element when hideLabels is false and the element is not hidden', () => {
      const handlebarsRenderFn = jest.fn();
      const mockConfig = {
        elementsConfig: {
          [CERTIFICATE_ELEMENT_TYPE.LOGO]: { hidden: false },
        },
        hideLabels: false,
      } as CertificateConfig;

      elementVisible(mockConfig, CERTIFICATE_ELEMENT_TYPE.LOGO, { fn: handlebarsRenderFn } as unknown as HelperOptions);

      expect(handlebarsRenderFn).toHaveBeenCalled();
    });

    it('should not render the element when hideLabels is true', () => {
      const handlebarsRenderFn = jest.fn();
      const mockConfig = {
        elementsConfig: {
          [CERTIFICATE_ELEMENT_TYPE.TITLE]: { hidden: false },
        },
        hideLabels: true,
      } as CertificateConfig;

      elementVisible(mockConfig, CERTIFICATE_ELEMENT_TYPE.TITLE, {
        fn: handlebarsRenderFn,
      } as unknown as HelperOptions);

      expect(handlebarsRenderFn).not.toHaveBeenCalled();
    });

    it('should not render the element when it is hidden in the configuration', () => {
      const handlebarsRenderFn = jest.fn();
      const mockConfig = {
        elementsConfig: {
          [CERTIFICATE_ELEMENT_TYPE.COURSE_NAME]: { hidden: true },
        },
        hideLabels: false,
      } as CertificateConfig;

      elementVisible(mockConfig, CERTIFICATE_ELEMENT_TYPE.COURSE_NAME, {
        fn: handlebarsRenderFn,
      } as unknown as HelperOptions);

      expect(handlebarsRenderFn).not.toHaveBeenCalled();
    });
  });
});
