import {
  CERTIFICATE_ELEMENT_TYPE,
  CertificateConfig,
  CertificateElementsStyling,
  CertificateElementStyling,
  CertificateTemplate,
} from '../models';
import {
  DEFAULT_ELEMENTS_STYLING,
  DEFAULT_MISSION_CONFIG,
  DEFAULT_TRAIL_CONFIG,
  MISSION_PORTRAIT_CONFIG,
  TRAIL_PORTRAIT_CONFIG,
} from '../default-certificate-configs';
import { Certificate } from '../entities/certificate.entity';
import { HelperOptions } from 'handlebars';

export function paginateContents<T>(elements: T[], elementsPerPage = 15) {
  if (!elements?.length) {
    return [];
  }

  const totalContents = elements.length;
  const pages: T[][] = [];

  for (let i = 0; i < totalContents; i += elementsPerPage) {
    pages.push(elements.slice(i, i + elementsPerPage));
  }

  return pages;
}

export function certificateElementStyleHelper(elementStyling: CertificateElementStyling): string {
  const { x, y } = elementStyling.position;
  const styleRules = [`left: ${x * 100}%`, `top: ${y * 100}%`];

  if (elementStyling.textColor) {
    styleRules.push(`color: ${elementStyling.textColor}`);
  }

  return `style="${styleRules.join('; ')}"`;
}

export function numberToPx(value: number) {
  return `${value}px`;
}

export function elementVisible(
  config: CertificateConfig,
  elementType: CERTIFICATE_ELEMENT_TYPE,
  options: HelperOptions,
) {
  const elementConfig = config.elementsConfig[elementType];
  if (!config.hideLabels && !elementConfig?.hidden) {
    return options.fn(this);
  }
}

export function buildConfig(
  template: CertificateTemplate = CertificateTemplate.MISSION,
  portrait = false,
  language = 'pt-BR',
  certificate?: Certificate,
): CertificateConfig {
  const missionConfig = portrait ? MISSION_PORTRAIT_CONFIG : DEFAULT_MISSION_CONFIG;
  const trailConfig = portrait ? TRAIL_PORTRAIT_CONFIG : DEFAULT_TRAIL_CONFIG;
  const elementsStyling = parseCertificateElementsStyling(certificate);

  const configMap: Record<CertificateTemplate, CertificateConfig> = {
    mission: missionConfig,
    trail: trailConfig,
  };

  const config = configMap[template];
  const backgroundImageUrl = getCertificateBackgroundImage(language, certificate) || config.backgroundImageUrl;
  const textColor = certificate?.textColor || config.textColor;
  const backgroundColor = certificate?.backgroundColor || config.backgroundColor;
  const brandImageUrl = certificate?.brandImage || '';
  const hideLabels = certificate?.hideLabels;
  const displayPerformance = certificate?.displayPerformance ?? true;
  const displayConclusionDate = certificate?.displayConclusionDate ?? true;
  const displayTotalTime = certificate?.displayTotalTime ?? true;

  return {
    ...config,
    backgroundImageUrl,
    textColor,
    backgroundColor,
    brandImageUrl,
    elementsConfig: elementsStyling,
    hideLabels,
    displayPerformance,
    displayConclusionDate,
    displayTotalTime,
  };
}

function getCertificateBackgroundImage(language: string, certificate?: Certificate): string {
  const imageByLanguage: Record<string, string> = {
    es: certificate?.backgroundImageEs,
    en: certificate?.backgroundImageEn,
  };

  return imageByLanguage[language] || certificate?.backgroundImage;
}

function parseCertificateElementsStyling(certificate: Certificate): CertificateElementsStyling | undefined {
  if (!certificate?.elements?.length) {
    return DEFAULT_ELEMENTS_STYLING;
  }

  const elements = certificate.elements;
  return elements.reduce(
    (previousValue, currentValue) => {
      previousValue[currentValue.type] = {
        position: { x: currentValue.xPos, y: currentValue.yPos },
        textColor: currentValue.textColor,
        hidden: currentValue.hidden,
      };
      return previousValue;
    },
    { ...DEFAULT_ELEMENTS_STYLING } as CertificateElementsStyling,
  );
}
