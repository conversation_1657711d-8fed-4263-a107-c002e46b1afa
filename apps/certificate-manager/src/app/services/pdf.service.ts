import { Injectable } from '@nestjs/common';
import { TemplateCompilerService } from '@template-compiler';
import { buildConfig, numberToPx, paginateContents, certificateElementStyleHelper, elementVisible } from '../helpers';
import { CertificateGenerateDto, CertificateGenerateResponseDto, CourseContentsDto } from '../dtos';
import { CertificateConfig, CertificateOrientation, CertificateTemplate, PrintCertificateData } from '../models';
import puppeteer from 'puppeteer';
import { CertificatesService } from './certificates.service';
import { Certificate } from '../entities/certificate.entity';
import { S3Uploader } from '@keeps-node-apis/@core';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class PdfService {
  constructor(
    private readonly templateCompilerService: TemplateCompilerService,
    private readonly certificatesService: CertificatesService,
    private readonly s3Uploader: S3Uploader,
  ) {
    this.templateCompilerService.registerHelper('elementStyle', certificateElementStyleHelper);
    this.templateCompilerService.registerHelper('numberToPx', numberToPx);
    this.templateCompilerService.registerHelper('elementVisible', elementVisible);
  }

  async generateCertificatePdf(
    certificateGenerateDto: CertificateGenerateDto,
  ): Promise<CertificateGenerateResponseDto> {
    const { learn_content_id, language, workspace_id } = certificateGenerateDto;
    const template = CertificateGenerateDto.getCertificateTemplate(certificateGenerateDto);
    const certificate = await this.certificatesService.getCertificateForPDFCreation(
      learn_content_id,
      workspace_id,
      template,
    );
    const certificatePrintData = this.getCertificatePrintData(certificateGenerateDto, template, language, certificate);
    const htmlContent = await this.templateCompilerService.execute(template, certificatePrintData, language);
    const certificateBuffer = await this.convertHtmlToPdf(htmlContent, certificatePrintData.config);
    const url = await this.uploadCertificate(certificateBuffer, workspace_id);
    return { url };
  }

  private getCertificatePrintData(
    certificateGenerateDto: CertificateGenerateDto,
    template: CertificateTemplate,
    language: string,
    certificate?: Certificate,
  ): PrintCertificateData & CertificateGenerateDto {
    const portrait = certificate?.orientation === CertificateOrientation.PORTRAIT;
    return {
      ...certificateGenerateDto,
      paginatedContent: paginateContents<CourseContentsDto>(certificateGenerateDto.contents, portrait ? 16 : 15),
      config: buildConfig(template, portrait, language, certificate),
    };
  }

  private async convertHtmlToPdf(htmlContent: string, config: CertificateConfig) {
    const browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox'],
      defaultViewport: { width: config.width, height: config.height },
    });
    const page = await browser.newPage();
    await page.setContent(htmlContent);
    const buffer = await page.pdf({
      width: config.width,
      height: config.height,
      printBackground: true,
      margin: {
        left: '0px',
        top: '0px',
        right: '0px',
        bottom: '0px',
      },
    });
    await page.close();
    await browser.close();
    return buffer;
  }

  private async uploadCertificate(certificateBuffer: Buffer, workspaceId: string) {
    const fileName = uuidv4();
    return await this.s3Uploader.uploadFile(`${workspaceId}/certificates/${fileName}`, certificateBuffer, 'PDF');
  }
}
