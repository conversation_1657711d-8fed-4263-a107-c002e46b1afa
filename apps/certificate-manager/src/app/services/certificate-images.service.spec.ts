import { CertificateImagesService } from './certificate-images.service';
import { S3Uploader } from '@keeps-node-apis/@core';
import { CertificateOrientation } from '../models';
import { Chance } from 'chance';
import { ImageProcessorService } from './image-processor.service';

describe('CertificateImagesService', () => {
  const chance = new Chance();
  const workspaceId = chance.guid();
  let service: CertificateImagesService;
  let s3Uploader: jest.Mocked<S3Uploader>;
  let imageProcessor: jest.Mocked<ImageProcessorService>;

  beforeEach(async () => {
    s3Uploader = { uploadFile: jest.fn() } as unknown as jest.Mocked<S3Uploader>;
    imageProcessor = {
      processImage: jest.fn().mockResolvedValue(Buffer.from('mock-buffer')),
    } as any;

    service = new CertificateImagesService(s3Uploader, imageProcessor);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('resolveBackgroundAndThumbnail', () => {
    it('should return the default background image and thumbnail when no file is provided', async () => {
      const landscapeImages = await service.resolveBackgroundAndThumbnail(
        CertificateOrientation.LANDSCAPE,
        workspaceId,
      );
      const portraitImages = await service.resolveBackgroundAndThumbnail(CertificateOrientation.PORTRAIT, workspaceId);

      expect(landscapeImages.backgroundImage).toEqual(
        'https://media.keepsdev.com/certificate-manager/default-images/landscape.png',
      );
      expect(landscapeImages.thumbnail).toEqual(
        'https://media.keepsdev.com/certificate-manager/default-images/landscape_thumb.avif',
      );

      expect(portraitImages.backgroundImage).toEqual(
        'https://media.keepsdev.com/certificate-manager/default-images/portrait.png',
      );
      expect(portraitImages.thumbnail).toEqual(
        'https://media.keepsdev.com/certificate-manager/default-images/portrait_thumb.avif',
      );
    });

    it('should store and return the urls for both the background image and its thumbnail', async () => {
      const file = {
        buffer: Buffer.from('mock-buffer'),
      } as Express.Multer.File;
      s3Uploader.uploadFile.mockResolvedValueOnce('background-image-url');
      s3Uploader.uploadFile.mockResolvedValueOnce('thumbnail-image-url');

      const result = await service.resolveBackgroundAndThumbnail(CertificateOrientation.LANDSCAPE, workspaceId, file);

      expect(imageProcessor.processImage).toHaveBeenCalledWith(file.buffer, { width: 1920, height: 1022 });
      expect(imageProcessor.processImage).toHaveBeenCalledWith(file.buffer, { width: 480, height: 255 });
      expect(s3Uploader.uploadFile).toHaveBeenCalledWith(
        expect.stringContaining(`${workspaceId}/`),
        file.buffer,
        'AVIF',
      );
      expect(s3Uploader.uploadFile).toHaveBeenCalledTimes(2);
      expect(result.backgroundImage).toEqual('background-image-url');
      expect(result.thumbnail).toEqual('thumbnail-image-url');
    });

    it('should use the correct image dimensions for portrait certificates', async () => {
      const file = {
        buffer: Buffer.from('mock-buffer'),
      } as Express.Multer.File;

      await service.resolveBackgroundAndThumbnail(CertificateOrientation.PORTRAIT, workspaceId, file);

      expect(imageProcessor.processImage).toHaveBeenCalledWith(file.buffer, { width: 1022, height: 1920 });
      expect(imageProcessor.processImage).toHaveBeenCalledWith(file.buffer, { width: 255, height: 480 });
    });
  });

  describe('resolveBrandImage', () => {
    it('should return brand image url', async () => {
      const file = {
        buffer: Buffer.from('mock-buffer'),
      } as Express.Multer.File;
      s3Uploader.uploadFile.mockResolvedValueOnce('brand-image-url');

      const emptyBrandImageResult = await service.resolveBrandImage(workspaceId);
      const brandImageUrl = await service.resolveBrandImage(workspaceId, file);

      expect(emptyBrandImageResult).toBeUndefined();
      expect(s3Uploader.uploadFile).toHaveBeenCalledWith(
        expect.stringContaining(`${workspaceId}/`),
        file.buffer,
        'AVIF',
      );
      expect(brandImageUrl).toEqual('brand-image-url');
    });
  });
});
