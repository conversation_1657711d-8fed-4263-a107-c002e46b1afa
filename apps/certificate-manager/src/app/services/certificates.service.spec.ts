import { CertificatesService } from './certificates.service';
import { CertificateLearnContentRepository, CertificatesRepository } from '../repositories';
import { CertificateCreateDto, CertificateItemDto, VinculateCertificateDto } from '../dtos';
import { CertificateOrientation, CertificateTemplate } from '../models';
import { PageDto } from '@keeps-node-apis/@core';
import { Chance } from 'chance';
import { Certificate } from '../entities/certificate.entity';
import { CertificateLearnContent } from '../entities/certificate-learn-content.entity';
import { CertificateImagesService } from './certificate-images.service';
import { PaginateQuery } from 'nestjs-paginate';
import { LearnContentCertificateNotFoundException } from '../exceptions/learn-content-certificate-not-found.exception';

describe('CertificateService', () => {
  let service: CertificatesService;
  let certificateRepository: jest.Mocked<CertificatesRepository>;
  let certificateLearnContentRepository: jest.Mocked<CertificateLearnContentRepository>;
  let certificateImagesService: jest.Mocked<CertificateImagesService>;

  const chance = new Chance();
  const workspaceId = chance.guid();

  beforeEach(async () => {
    certificateRepository = {
      add: jest.fn(),
      getById: jest.fn().mockResolvedValue(() => ({})),
      update: jest.fn(),
      delete: jest.fn(),
      list: jest.fn().mockResolvedValue(new PageDto<CertificateItemDto>([], null, 10)),
      batchSave: jest.fn().mockImplementation((changes: any[]) => changes),
      findOneBy: jest.fn(),
      getLatestCreated: jest.fn(),
      getDefaultCertificate: jest.fn(),
    };
    certificateLearnContentRepository = {
      add: jest.fn(),
      delete: jest.fn(),
      getById: jest.fn(),
    };

    certificateImagesService = {
      resolveBrandImage: jest.fn(),
      resolveBackgroundAndThumbnail: jest.fn().mockResolvedValue({
        backgroundImage: 'default-bg-image-url',
        thumbnail: 'default-thumb-image-url',
      }),
    } as unknown as jest.Mocked<CertificateImagesService>;

    service = new CertificatesService(
      certificateRepository,
      certificateLearnContentRepository,
      certificateImagesService,
    );
  });

  it('should add a new certificate', async () => {
    const input: CertificateCreateDto = {
      name: 'mock_certificate',
      template: CertificateTemplate.MISSION,
      orientation: CertificateOrientation.PORTRAIT,
      default: true,
    };

    await service.createCertificate(input);

    expect(certificateRepository.add).toHaveBeenCalledWith({
      ...input,
      backgroundImage: 'default-bg-image-url',
      thumbnail: 'default-thumb-image-url',
      brandImage: undefined,
    });
  });

  it('should create a new certificate and upload the provided background image', async () => {
    const input: CertificateCreateDto = {
      name: 'mock_certificate',
      template: CertificateTemplate.MISSION,
      orientation: CertificateOrientation.PORTRAIT,
      workspaceId: workspaceId,
      default: true,
    };
    const mockFile = {
      buffer: Buffer.from('mock-content'),
    } as Express.Multer.File;
    certificateImagesService.resolveBackgroundAndThumbnail.mockResolvedValueOnce({
      backgroundImage: 'bg-image-url',
      thumbnail: 'thumb-image-url',
    });

    await service.createCertificate(input, mockFile);

    expect(certificateImagesService.resolveBackgroundAndThumbnail).toHaveBeenCalledWith(
      input.orientation,
      input.workspaceId,
      mockFile,
    );
    expect(certificateRepository.add).toHaveBeenCalledWith({
      ...input,
      backgroundImage: 'bg-image-url',
      thumbnail: 'thumb-image-url',
      brandImage: undefined,
    });
  });

  it('should update a certificate', async () => {
    const input: CertificateCreateDto = {
      name: 'mock_certificate',
      template: CertificateTemplate.MISSION,
      orientation: CertificateOrientation.PORTRAIT,
    };
    const existingCertificate = {
      orientation: CertificateOrientation.PORTRAIT,
      template: CertificateTemplate.MISSION,
      backgroundImage: 'default-bg-image-url',
      thumbnail: 'default-thumb-image-url',
      brandImage: undefined,
    } as Certificate;
    certificateRepository.getById.mockResolvedValueOnce(existingCertificate);

    await service.updateCertificate('certificateId', workspaceId, input);

    expect(certificateRepository.update).toHaveBeenCalledWith('certificateId', workspaceId, input);
  });

  it('should update a certificate with background image', async () => {
    const input: CertificateCreateDto = {
      name: 'mock_certificate',
      template: CertificateTemplate.MISSION,
      orientation: CertificateOrientation.PORTRAIT,
    };
    const mockFile = {
      buffer: Buffer.from('mock-content'),
    } as Express.Multer.File;
    const existingCertificate = {
      orientation: CertificateOrientation.PORTRAIT,
      template: CertificateTemplate.MISSION,
      backgroundImage: 'default-bg-image-url',
      thumbnail: 'default-thumb-image-url',
      brandImage: undefined,
    } as Certificate;

    certificateRepository.getById.mockResolvedValueOnce(existingCertificate);
    certificateImagesService.resolveBackgroundAndThumbnail.mockResolvedValueOnce({
      backgroundImage: 'new-bg-image-url',
      thumbnail: 'new-thumb-image-url',
    });

    await service.updateCertificate('certificateId', workspaceId, input, mockFile);

    expect(certificateRepository.update).toHaveBeenCalledWith('certificateId', workspaceId, {
      ...input,
      backgroundImage: 'new-bg-image-url',
      thumbnail: 'new-thumb-image-url',
      brandImage: undefined,
    });
  });

  it('should update a certificate with brand image', async () => {
    const input: CertificateCreateDto = {
      name: 'mock_certificate',
      template: CertificateTemplate.MISSION,
      orientation: CertificateOrientation.PORTRAIT,
    };
    const mockFile = {
      buffer: Buffer.from('mock-content'),
    } as Express.Multer.File;
    const existingCertificate = {
      orientation: CertificateOrientation.PORTRAIT,
      template: CertificateTemplate.MISSION,
      brandImage: 'old-brand-image-url',
    } as Certificate;

    certificateRepository.getById.mockResolvedValueOnce(existingCertificate);
    certificateImagesService.resolveBrandImage.mockResolvedValueOnce('new-brand-image-url');

    await service.updateCertificate('certificateId', workspaceId, input, undefined, mockFile);

    expect(certificateRepository.update).toHaveBeenCalledWith('certificateId', workspaceId, {
      ...input,
      brandImage: 'new-brand-image-url',
    });
  });

  it('should retrieve an certificate by its id', () => {
    service.getById('certificateId', workspaceId);
    expect(certificateRepository.getById).toHaveBeenCalledWith('certificateId', workspaceId);
  });

  it('should retrieve the certificates list', () => {
    const listParamsDto = { search: 'mock_search', page: 1, limit: 10 } as PaginateQuery;
    service.list(workspaceId, listParamsDto);
    expect(certificateRepository.list).toHaveBeenCalledWith(workspaceId, listParamsDto);
  });

  describe('delete', () => {
    it('should delete an certificate by its id', () => {
      service.delete('certificateId', workspaceId);
      expect(certificateRepository.delete).toHaveBeenCalledWith('certificateId', workspaceId);
    });

    it('should set the last created certificate as default if the deleted is the current default', async () => {
      const deleteStub = { default: true, template: CertificateTemplate.MISSION } as Certificate;
      const lastCreatedStub = { id: chance.guid() } as Certificate;
      const toggleDefaultSpy = jest.spyOn(service, 'toggleDefaultCertificate');
      certificateRepository.delete.mockResolvedValueOnce(deleteStub);
      certificateRepository.getLatestCreated.mockResolvedValueOnce(lastCreatedStub);

      await service.delete('certificateId', workspaceId);

      expect(toggleDefaultSpy).toHaveBeenCalledWith(lastCreatedStub.id, workspaceId);
    });
  });

  describe('toggleDefaultCertificate', () => {
    it('should toggle the default certificate for the learn content type in a workspace', async () => {
      const newCertificateId = chance.guid();
      const currentCertificateId = chance.guid();
      const newDefaultCertificate = {
        id: newCertificateId,
        default: false,
        template: CertificateTemplate.MISSION,
        workspaceId,
      } as Certificate;
      const currentDefaultCertificate = {
        id: currentCertificateId,
        default: true,
        template: CertificateTemplate.MISSION,
        workspaceId,
      } as Certificate;
      certificateRepository.getById.mockResolvedValueOnce(newDefaultCertificate);
      certificateRepository.getDefaultCertificate.mockResolvedValueOnce(currentDefaultCertificate);

      await service.toggleDefaultCertificate(newCertificateId, workspaceId);

      expect(certificateRepository.getDefaultCertificate).toHaveBeenCalledWith(
        workspaceId,
        newDefaultCertificate.template,
      );
      expect(certificateRepository.batchSave).toHaveBeenCalledWith([
        {
          ...newDefaultCertificate,
          default: true,
        },
        { ...currentDefaultCertificate, default: false },
      ]);
    });

    it('should toggle current certificate if it is the current default', async () => {
      const newCertificateId = chance.guid();
      const workspaceId = chance.guid();
      const newDefaultCertificate = {
        id: newCertificateId,
        default: true,
        template: CertificateTemplate.MISSION,
        workspaceId,
      } as Certificate;
      certificateRepository.getById.mockResolvedValueOnce(newDefaultCertificate);
      certificateRepository.getDefaultCertificate.mockResolvedValueOnce(newDefaultCertificate);

      await service.toggleDefaultCertificate(newCertificateId, workspaceId);

      expect(certificateRepository.batchSave).toHaveBeenCalledWith([
        {
          ...newDefaultCertificate,
          default: false,
        },
      ]);
    });
  });

  it('should vinculate a certificate to a learn content', () => {
    const vinculateCertificateDto: VinculateCertificateDto = {
      learnContentId: chance.guid(),
      certificateId: chance.guid(),
    };
    service.vinculateCertificate(workspaceId, vinculateCertificateDto);
    expect(certificateLearnContentRepository.add).toHaveBeenCalledWith(vinculateCertificateDto, workspaceId);
  });

  it('should desvinculate a certificate from a learn content', async () => {
    const learnContentId = chance.guid();
    const certificateLearnContent = { learnContentId } as CertificateLearnContent;
    certificateLearnContentRepository.getById.mockResolvedValueOnce(certificateLearnContent);

    await service.desvinculateCertificate(workspaceId, learnContentId);

    expect(certificateLearnContentRepository.delete).toHaveBeenCalledWith(learnContentId, workspaceId);
  });

  it('should throw an error when trying to desvinculate an inexistent learn content certificate', () => {
    const learnContentId = chance.guid();
    certificateLearnContentRepository.getById.mockResolvedValueOnce(null);

    expect(async () => {
      await service.desvinculateCertificate(workspaceId, learnContentId);
    }).rejects.toThrow(LearnContentCertificateNotFoundException);
  });

  describe('getCertificateForPDFCreation', () => {
    const learnContentId = chance.guid();
    const workspaceId = chance.guid();

    it('should retrieve a certificate from the associated learn content', async () => {
      const certificate = { id: chance.guid() } as Certificate;
      const certificateLearnContent = { certificate } as CertificateLearnContent;
      certificateLearnContentRepository.getById.mockResolvedValueOnce(certificateLearnContent);

      const result = await service.getCertificateForPDFCreation(
        learnContentId,
        workspaceId,
        CertificateTemplate.MISSION,
      );

      expect(certificateLearnContentRepository.getById).toHaveBeenCalledWith(learnContentId, workspaceId);
      expect(result).toBe(certificate);
    });

    it('should retrieve the workspace default certificate if there is no associated learn content', async () => {
      const certificate = { id: chance.guid() } as Certificate;
      certificateLearnContentRepository.getById.mockResolvedValueOnce(null);
      certificateRepository.getDefaultCertificate.mockResolvedValueOnce(certificate);

      const result = await service.getCertificateForPDFCreation(
        learnContentId,
        workspaceId,
        CertificateTemplate.MISSION,
      );

      expect(certificateRepository.getDefaultCertificate).toHaveBeenCalledWith(
        workspaceId,
        CertificateTemplate.MISSION,
      );
      expect(result).toBe(certificate);
    });
  });

  describe('getLearnContentCertificate', () => {
    it('should query the learn content certificate', async () => {
      const learnContentId = chance.guid();
      const certificate = { id: chance.guid() } as Certificate;
      const certificateLearnContent = { certificate } as CertificateLearnContent;
      certificateLearnContentRepository.getById.mockResolvedValueOnce(certificateLearnContent);

      const result = await service.getLearnContentCertificate(workspaceId, learnContentId);

      expect(certificateLearnContentRepository.getById).toHaveBeenCalledWith(learnContentId, workspaceId);
      expect(result).toBe(certificate);
    });

    it('should throw an not found exception when there is no certificate', () => {
      const learnContentId = chance.guid();
      certificateLearnContentRepository.getById.mockResolvedValueOnce(undefined);

      expect(async () => {
        await service.getLearnContentCertificate(workspaceId, learnContentId);
      }).rejects.toThrow(LearnContentCertificateNotFoundException);
    });
  });
});
