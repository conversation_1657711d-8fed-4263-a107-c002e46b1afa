import { ImageProcessorService } from './image-processor.service';
import * as sharp from 'sharp';
import { ImageProcessingException } from '../exceptions/image-processing.exception';

jest.mock('sharp');

describe('ImageProcessorService', () => {
  let service: ImageProcessorService;
  let mockSharpInstance: jest.Mocked<sharp.Sharp>;

  beforeEach(async () => {
    mockSharpInstance = {
      resize: jest.fn().mockReturnThis(),
      toFormat: jest.fn().mockReturnThis(),
      toBuffer: jest.fn(),
    } as any;

    (sharp as unknown as jest.Mock).mockReturnValue(mockSharpInstance);

    service = new ImageProcessorService();
  });

  describe('createBackgroundImage', () => {
    const mockBuffer = Buffer.from('image-data');
    const mockResultBuffer = Buffer.from('mock-result-buffer');
    const mockOutputInfo: sharp.OutputInfo = {
      width: 1920,
      height: 1022,
      channels: 4,
      format: 'avif',
      size: mockResultBuffer.length,
      premultiplied: true,
    };

    it('should successfully process an image', async () => {
      mockSharpInstance.toBuffer.mockResolvedValue({ data: mockResultBuffer, info: mockOutputInfo });

      const result = await service.processImage(mockBuffer, { width: 1920, height: 1022 });

      expect(sharp).toHaveBeenCalledWith(mockBuffer);
      expect(mockSharpInstance.resize).toHaveBeenCalledWith(1920, 1022, { fit: 'cover', position: 'center' });
      expect(mockSharpInstance.toFormat).toHaveBeenCalledWith('avif', { quality: 90 });
      expect(mockSharpInstance.toBuffer).toHaveBeenCalled();
      expect(result).toEqual({ data: mockResultBuffer, info: mockOutputInfo });
    });

    // it('should successfully create a background image in portrait orientation', async () => {
    //   mockSharpInstance.toBuffer.mockResolvedValue({ data: mockResultBuffer, info: mockOutputInfo });
    //
    //   await service.createBackgroundImage(mockBuffer, CertificateOrientation.PORTRAIT);
    //
    //   expect(sharp).toHaveBeenCalledWith(mockBuffer);
    //   expect(mockSharpInstance.resize).toHaveBeenCalledWith(1022, 1920, { fit: 'cover', position: 'center' });
    //   expect(mockSharpInstance.toBuffer).toHaveBeenCalled();
    // });

    it('should propagate image processing errors', async () => {
      const error = new Error('Image processing error');
      mockSharpInstance.toBuffer.mockRejectedValueOnce(error);

      const expectedError = new ImageProcessingException(error.message);

      await expect(service.processImage(mockBuffer)).rejects.toThrow(expectedError);
      expect(sharp).toHaveBeenCalledWith(mockBuffer);
      expect(mockSharpInstance.toBuffer).toHaveBeenCalled();
    });
  });

  // describe('createThumbnail', () => {
  //   const mockBuffer = Buffer.from('image-data');
  //   const mockResultBuffer = Buffer.from('mock-result-buffer');
  //   const mockOutputInfo: sharp.OutputInfo = {
  //     width: 480,
  //     height: 255,
  //     channels: 4,
  //     format: 'avif',
  //     size: mockResultBuffer.length,
  //     premultiplied: true,
  //   };
  //
  //   it('should successfully create a thumbnail image with landscape orientation', async () => {
  //     mockSharpInstance.toBuffer.mockResolvedValue({ data: mockResultBuffer, info: mockOutputInfo });
  //
  //     const result = await service.createThumbnail(mockBuffer, CertificateOrientation.LANDSCAPE);
  //
  //     expect(sharp).toHaveBeenCalledWith(mockBuffer);
  //     expect(mockSharpInstance.resize).toHaveBeenCalledWith(480, 255, { fit: 'cover', position: 'center' });
  //     expect(mockSharpInstance.toFormat).toHaveBeenCalledWith('avif', { quality: 90 });
  //     expect(mockSharpInstance.toBuffer).toHaveBeenCalled();
  //     expect(result).toEqual({ data: mockResultBuffer, info: mockOutputInfo });
  //   });
  //
  //   it('should successfully create a thumbnail image with portrait orientation', async () => {
  //     mockSharpInstance.toBuffer.mockResolvedValue({ data: mockResultBuffer, info: mockOutputInfo });
  //
  //     await service.createThumbnail(mockBuffer, CertificateOrientation.PORTRAIT);
  //
  //     expect(sharp).toHaveBeenCalledWith(mockBuffer);
  //     expect(mockSharpInstance.resize).toHaveBeenCalledWith(255, 480, { fit: 'cover', position: 'center' });
  //     expect(mockSharpInstance.toBuffer).toHaveBeenCalled();
  //   });
  // });
});
