import { Injectable } from '@nestjs/common';
import { CertificateOrientation } from '../models';

import { S3Uploader } from '@keeps-node-apis/@core';
import { ImageUploadResponseDto } from '../dtos';
import { v4 as uuidv4 } from 'uuid';
import { ImageProcessorService } from './image-processor.service';

const DEFAULT_IMAGES_MAP = {
  background: {
    landscape: 'https://media.keepsdev.com/certificate-manager/default-images/landscape.png',
    portrait: 'https://media.keepsdev.com/certificate-manager/default-images/portrait.png',
  },
  thumbnail: {
    landscape: 'https://media.keepsdev.com/certificate-manager/default-images/landscape_thumb.avif',
    portrait: 'https://media.keepsdev.com/certificate-manager/default-images/portrait_thumb.avif',
  },
};

const DIMENSIONS_MAP = {
  [CertificateOrientation.LANDSCAPE]: {
    background: { width: 1920, height: 1022 },
    thumbnail: { width: 480, height: 255 },
  },
  [CertificateOrientation.PORTRAIT]: {
    background: { width: 1022, height: 1920 },
    thumbnail: { width: 255, height: 480 },
  },
};

@Injectable()
export class CertificateImagesService {
  constructor(
    private readonly s3Uploader: S3Uploader,
    private readonly imageProcessor: ImageProcessorService,
  ) {}

  async resolveBackgroundAndThumbnail(
    orientation: CertificateOrientation,
    workspaceId: string,
    backgroundImageFile?: Express.Multer.File,
  ) {
    if (!backgroundImageFile) {
      return this.getDefaultBackgroundAndThumbnail(orientation);
    }
    return this.storeBackgroundImageAndThumbnail(backgroundImageFile, orientation, workspaceId);
  }

  async resolveBrandImage(workspaceId: string, file?: Express.Multer.File) {
    if (!file) {
      return undefined;
    }

    const fileName = uuidv4();
    const brandImageBuffer = await this.imageProcessor.processImage(file.buffer);
    return await this.s3Uploader.uploadFile(`${workspaceId}/images/${fileName}_brand`, brandImageBuffer, 'AVIF');
  }

  private async storeBackgroundImageAndThumbnail(
    file: Express.Multer.File,
    orientation: CertificateOrientation,
    workspaceId: string,
  ): Promise<ImageUploadResponseDto> {
    const backgroundImageBuffer = await this.imageProcessor.processImage(
      file.buffer,
      DIMENSIONS_MAP[orientation].background,
    );
    const thumbnailBuffer = await this.imageProcessor.processImage(file.buffer, DIMENSIONS_MAP[orientation].thumbnail);

    const fileName = uuidv4();
    const backgroundImage = await this.s3Uploader.uploadFile(
      `${workspaceId}/images/${fileName}`,
      backgroundImageBuffer,
      'AVIF',
    );

    const thumbnail = await this.s3Uploader.uploadFile(
      `${workspaceId}/images/${fileName}_thumb`,
      thumbnailBuffer,
      'AVIF',
    );

    return { backgroundImage, thumbnail };
  }

  private getDefaultBackgroundAndThumbnail(orientation: CertificateOrientation) {
    const backgroundImage = DEFAULT_IMAGES_MAP?.background[orientation];
    const thumbnail = DEFAULT_IMAGES_MAP?.thumbnail[orientation];
    return { backgroundImage, thumbnail };
  }
}
