const puppeteerMock = {
  launch: jest.fn().mockResolvedValue({
    newPage: jest.fn().mockResolvedValue({
      setContent: jest.fn(),
      pdf: jest.fn().mockResolvedValue([]),
      close: jest.fn(),
    }),
    close: jest.fn(),
  }),
};

jest.mock('puppeteer', () => puppeteerMock);

import { CertificatesService } from './certificates.service';
import { PrintCertificateData } from '../models';
import { PdfService } from './pdf.service';
import { CertificateGenerateDto, CourseContentsDto, PROTO_CERTIFICATE_TEMPLATE } from '../dtos';
import { DEFAULT_MISSION_CONFIG } from '../default-certificate-configs';
import { TemplateCompilerService } from '@template-compiler';
import { S3Uploader } from '@keeps-node-apis/@core';
import { Chance } from 'chance';

const chance = new Chance();
const workspaceId = chance.guid();
const mockContents: CourseContentsDto[] = [
  {
    title: 'Primeiro conteúdo com um texto',
    time: '40 min',
    description: 'Descrição primeiro conteúdo',
    performance: '100%',
  },
];

const mockCertificateParams = {
  contents: mockContents,
  language: 'en',
  template: PROTO_CERTIFICATE_TEMPLATE.MISSION,
  workspace_id: workspaceId,
} as CertificateGenerateDto;

describe('PdfService', () => {
  let service: PdfService;
  let templateCompilerService: jest.Mocked<TemplateCompilerService>;
  let certificateServiceMock: jest.Mocked<CertificatesService>;
  let s3Uploader: jest.Mocked<S3Uploader>;

  beforeEach(async () => {
    templateCompilerService = {
      registerHelper: jest.fn(),
      execute: jest.fn().mockResolvedValue(`<!--suppress HtmlRequiredLangAttribute --><html>Test HTML</html>`),
    } as unknown as jest.Mocked<TemplateCompilerService>;
    certificateServiceMock = {
      getCertificateForPDFCreation: jest.fn(),
    } as unknown as jest.Mocked<CertificatesService>;
    s3Uploader = { uploadFile: jest.fn().mockResolvedValue('mock_url') } as unknown as jest.Mocked<S3Uploader>;

    service = new PdfService(templateCompilerService, certificateServiceMock, s3Uploader);
  });

  describe('generateCertificatePdf', () => {
    it('should generate a PDF and return its S3 storage url', async () => {
      const expectedCertificatePrintData: PrintCertificateData = {
        ...mockCertificateParams,
        config: DEFAULT_MISSION_CONFIG,
        paginatedContent: [mockContents],
      };

      const result = await service.generateCertificatePdf(mockCertificateParams);

      expect(result.url).toBe('mock_url');
      expect(s3Uploader.uploadFile).toHaveBeenCalledWith(
        expect.stringContaining(`${workspaceId}/certificates/`),
        [],
        'PDF',
      );
      expect(templateCompilerService.execute).toHaveBeenCalledWith('mission', expectedCertificatePrintData, 'en');
    });

    it('should throw an error if template compilation fails', () => {
      templateCompilerService.execute.mockRejectedValueOnce(new Error('Template compilation failed'));

      expect(async () => {
        await service.generateCertificatePdf(mockCertificateParams);
      }).rejects.toThrow('Template compilation failed');
    });
  });
});
