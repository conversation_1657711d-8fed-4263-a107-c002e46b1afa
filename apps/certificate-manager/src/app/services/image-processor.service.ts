import { Injectable } from '@nestjs/common';
import { ImageProcessingException } from '../exceptions/image-processing.exception';
import sharp from 'sharp';

@Injectable()
export class ImageProcessorService {
  async processImage(buffer: Buffer, dimensions?: { width: number; height: number }) {
    try {
      let sharpInstance = sharp(buffer);
      if (dimensions) {
        sharpInstance = sharpInstance.resize(dimensions.width, dimensions.height, { fit: 'cover', position: 'center' });
      }
      return await sharpInstance.toFormat('avif', { quality: 90 }).toBuffer();
    } catch (error) {
      throw new ImageProcessingException(error.message);
    }
  }
}
