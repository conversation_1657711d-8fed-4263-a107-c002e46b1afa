import { Injectable } from '@nestjs/common';
import { CertificateCreateDto, CertificateItemDto, VinculateCertificateDto } from '../dtos';
import { CertificateLearnContentRepository, CertificatesRepository } from '../repositories';
import { Certificate } from '../entities/certificate.entity';
import { CertificateTemplate } from '../models';
import { LearnContentCertificateNotFoundException } from '../exceptions/learn-content-certificate-not-found.exception';
import { CertificateUpdateDto } from '../dtos/certificate-update.dto';
import { CertificateImagesService } from './certificate-images.service';
import { PaginateQuery } from 'nestjs-paginate';

@Injectable()
export class CertificatesService {
  constructor(
    private readonly certificatesRepository: CertificatesRepository,
    private readonly certificateLearnContentRepository: CertificateLearnContentRepository,
    private readonly certificateImagesService: CertificateImagesService,
  ) {}

  async createCertificate(
    certificateCreateDto: CertificateCreateDto,
    bgImage?: Express.Multer.File,
    logoImage?: Express.Multer.File,
  ): Promise<CertificateItemDto> {
    const updatedDto = {
      ...certificateCreateDto,
      default: certificateCreateDto.default ?? false,
    };

    const { backgroundImage, thumbnail } = await this.certificateImagesService.resolveBackgroundAndThumbnail(
      certificateCreateDto.orientation,
      certificateCreateDto.workspaceId,
      bgImage,
    );

    const brandImage = await this.certificateImagesService.resolveBrandImage(
      certificateCreateDto.workspaceId,
      logoImage,
    );

    return this.certificatesRepository.add({ ...updatedDto, thumbnail, backgroundImage, brandImage });
  }

  async updateCertificate(
    certificateId: string,
    workspaceId: string,
    updateDto: CertificateUpdateDto,
    bgImage?: Express.Multer.File,
    brandImage?: Express.Multer.File,
  ): Promise<CertificateItemDto> {
    const certificate = await this.certificatesRepository.getById(certificateId, workspaceId);
    const orientation = updateDto.orientation || certificate.orientation;

    let certificateBackgroundImage = updateDto.backgroundImage;
    let certificateThumbnail = updateDto.thumbnail;
    let certificateBrandImage = updateDto.brandImage;

    if (bgImage || updateDto.backgroundImage === null) {
      const backgroundImageResult = await this.certificateImagesService.resolveBackgroundAndThumbnail(
        orientation,
        workspaceId,
        bgImage,
      );

      certificateBackgroundImage = backgroundImageResult.backgroundImage;
      certificateThumbnail = backgroundImageResult.thumbnail;
    }

    if (brandImage) {
      certificateBrandImage = await this.certificateImagesService.resolveBrandImage(workspaceId, brandImage);
    }

    return this.certificatesRepository.update(certificateId, workspaceId, {
      ...updateDto,
      backgroundImage: certificateBackgroundImage,
      thumbnail: certificateThumbnail,
      brandImage: certificateBrandImage,
    });
  }

  getById(certificateId: string, workspaceId: string): Promise<CertificateItemDto> {
    return this.certificatesRepository.getById(certificateId, workspaceId);
  }

  async list(workspaceId: string, query: PaginateQuery) {
    return this.certificatesRepository.list(workspaceId, query);
  }

  async delete(certificateId: string, workspaceId: string) {
    const deleted = await this.certificatesRepository.delete(certificateId, workspaceId);
    if (deleted?.default) {
      await this.setLastCreatedAsDefault(workspaceId, deleted.template);
    }

    return deleted;
  }

  async toggleDefaultCertificate(certificateId: string, workspaceId: string): Promise<boolean> {
    const newDefaultCertificate = await this.certificatesRepository.getById(certificateId, workspaceId);
    const currentDefaultCertificate = await this.getDefaultCertificate(workspaceId, newDefaultCertificate.template);
    const areSameCertificate = newDefaultCertificate.id === currentDefaultCertificate?.id;

    newDefaultCertificate.default = !areSameCertificate;
    const changes: Certificate[] = [newDefaultCertificate];
    if (currentDefaultCertificate && !areSameCertificate) {
      currentDefaultCertificate.default = false;
      changes.push(currentDefaultCertificate);
    }
    const updates = await this.certificatesRepository.batchSave(changes);
    return !!updates.length;
  }

  vinculateCertificate(workspaceId: string, vinculateCertificateDto: VinculateCertificateDto) {
    return this.certificateLearnContentRepository.add(vinculateCertificateDto, workspaceId);
  }

  async desvinculateCertificate(workspaceId: string, learnContentId: string) {
    const learnContentCertificate = await this.certificateLearnContentRepository.getById(learnContentId, workspaceId);

    if (!learnContentCertificate) {
      throw new LearnContentCertificateNotFoundException(learnContentId, workspaceId);
    }

    return this.certificateLearnContentRepository.delete(learnContentId, workspaceId);
  }

  async getLearnContentCertificate(workspaceId: string, learnContentId: string) {
    const learnContentCertificate = await this.certificateLearnContentRepository.getById(learnContentId, workspaceId);
    if (!learnContentCertificate) {
      throw new LearnContentCertificateNotFoundException(learnContentId, workspaceId);
    }

    return learnContentCertificate.certificate;
  }

  async getCertificateForPDFCreation(learnContentId: string, workspaceId: string, template: CertificateTemplate) {
    const learnContentCertificate = await this.certificateLearnContentRepository.getById(learnContentId, workspaceId);
    if (learnContentCertificate) {
      return learnContentCertificate.certificate;
    }

    return await this.getDefaultCertificate(workspaceId, template);
  }

  private getDefaultCertificate(workspaceId: string, template: CertificateTemplate) {
    return this.certificatesRepository.getDefaultCertificate(workspaceId, template);
  }

  private async setLastCreatedAsDefault(workspaceId: string, template: CertificateTemplate) {
    const lastCreated = await this.certificatesRepository.getLatestCreated(workspaceId, template);
    if (!lastCreated) {
      return;
    }
    await this.toggleDefaultCertificate(lastCreated.id, workspaceId);
  }
}
