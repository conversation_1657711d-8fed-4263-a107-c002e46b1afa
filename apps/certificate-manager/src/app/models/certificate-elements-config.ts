export type CertificateElementStyling = {
  position: { x: number; y: number };
  textColor?: string;
  hidden?: boolean;
};

export enum CERTIFICATE_ELEMENT_TYPE {
  LOGO = 'logo',
  TITLE = 'title',
  CERTIFY_LABEL = 'weCertifyLabel',
  HOLDER_NAME = 'holderName',
  HAS_COMPLETED_LABEL = 'hasCompletedLabel',
  COURSE_NAME = 'courseName',
  SIGNED_BY_LABEL = 'signedByLabel',
  PERFORMANCE_INFO = 'performanceInfo',
  TOTAL_TIME = 'totalTime',
  CONCLUSION_DATE = 'conclusionDate',
}

export type CertificateCustomizableElements = `${CERTIFICATE_ELEMENT_TYPE}`;

export type CertificateElementsStyling = Record<CertificateCustomizableElements, CertificateElementStyling>;
