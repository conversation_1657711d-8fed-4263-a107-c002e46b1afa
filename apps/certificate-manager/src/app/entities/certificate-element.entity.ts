import { Column, <PERSON>tity, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { Certificate } from './certificate.entity';
import { CERTIFICATE_ELEMENT_TYPE } from '../models';

@Entity('certificate_element')
export class CertificateElement {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ManyToOne(() => Certificate, (certificate) => certificate.elements, { onDelete: 'CASCADE' })
  certificate: Certificate;

  @Column({ type: 'float' })
  xPos: number;

  @Column({ type: 'float' })
  yPos: number;

  @Column({ type: 'enum', enum: CERTIFICATE_ELEMENT_TYPE })
  type: CERTIFICATE_ELEMENT_TYPE;

  @Column({ type: 'text', nullable: true })
  textColor: string;

  @Column({ type: 'boolean', nullable: true })
  hidden: boolean;
}
