import { <PERSON><PERSON>ty, ManyToOne, PrimaryColumn, <PERSON>inColumn } from 'typeorm';
import { Certificate } from './certificate.entity';

@Entity('certificate_learn_content')
export class CertificateLearnContent {
  @PrimaryColumn({ name: 'learn_content_id' })
  learnContentId: string;

  @PrimaryColumn({ name: 'workspace_id' })
  workspaceId: string;

  @ManyToOne(() => Certificate, (certificate) => certificate.learnContents, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'certificate_id' })
  certificate: Certificate;
}
