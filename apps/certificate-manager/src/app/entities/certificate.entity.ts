import { Column, CreateDateColumn, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { CertificateOrientation, CertificateTemplate } from '../models';
import { CertificateLearnContent } from './certificate-learn-content.entity';
import { CertificateElement } from './certificate-element.entity';

@Entity('certificate')
export class Certificate {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  name: string;

  @Column()
  default: boolean;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @Column({ name: 'signed_by', nullable: true })
  signedBy: string;

  @Column({ name: 'template', enum: CertificateTemplate, type: 'enum' })
  template: CertificateTemplate;

  @Column({ enum: CertificateOrientation, type: 'enum', default: CertificateOrientation.LANDSCAPE })
  orientation: CertificateOrientation;

  @Column('boolean', { name: 'display_performance', nullable: true, default: true })
  displayPerformance: boolean;

  @Column('boolean', { name: 'display_total_time', nullable: true, default: true })
  displayTotalTime: boolean;

  @Column('boolean', { name: 'display_conclusion_date', nullable: true, default: true })
  displayConclusionDate: boolean;

  @Column('boolean', { name: 'hide_labels', nullable: true })
  hideLabels: boolean;

  @Column({ name: 'text_color', nullable: true })
  textColor: string;

  @Column({ name: 'background_color', nullable: true })
  backgroundColor: string;

  @Column({ name: 'thumbnail', nullable: true })
  thumbnail: string;

  @Column({ name: 'background_image', nullable: true })
  backgroundImage: string;

  @Column({ name: 'background_image_en', nullable: true })
  backgroundImageEn: string;

  @Column({ name: 'background_image_es', nullable: true })
  backgroundImageEs: string;

  @Column({ name: 'brand_image', nullable: true })
  brandImage: string;

  @Column({ name: 'workspace_id' })
  workspaceId: string;

  @OneToMany(() => CertificateLearnContent, (learnContent) => learnContent.certificate)
  learnContents: CertificateLearnContent[];

  @OneToMany(() => CertificateElement, (element) => element.certificate, { cascade: true })
  elements: CertificateElement[];
}
