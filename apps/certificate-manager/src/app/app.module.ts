import { AuthModule, AwsModule, KeepsAuthGuard, RolesGuard } from '@keeps-node-apis/@core';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { TemplateCompilerModule } from '@template-compiler';
import { KeycloakConnectConfig, KeycloakConnectModule } from 'nest-keycloak-connect';
import { AuthConfig, DatabaseConfig } from './config';
import { CertificatesController, PdfController } from './controllers';
import {
  CertificateLearnContentRepository,
  CertificateLearnContentTypeOrmRepository,
  CertificatesRepository,
  CertificatesTypeOrmRepository,
} from './repositories';
import { CertificatesService, PdfService } from './services';
import { CertificateImagesService } from './services/certificate-images.service';
import { ImageProcessorService } from './services/image-processor.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [AuthConfig, DatabaseConfig],
    }),
    TemplateCompilerModule.register({ i18nNamespace: 'CERTIFICATE' }),
    KeycloakConnectModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<KeycloakConnectConfig>('auth'),
      }),
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        ...configService.get<TypeOrmModuleOptions>('database'),
      }),
    }),
    AuthModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return { authAppId: configService.get('KONQUEST_APPLICATION_ID') };
      },
    }),
    AwsModule,
  ],
  controllers: [PdfController, CertificatesController],
  providers: [
    CertificatesService,
    PdfService,
    { provide: CertificatesRepository, useClass: CertificatesTypeOrmRepository },
    { provide: CertificateLearnContentRepository, useClass: CertificateLearnContentTypeOrmRepository },
    {
      provide: APP_GUARD,
      useClass: KeepsAuthGuard,
    },
    { provide: APP_GUARD, useClass: RolesGuard },
    ImageProcessorService,
    CertificateImagesService,
  ],
})
export class AppModule {}
