import { Injectable } from '@nestjs/common';
import { VinculateCertificateDto } from '../../dtos';
import { CertificateLearnContent } from '../../entities/certificate-learn-content.entity';
import { CertificateLearnContentRepository } from './certificate-learn-content.repository';
import { DataSource, Repository } from 'typeorm';

@Injectable()
export class CertificateLearnContentTypeOrmRepository implements CertificateLearnContentRepository {
  private readonly repository: Repository<CertificateLearnContent>;

  constructor(private readonly dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(CertificateLearnContent);
  }

  async add(vinculateCertificateDto: VinculateCertificateDto, workspaceId: string): Promise<CertificateLearnContent> {
    const { certificateId, learnContentId } = vinculateCertificateDto;
    const certificateLearnContent = this.repository.create({
      workspaceId,
      learnContentId,
      certificate: { id: certificateId },
    });
    return this.repository.save(certificateLearnContent);
  }

  async delete(learnContentId: string, workspaceId: string): Promise<CertificateLearnContent> {
    const certificateLeanContent = await this.repository.findOneByOrFail({
      learnContentId,
      workspaceId,
    });
    return this.repository.remove(certificateLeanContent);
  }

  getById(learnContentId: string, workspaceId: string): Promise<CertificateLearnContent> {
    return this.repository
      .createQueryBuilder('certificate_learn_content')
      .leftJoinAndSelect('certificate_learn_content.certificate', 'certificate')
      .leftJoinAndSelect('certificate.elements', 'certificate_elements')
      .where('certificate_learn_content.workspace_id = :workspaceId', { workspaceId })
      .andWhere('certificate_learn_content.learn_content_id = :learnContentId', { learnContentId })
      .getOne();
  }
}
