import { CertificateLearnContentTypeOrmRepository } from './certificate-learn-content-type-orm.repository';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { CertificateLearnContent } from '../../entities/certificate-learn-content.entity';
import { Chance } from 'chance';
import { VinculateCertificateDto } from '../../dtos';

describe('CertificateLearnContentTypeOrmRepository', () => {
  let learnContentRepository: CertificateLearnContentTypeOrmRepository;
  let typeOrmRepositoryMock: jest.Mocked<Repository<CertificateLearnContent>>;
  let dataSourceStub: jest.Mocked<DataSource>;
  let queryBuilderMock: jest.Mocked<SelectQueryBuilder<CertificateLearnContent>>;
  const chance = new Chance();
  const workspaceId = chance.guid();

  beforeEach(() => {
    queryBuilderMock = {
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      getOne: jest.fn().mockReturnThis(),
    } as unknown as jest.Mocked<SelectQueryBuilder<CertificateLearnContent>>;

    typeOrmRepositoryMock = {
      create: jest.fn(),
      save: jest.fn(),
      findOneByOrFail: jest.fn(),
      remove: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue(queryBuilderMock),
    } as unknown as jest.Mocked<Repository<CertificateLearnContent>>;
    dataSourceStub = {
      getRepository: jest.fn().mockReturnValue(typeOrmRepositoryMock),
    } as unknown as jest.Mocked<DataSource>;
    learnContentRepository = new CertificateLearnContentTypeOrmRepository(dataSourceStub);
  });

  it('should vinculate a certificate to a learn content', async () => {
    const input: VinculateCertificateDto = {
      certificateId: chance.guid(),
      learnContentId: chance.guid(),
    };
    const expectedParams = {
      workspaceId,
      learnContentId: input.learnContentId,
      certificate: { id: input.certificateId },
    } as CertificateLearnContent;
    await learnContentRepository.add(input, workspaceId);
    expect(typeOrmRepositoryMock.create).toHaveBeenCalledWith(expectedParams);
  });

  it('should desvinculate a certificate from a learn content', async () => {
    const learnContentId = chance.guid();
    const stubLearnContentCertificate = {
      learnContentId,
      certificate: { id: chance.guid() },
      workspaceId,
    } as CertificateLearnContent;
    typeOrmRepositoryMock.findOneByOrFail.mockResolvedValueOnce(stubLearnContentCertificate);

    await learnContentRepository.delete(learnContentId, workspaceId);
    expect(typeOrmRepositoryMock.remove).toHaveBeenCalledWith(stubLearnContentCertificate);
  });

  it('should query a learn content by its id', async () => {
    const learnContentId = chance.guid();
    const workspaceId = chance.guid();

    await learnContentRepository.getById(learnContentId, workspaceId);
    expect(typeOrmRepositoryMock.createQueryBuilder).toHaveBeenCalledWith('certificate_learn_content');
    expect(queryBuilderMock.leftJoinAndSelect).toHaveBeenCalledWith(
      'certificate_learn_content.certificate',
      'certificate',
    );
    expect(queryBuilderMock.where).toHaveBeenCalledWith('certificate_learn_content.workspace_id = :workspaceId', {
      workspaceId,
    });
    expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
      'certificate_learn_content.learn_content_id = :learnContentId',
      { learnContentId },
    );
    expect(queryBuilderMock.getOne).toHaveBeenCalled();
  });
});
