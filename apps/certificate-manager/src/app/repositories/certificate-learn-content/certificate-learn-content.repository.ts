import { CertificateLearnContent } from '../../entities/certificate-learn-content.entity';
import { VinculateCertificateDto } from '../../dtos';

export abstract class CertificateLearnContentRepository {
  abstract add(vinculateCertificateDto: VinculateCertificateDto, workspaceId: string): Promise<CertificateLearnContent>;

  abstract delete(learnContentId: string, workspaceId: string): Promise<CertificateLearnContent>;

  abstract getById(learnContentId: string, workspaceId: string): Promise<CertificateLearnContent>;
}
