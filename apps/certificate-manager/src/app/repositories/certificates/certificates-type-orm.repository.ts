import { Injectable } from '@nestjs/common';
import { CertificatesRepository } from './certificates.repository';
import { CertificateCreateDto, CertificateItemDto } from '../../dtos';
import { DataSource, Repository } from 'typeorm';
import { Certificate } from '../../entities/certificate.entity';
import { CertificateTemplate } from '../../models';
import { CertificateUpdateDto } from '../../dtos/certificate-update.dto';
import { paginate, Paginated, PaginateQuery } from 'nestjs-paginate';
import { CERTIFICATES_QUERY_CONFIG } from '../../certificates.query-config';

@Injectable()
export class CertificatesTypeOrmRepository implements CertificatesRepository {
  private readonly repository: Repository<Certificate>;

  constructor(private readonly dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(Certificate);
  }

  async add(certificate: CertificateCreateDto) {
    const created = this.repository.create(certificate);
    let changes = [created];

    if (created.default) {
      changes = await this.addCurrentDefaultToChangeList(certificate.workspaceId, certificate.template, changes);
    }

    await this.repository.save(changes);
    return created;
  }

  async update(certificateId: string, workspaceId: string, updateDto: CertificateUpdateDto) {
    const entity = await this.getById(certificateId, workspaceId);
    const updated = { ...entity, ...updateDto } as Certificate;
    let changes = [updated];

    if (updateDto.default && !entity.default) {
      changes = await this.addCurrentDefaultToChangeList(workspaceId, updated.template, changes);
    }

    await this.repository.save(changes);
    return updated;
  }

  async getById(certificateId: string, workspaceId: string): Promise<Certificate> {
    return this.repository.findOneByOrFail({ id: certificateId, workspaceId });
  }

  async delete(certificateId: string, workspaceId: string): Promise<CertificateItemDto> {
    const certificate = await this.getById(certificateId, workspaceId);
    return this.repository.remove(certificate);
  }

  findOneBy(where: Partial<CertificateItemDto>): Promise<Certificate | null> {
    return this.repository.findOneBy(where);
  }

  batchSave(entities: Certificate[]) {
    return this.repository.save(entities);
  }

  getLatestCreated(workspaceId: string, template: CertificateTemplate): Promise<Certificate | null> {
    return this.repository
      .createQueryBuilder('certificate')
      .where('certificate.workspace_id = :workspaceId', { workspaceId })
      .andWhere('certificate.template = :template', { template })
      .orderBy('certificate.createdAt', 'DESC')
      .getOne();
  }

  getDefaultCertificate(workspaceId: string, template: CertificateTemplate): Promise<Certificate | null> {
    return this.repository
      .createQueryBuilder('certificate')
      .leftJoinAndSelect('certificate.elements', 'certificate_elements')
      .where('certificate.workspace_id = :workspaceId', { workspaceId })
      .andWhere('certificate.template = :template', { template })
      .andWhere('certificate.default = :default', { default: true })
      .getOne();
  }

  async list(workspaceId: string, query: PaginateQuery): Promise<Paginated<Certificate>> {
    const config = { ...CERTIFICATES_QUERY_CONFIG, where: [{ workspaceId }] };
    return await paginate<Certificate>(query, this.repository, config);
  }

  private async addCurrentDefaultToChangeList(
    workspaceId: string,
    template: CertificateTemplate,
    changeList: Certificate[],
  ): Promise<Certificate[]> {
    const currentDefault = await this.findOneBy({
      default: true,
      template,
      workspaceId,
    });

    if (!currentDefault) {
      return changeList;
    }

    currentDefault.default = false;
    return [currentDefault, ...changeList];
  }
}
