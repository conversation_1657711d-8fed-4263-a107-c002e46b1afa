import { CertificatesTypeOrmRepository } from './certificates-type-orm.repository';
import { CertificateCreateDto } from '../../dtos';
import { CertificateOrientation, CertificateTemplate } from '../../models';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { Certificate } from '../../entities/certificate.entity';
import { Chance } from 'chance';
import { CertificateUpdateDto } from '../../dtos/certificate-update.dto';
import * as nestjsPaginate from 'nestjs-paginate';
import { PaginateQuery } from 'nestjs-paginate';

jest.mock('nestjs-paginate', () => ({
  paginate: jest.fn(),
  FilterOperator: {
    ILIKE: 'ILIKE',
    EQ: 'EQ',
    NOT: 'NOT',
  },
  FilterSuffix: {
    NOT: 'NOT',
  },
}));

describe('CertificatesTypeOrmRepository', () => {
  let certificatesRepository: CertificatesTypeOrmRepository;
  let typeOrmRepositoryMock: jest.Mocked<Repository<Certificate>>;
  let dataSourceStub: jest.Mocked<DataSource>;
  const chance = new Chance();
  const workspaceId = chance.guid();
  let queryBuilderMock: jest.Mocked<SelectQueryBuilder<Certificate>>;

  beforeEach(async () => {
    queryBuilderMock = {
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      addOrderBy: jest.fn().mockReturnThis(),
      getOne: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getCount: jest.fn().mockResolvedValue(1),
      getRawAndEntities: jest.fn().mockReturnValue({ entities: [] }),
    } as unknown as jest.Mocked<SelectQueryBuilder<Certificate>>;

    typeOrmRepositoryMock = {
      create: jest.fn().mockImplementation((input) => ({ id: chance.guid(), ...input })),
      save: jest.fn(),
      findOneByOrFail: jest.fn(),
      remove: jest.fn(),
      findOneBy: jest.fn(),
      createQueryBuilder: jest.fn().mockReturnValue(queryBuilderMock),
    } as unknown as jest.Mocked<Repository<Certificate>>;
    dataSourceStub = {
      getRepository: jest.fn().mockReturnValue(typeOrmRepositoryMock),
    } as unknown as jest.Mocked<DataSource>;
    certificatesRepository = new CertificatesTypeOrmRepository(dataSourceStub);
  });

  describe('add', () => {
    it('should add a new certificate', async () => {
      const input: CertificateCreateDto = {
        name: 'certificate',
        template: CertificateTemplate.MISSION,
        orientation: CertificateOrientation.PORTRAIT,
      };

      const result = await certificatesRepository.add(input);

      expect(typeOrmRepositoryMock.create).toHaveBeenCalledWith(input);
      expect(typeOrmRepositoryMock.save).toHaveBeenCalledWith([{ ...input, ...result }]);
      expect(result.id).toBeDefined();
    });

    it('should save the new certificate and update the current default', async () => {
      const input: CertificateCreateDto = {
        name: 'certificate',
        template: CertificateTemplate.MISSION,
        orientation: CertificateOrientation.PORTRAIT,
        default: true,
        workspaceId,
      };
      const currentDefault = { default: true, id: chance.guid() } as Certificate;
      typeOrmRepositoryMock.findOneBy.mockResolvedValueOnce(currentDefault);

      const result = await certificatesRepository.add(input);

      expect(typeOrmRepositoryMock.findOneBy).toHaveBeenCalledWith({
        default: true,
        template: input.template,
        workspaceId,
      });
      expect(typeOrmRepositoryMock.create).toHaveBeenCalledWith(input);
      expect(typeOrmRepositoryMock.save).toHaveBeenCalledWith([currentDefault, { id: result.id, ...input }]);
    });
  });

  describe('update', () => {
    it('should update a certificate', async () => {
      const certificateId = chance.guid();
      const input: CertificateUpdateDto = {
        name: 'certificate',
        template: CertificateTemplate.MISSION,
        orientation: CertificateOrientation.PORTRAIT,
      };
      typeOrmRepositoryMock.findOneByOrFail.mockResolvedValueOnce({ id: certificateId, ...input } as Certificate);

      await certificatesRepository.update(certificateId, workspaceId, input);

      expect(typeOrmRepositoryMock.save).toHaveBeenCalledWith([{ id: certificateId, ...input }]);
    });

    it('should update a certificate as default', async () => {
      const certificateId = chance.guid();
      const input: CertificateCreateDto = {
        name: 'certificate',
        template: CertificateTemplate.MISSION,
        orientation: CertificateOrientation.PORTRAIT,
        default: true,
      };
      typeOrmRepositoryMock.findOneByOrFail.mockResolvedValueOnce({
        id: certificateId,
        ...input,
        default: false,
      } as Certificate);
      const currentDefault = { default: true, id: chance.guid() } as Certificate;
      typeOrmRepositoryMock.findOneBy.mockResolvedValueOnce(currentDefault);

      await certificatesRepository.update(certificateId, workspaceId, input);

      expect(typeOrmRepositoryMock.save).toHaveBeenCalledWith([currentDefault, { id: certificateId, ...input }]);
    });

    it('should not update the default certificate when it is the entity being updated', async () => {
      const certificateId = chance.guid();
      const input: CertificateCreateDto = {
        name: 'certificate',
        template: CertificateTemplate.MISSION,
        orientation: CertificateOrientation.PORTRAIT,
        default: true,
      };
      typeOrmRepositoryMock.findOneByOrFail.mockResolvedValueOnce({
        id: certificateId,
        ...input,
      } as Certificate);

      await certificatesRepository.update(certificateId, workspaceId, input);

      expect(typeOrmRepositoryMock.save).toHaveBeenCalledWith([{ id: certificateId, ...input }]);
    });
  });

  it('should retrieve a certificate by its id', async () => {
    const certificateId = chance.guid();

    await certificatesRepository.getById(certificateId, workspaceId);

    expect(typeOrmRepositoryMock.findOneByOrFail).toHaveBeenCalledWith({ id: certificateId, workspaceId });
  });

  it('should retrieve a certificate by its properties', async () => {
    const where = { id: chance.guid(), workspaceId } as Partial<Certificate>;

    await certificatesRepository.findOneBy(where);

    expect(typeOrmRepositoryMock.findOneBy).toHaveBeenCalledWith(where);
  });

  it('should batch save a list of certificates', async () => {
    const certificates = [
      { id: chance.guid(), workspaceId },
      { id: chance.guid(), workspaceId },
    ] as Certificate[];

    await certificatesRepository.batchSave(certificates);

    expect(typeOrmRepositoryMock.save).toHaveBeenCalledWith(certificates);
  });

  it('should query for the last created certificate', async () => {
    const template = CertificateTemplate.MISSION;

    await certificatesRepository.getLatestCreated(workspaceId, template);

    expect(queryBuilderMock.where).toHaveBeenCalledWith('certificate.workspace_id = :workspaceId', { workspaceId });
    expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('certificate.template = :template', { template });
    expect(queryBuilderMock.orderBy).toHaveBeenCalledWith('certificate.createdAt', 'DESC');
    expect(queryBuilderMock.getOne).toHaveBeenCalled();
  });

  it('should filter the certificates list', async () => {
    const listParamsDto = {
      search: 'mock_search',
      page: 1,
      limit: 10,
    } as PaginateQuery;

    await certificatesRepository.list(workspaceId, listParamsDto);

    expect(nestjsPaginate.paginate).toHaveBeenCalledWith(listParamsDto, expect.any(Object), expect.any(Object));
  });
});
