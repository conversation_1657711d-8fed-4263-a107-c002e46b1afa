import { CertificateCreateDto, CertificateItemDto } from '../../dtos';
import { Certificate } from '../../entities/certificate.entity';
import { CertificateTemplate } from '../../models';
import { CertificateUpdateDto } from '../../dtos/certificate-update.dto';
import { Paginated, PaginateQuery } from 'nestjs-paginate';

export abstract class CertificatesRepository {
  abstract add(certificate: CertificateCreateDto): Promise<CertificateItemDto>;

  abstract update(
    certificateId: string,
    workspaceId: string,
    certificate: CertificateUpdateDto,
  ): Promise<CertificateItemDto | undefined>;

  abstract getById(certificateId: string, workspaceId: string): Promise<Certificate>;

  abstract list(workspaceId: string, query: PaginateQuery): Promise<Paginated<Certificate>>;

  abstract delete(certificateId: string, workspaceId: string): Promise<CertificateItemDto>;

  abstract findOneBy(where: Partial<CertificateItemDto>): Promise<Certificate | null>;

  abstract batchSave(entities: Certificate[]): Promise<Certificate[]>;

  abstract getLatestCreated(workspaceId: string, template: CertificateTemplate): Promise<Certificate | null>;

  abstract getDefaultCertificate(workspaceId: string, template: CertificateTemplate): Promise<Certificate | null>;
}
