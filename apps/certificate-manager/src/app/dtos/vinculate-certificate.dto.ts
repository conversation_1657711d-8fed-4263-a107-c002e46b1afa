import { IsNotEmpty, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class VinculateCertificateDto {
  @ApiProperty({
    description: 'The unique identifier of the certificate',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  @IsString()
  certificateId: string;

  @ApiProperty({
    description: 'The unique identifier of the learning content',
    example: '7c4e8d85-f793-4c29-9962-bed7d8631789',
  })
  @IsNotEmpty()
  @IsString()
  learnContentId: string;
}
