import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsHexColor,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  ValidateIf,
  ValidateNested,
} from 'class-validator';
import { CertificateOrientation, CertificateTemplate } from '../models';
import { CertificateElementDto } from './certificate-element.dto';
import { Transform, Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CertificateCreateDto {
  @ApiProperty({ example: 'Course Completion Certificate', description: 'The name of the certificate' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ example: '<PERSON>', description: 'The name of the person who signed the certificate' })
  @IsString()
  @IsOptional()
  signedBy?: string;

  @ApiProperty({
    enum: CertificateTemplate,
    description: 'The template type of the certificate',
    example: CertificateTemplate.MISSION,
  })
  @IsEnum(CertificateTemplate)
  template: CertificateTemplate;

  @ApiProperty({
    enum: CertificateOrientation,
    description: 'The orientation of the certificate',
    example: CertificateOrientation.PORTRAIT,
  })
  @IsEnum(CertificateOrientation)
  orientation: CertificateOrientation;

  @ApiPropertyOptional({ example: true, description: 'Whether to display performance information' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  @IsOptional()
  displayPerformance?: boolean;

  @ApiPropertyOptional({ example: true, description: 'Whether to display total time' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  @IsOptional()
  displayTotalTime?: boolean;

  @ApiPropertyOptional({ example: true, description: 'Whether to display conclusion date' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  @IsOptional()
  displayConclusionDate?: boolean | null;

  @ApiPropertyOptional({ example: false, description: 'Whether to hide labels' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsOptional()
  @IsBoolean()
  hideLabels?: boolean | null;

  @ApiPropertyOptional({ example: '#000000', description: 'The color of the text in hex format' })
  @IsHexColor()
  @IsOptional()
  textColor?: string;

  @ApiPropertyOptional({ example: '#FFFFFF', description: 'The background color in hex format' })
  @IsHexColor()
  @IsOptional()
  @ValidateIf((_, value) => value !== 'null' && value !== null && value !== '')
  backgroundColor?: string | null;

  @ApiPropertyOptional({ example: 'https://example.com/background.jpg', description: 'URL of the background image' })
  @IsOptional()
  @IsUrl()
  @ValidateIf((_, value) => value !== 'null' && value !== null && value !== '')
  @Transform(({ value }) => (value === 'null' ? null : value))
  backgroundImage?: string | null;

  @ApiPropertyOptional({
    example: 'https://example.com/background-en.jpg',
    description: 'URL of the English background image',
  })
  @IsOptional()
  @IsUrl()
  @ValidateIf((_, value) => value !== 'null' && value !== null)
  @Transform(({ value }) => (value === 'null' ? null : value))
  backgroundImageEn?: string;

  @ApiPropertyOptional({
    example: 'https://example.com/background-es.jpg',
    description: 'URL of the Spanish background image',
  })
  @IsOptional()
  @IsUrl()
  @ValidateIf((_, value) => value !== 'null' && value !== null)
  @Transform(({ value }) => (value === 'null' ? null : value))
  backgroundImageEs?: string;

  @ApiPropertyOptional({ example: 'https://example.com/brand.jpg', description: 'URL of the brand image' })
  @IsOptional()
  @IsUrl()
  @ValidateIf((_, value) => value !== 'null' && value !== null && value !== '')
  @Transform(({ value }) => (value === 'null' ? null : value))
  brandImage?: string | null;

  @ApiPropertyOptional({ example: false, description: 'Whether this is the default certificate' })
  @Transform(({ value }) => value === 'true' || value === true)
  @IsOptional()
  @IsBoolean()
  default?: boolean;

  @ApiPropertyOptional({ type: [CertificateElementDto], description: 'Array of certificate elements' })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CertificateElementDto)
  elements?: CertificateElementDto[];

  @ApiPropertyOptional({ example: 'workspace123', description: 'ID of the workspace' })
  workspaceId?: string;

  @ApiPropertyOptional({ example: 'thumbnail.jpg', description: 'Thumbnail image of the certificate' })
  thumbnail?: string;
}
