import { IsBoolean, <PERSON><PERSON><PERSON>, IsHexColor, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { CERTIFICATE_ELEMENT_TYPE } from '../models';

export class CertificateElementDto {
  @ApiProperty({
    enum: CERTIFICATE_ELEMENT_TYPE,
    description: 'Type of certificate element',
  })
  @IsEnum(CERTIFICATE_ELEMENT_TYPE)
  type: CERTIFICATE_ELEMENT_TYPE;

  @ApiProperty({
    description: 'Vertical position (0-1)',
    example: 0.5,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @Max(1)
  yPos: number;

  @ApiProperty({
    description: 'Horizontal position (0-1)',
    example: 0.5,
    minimum: 0,
    maximum: 1,
  })
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @Max(1)
  xPos: number;

  @ApiProperty({
    description: 'Text color in hexadecimal format',
    example: '#000000',
    required: false,
  })
  @IsHexColor()
  @IsOptional()
  textColor?: string;

  @ApiProperty({
    description: 'Indicates if the element is hidden',
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  hidden?: boolean;
}
