import { ApiProperty } from '@nestjs/swagger';

export class ImageUploadResponseDto {
  @ApiProperty({
    description: 'URL of the uploaded background image',
    example: 'https://storage.googleapis.com/bucket/background-image.jpg',
  })
  backgroundImage: string;

  @ApiProperty({
    description: 'URL of the generated thumbnail image',
    example: 'https://storage.googleapis.com/bucket/thumbnail.jpg',
  })
  thumbnail: string;
}
