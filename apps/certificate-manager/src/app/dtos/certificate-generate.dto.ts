import { CourseContentsDto } from './course-contents.dto';
import { CertificateTemplate } from '../models';

export enum PROTO_CERTIFICATE_TEMPLATE {
  MISSION,
  TRAIL,
}

export class CertificateGenerateDto {
  learn_content_id: string;
  workspace_id: string;
  template: PROTO_CERTIFICATE_TEMPLATE;
  language: string;
  user_name: string;
  performance: string;
  time: string;
  date_finish: string;
  course_name: string;
  contents: CourseContentsDto[];

  static getCertificateTemplate(certificateGenerateDto: CertificateGenerateDto): CertificateTemplate {
    const isTrailTemplate = certificateGenerateDto.template == PROTO_CERTIFICATE_TEMPLATE.TRAIL;
    return isTrailTemplate ? CertificateTemplate.TRAIL : CertificateTemplate.MISSION;
  }
}
