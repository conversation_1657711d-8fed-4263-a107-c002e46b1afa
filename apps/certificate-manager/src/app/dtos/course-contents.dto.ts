import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CourseContentsDto {
  @ApiProperty({
    description: 'The title of the course content',
    example: 'Introduction to TypeScript',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'The duration of the course content',
    example: '2h 30min',
  })
  @IsNotEmpty()
  @IsString()
  time: string;

  @ApiProperty({
    description: 'The description of the course content',
    example: 'Learn the basics of TypeScript programming language',
    required: false,
  })
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty({
    description: 'The performance metrics of the course content',
    example: '85%',
    required: false,
  })
  @IsOptional()
  @IsString()
  performance: string;
}
