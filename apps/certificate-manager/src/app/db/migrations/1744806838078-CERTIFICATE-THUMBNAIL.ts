import { MigrationInterface, QueryRunner } from "typeorm";

export class CE<PERSON><PERSON><PERSON><PERSON>ETHUMBNAIL1744806838078 implements MigrationInterface {
    name = 'CERTIFICATETHUMBNAIL1744806838078'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate" ADD "thumbnail" character varying`);
        await queryRunner.query(`ALTER TABLE "certificate" ALTER COLUMN "display_performance" SET DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "certificate" ALTER COLUMN "display_total_time" SET DEFAULT true`);
        await queryRunner.query(`ALTER TABLE "certificate" ALTER COLUMN "display_conclusion_date" SET DEFAULT true`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate" ALTER COLUMN "display_conclusion_date" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "certificate" ALTER COLUMN "display_total_time" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "certificate" ALTER COLUMN "display_performance" DROP DEFAULT`);
        await queryRunner.query(`ALTER TABLE "certificate" DROP COLUMN "thumbnail"`);
    }

}
