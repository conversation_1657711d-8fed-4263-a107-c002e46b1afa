import { MigrationInterface, QueryRunner } from "typeorm";

export class LABELSDISPLAY1740415477304 implements MigrationInterface {
    name = 'LABELSDISPLAY1740415477304'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate" ADD "hide_labels" boolean`);
        await queryRunner.query(`ALTER TABLE "certificate" ADD "background_image_en" character varying`);
        await queryRunner.query(`ALTER TABLE "certificate" ADD "background_image_es" character varying`);
        await queryRunner.query(`ALTER TYPE "public"."certificate_element_type_enum" RENAME TO "certificate_element_type_enum_old"`);
        await queryRunner.query(`CREATE TYPE "public"."certificate_element_type_enum" AS ENUM('logo', 'title', 'weCertifyLabel', 'holderName', 'hasCompletedLabel', 'courseName', 'signedByLabel', 'performanceInfo', 'totalTime', 'conclusionDate')`);
        await queryRunner.query(`ALTER TABLE "certificate_element" ALTER COLUMN "type" TYPE "public"."certificate_element_type_enum" USING "type"::"text"::"public"."certificate_element_type_enum"`);
        await queryRunner.query(`DROP TYPE "public"."certificate_element_type_enum_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."certificate_element_type_enum_old" AS ENUM('logo', 'title', 'weCertifyLabel', 'holderName', 'hasCompletedLabel', 'courseName', 'signedByLabel', 'performanceInfo')`);
        await queryRunner.query(`ALTER TABLE "certificate_element" ALTER COLUMN "type" TYPE "public"."certificate_element_type_enum_old" USING "type"::"text"::"public"."certificate_element_type_enum_old"`);
        await queryRunner.query(`DROP TYPE "public"."certificate_element_type_enum"`);
        await queryRunner.query(`ALTER TYPE "public"."certificate_element_type_enum_old" RENAME TO "certificate_element_type_enum"`);
        await queryRunner.query(`ALTER TABLE "certificate" DROP COLUMN "background_image_es"`);
        await queryRunner.query(`ALTER TABLE "certificate" DROP COLUMN "background_image_en"`);
        await queryRunner.query(`ALTER TABLE "certificate" DROP COLUMN "hide_labels"`);
    }

}
