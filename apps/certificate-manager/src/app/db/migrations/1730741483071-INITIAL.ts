import { MigrationInterface, QueryRunner } from "typeorm";

export class INITIAL1730741483071 implements MigrationInterface {
    name = 'INITIAL1730741483071'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."certificate_template_enum" AS ENUM('trail', 'mission')`);
        await queryRunner.query(`CREATE TYPE "public"."certificate_orientation_enum" AS ENUM('portrait', 'landscape')`);
        await queryRunner.query(`CREATE TABLE "certificate" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "name" character varying NOT NULL, "default" boolean NOT NULL, "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "signed_by" character varying, "template" "public"."certificate_template_enum" NOT NULL, "orientation" "public"."certificate_orientation_enum" NOT NULL DEFAULT 'landscape', "display_performance" boolean, "display_total_time" boolean, "display_conclusion_date" boolean, "text_color" character varying, "background_color" character varying, "background_image" character varying, "brand_image" character varying, "workspace_id" character varying NOT NULL, CONSTRAINT "PK_8daddfc65f59e341c2bbc9c9e43" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "certificate_learn_content" ("learn_content_id" character varying NOT NULL, "workspace_id" character varying NOT NULL, "certificate_id" uuid, CONSTRAINT "PK_86b74b1e18e4353d2172183bc57" PRIMARY KEY ("learn_content_id", "workspace_id"))`);
        await queryRunner.query(`ALTER TABLE "certificate_learn_content" ADD CONSTRAINT "FK_fb83215b67aa24610a23466caea" FOREIGN KEY ("certificate_id") REFERENCES "certificate"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_learn_content" DROP CONSTRAINT "FK_fb83215b67aa24610a23466caea"`);
        await queryRunner.query(`DROP TABLE "certificate_learn_content"`);
        await queryRunner.query(`DROP TABLE "certificate"`);
        await queryRunner.query(`DROP TYPE "public"."certificate_orientation_enum"`);
        await queryRunner.query(`DROP TYPE "public"."certificate_template_enum"`);
    }

}
