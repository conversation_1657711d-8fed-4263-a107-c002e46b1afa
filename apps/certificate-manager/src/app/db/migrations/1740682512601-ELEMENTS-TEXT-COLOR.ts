import { MigrationInterface, QueryRunner } from "typeorm";

export class ELEMENTSTEXTCOLOR1740682512601 implements MigrationInterface {
    name = 'ELEMENTSTEXTCOLOR1740682512601'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_element" ADD "textColor" text`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_element" DROP COLUMN "textColor"`);
    }

}
