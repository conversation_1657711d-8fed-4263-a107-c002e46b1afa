import { MigrationInterface, QueryRunner } from "typeorm";

export class HIDDENELEMENTS1746450638650 implements MigrationInterface {
    name = 'HIDDENELEMENTS1746450638650'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_element" ADD "hidden" boolean`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_element" DROP COLUMN "hidden"`);
    }

}
