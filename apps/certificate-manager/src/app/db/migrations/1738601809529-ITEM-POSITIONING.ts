import { MigrationInterface, QueryRunner } from "typeorm";

export class ITEMPOSITIONING1738601809529 implements MigrationInterface {
    name = 'ITEMPOSITIONING1738601809529'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."certificate_element_type_enum" AS ENUM('logo', 'title', 'weCertifyLabel', 'holderName', 'hasCompletedLabel', 'courseName', 'signedByLabel', 'performanceInfo')`);
        await queryRunner.query(`CREATE TABLE "certificate_element" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "xPos" double precision NOT NULL, "yPos" double precision NOT NULL, "type" "public"."certificate_element_type_enum" NOT NULL, "certificateId" uuid, CONSTRAINT "PK_57fb956cc2469158f95f52c4c5c" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "certificate_element" ADD CONSTRAINT "FK_4dcfdc5dc2fb5951eaf1b34de02" FOREIGN KEY ("certificateId") REFERENCES "certificate"("id") ON DELETE CASCADE ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "certificate_element" DROP CONSTRAINT "FK_4dcfdc5dc2fb5951eaf1b34de02"`);
        await queryRunner.query(`DROP TABLE "certificate_element"`);
        await queryRunner.query(`DROP TYPE "public"."certificate_element_type_enum"`);
    }

}
