import { Controller } from '@nestjs/common';
import { CertificateGenerateDto } from '../dtos';
import { PdfService } from '../services';
import { GrpcMethod } from '@nestjs/microservices';

@Controller()
export class PdfController {
  constructor(private readonly pdfService: PdfService) {}

  @GrpcMethod('PdfService', 'GenerateCertificate')
  generateCertificate(certificateData: CertificateGenerateDto) {
    return this.pdfService.generateCertificatePdf(certificateData);
  }
}
