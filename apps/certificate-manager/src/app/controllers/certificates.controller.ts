import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  FileTypeValidator,
  Get,
  Headers,
  HttpCode,
  MaxFileSizeValidator,
  Param,
  Patch,
  Post,
  UploadedFiles,
  UseInterceptors,
} from '@nestjs/common';
import { CertificateCreateDto, CertificateItemDto, SetDefaultCertificateDto, VinculateCertificateDto } from '../dtos';
import { CertificatesService } from '../services';
import { KONQUEST_ADMIN_ROLES, PaginatedResponseDto, Roles } from '@keeps-node-apis/@core';
import { FileFieldsInterceptor } from '@nestjs/platform-express';
import { CertificateUpdateDto } from '../dtos/certificate-update.dto';
import { CertificateImagesParsePipe } from '../pipes/certificate-images-parse-pipe.service';
import { IMAGE_MIME_TYPE_REGEX, MAX_FILE_SIZE_IN_BYTES } from '../config/file-upload.config';
import { UploadedFilesDto } from '../dtos/uploaded-files.dto';
import { Paginate, PaginatedSwaggerDocs, PaginateQuery } from 'nestjs-paginate';
import { CERTIFICATES_QUERY_CONFIG } from '../certificates.query-config';
import { plainToInstance } from 'class-transformer';

const IMAGE_PARSE_PIPE = new CertificateImagesParsePipe({
  validators: [
    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE_IN_BYTES, message: 'File size should be less than 5MB' }),
    new FileTypeValidator({ fileType: IMAGE_MIME_TYPE_REGEX }),
  ],
  fileIsRequired: false,
});

const CERTIFICATE_IMAGES_INTERCEPTOR = FileFieldsInterceptor([
  { name: 'backgroundImage', maxCount: 1 },
  { name: 'brandImage', maxCount: 1 },
]);

@Controller('certificates')
export class CertificatesController {
  constructor(private certificatesService: CertificatesService) {}

  @Roles(KONQUEST_ADMIN_ROLES)
  @Post()
  @UseInterceptors(CERTIFICATE_IMAGES_INTERCEPTOR)
  createCertificate(
    @Headers('x-client') workspace_id: string,
    @Body() certificateDto: CertificateCreateDto,
    @UploadedFiles(IMAGE_PARSE_PIPE)
    files?: UploadedFilesDto,
  ) {
    const certificate: CertificateCreateDto = { ...certificateDto, workspaceId: workspace_id };
    const backgroundImage = files?.backgroundImage?.at(0);
    const brandImage = files?.brandImage?.at(0);
    return this.certificatesService.createCertificate(certificate, backgroundImage, brandImage);
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @UseInterceptors(CERTIFICATE_IMAGES_INTERCEPTOR)
  @Patch('/:id')
  updateCertificate(
    @Param('id') certificateID: string,
    @Headers('x-client') workspace_id: string,
    @Body() certificateUpdateDto: CertificateUpdateDto,
    @UploadedFiles(IMAGE_PARSE_PIPE)
    files?: UploadedFilesDto,
  ) {
    const backgroundImage = files?.backgroundImage?.at(0);
    const brandImage = files?.brandImage?.at(0);
    return this.certificatesService.updateCertificate(
      certificateID,
      workspace_id,
      certificateUpdateDto,
      backgroundImage,
      brandImage,
    );
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @Get('/:id')
  getById(@Param('id') certificateID: string, @Headers('x-client') workspace_id: string): Promise<CertificateItemDto> {
    return this.certificatesService.getById(certificateID, workspace_id);
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @PaginatedSwaggerDocs(CertificateItemDto, CERTIFICATES_QUERY_CONFIG)
  @Get()
  async list(@Headers('x-client') workspace_id: string, @Paginate() query: PaginateQuery) {
    const result = await this.certificatesService.list(workspace_id, query);
    return plainToInstance(PaginatedResponseDto, {
      ...result,
      data: result.data.map((item) => plainToInstance(CertificateItemDto, item)),
    });
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @Delete('/:id')
  deleteCertificate(@Param('id') certificateID: string, @Headers('x-client') workspace_id: string) {
    return this.certificatesService.delete(certificateID, workspace_id);
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @Post('/toggle-default')
  @HttpCode(204)
  async toggleDefault(
    @Headers('x-client') workspace_id: string,
    @Body() defaultCertificateDto: SetDefaultCertificateDto,
  ) {
    const result = await this.certificatesService.toggleDefaultCertificate(
      defaultCertificateDto.certificateId,
      workspace_id,
    );

    if (!result) {
      throw new BadRequestException('Failure to toggle default certificate');
    }
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @Post('/vinculate')
  vinculate(@Headers('x-client') workspace_id: string, @Body() vinculateCertificateDto: VinculateCertificateDto) {
    return this.certificatesService.vinculateCertificate(workspace_id, vinculateCertificateDto);
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @Delete('/vinculate/:id')
  desvinculate(@Headers('x-client') workspace_id: string, @Param('id') learnContentId: string) {
    return this.certificatesService.desvinculateCertificate(workspace_id, learnContentId);
  }

  @Roles(KONQUEST_ADMIN_ROLES)
  @Get('/vinculate/:id')
  getByLearnContentId(@Headers('x-client') workspace_id: string, @Param('id') learnContentId: string) {
    return this.certificatesService.getLearnContentCertificate(workspace_id, learnContentId);
  }
}
