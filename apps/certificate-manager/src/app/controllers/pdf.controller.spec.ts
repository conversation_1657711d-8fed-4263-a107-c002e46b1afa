import { PdfController } from './pdf.controller';
import { PdfService } from '../services';
import { CertificateGenerateDto, PROTO_CERTIFICATE_TEMPLATE } from '../dtos';
import { Chance } from 'chance';

describe('PdfController', () => {
  let controller: PdfController;
  let pdfServiceMock: jest.Mocked<PdfService>;
  const chance = new Chance();

  beforeEach(async () => {
    pdfServiceMock = {
      generateCertificatePdf: jest.fn().mockResolvedValue({}),
    } as unknown as jest.Mocked<PdfService>;
    controller = new PdfController(pdfServiceMock);
  });

  it('should generate a new PDF file', async () => {
    const workspaceId = chance.guid();
    const params = {
      template: PROTO_CERTIFICATE_TEMPLATE.MISSION,
      workspace_id: workspaceId,
    } as CertificateGenerateDto;
    await controller.generateCertificate(params);
    expect(pdfServiceMock.generateCertificatePdf).toHaveBeenCalledWith(params);
  });
});
