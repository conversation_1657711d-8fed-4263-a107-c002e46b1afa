import { CertificatesController } from './certificates.controller';
import { CertificatesService } from '../services';
import { CertificateCreateDto, SetDefaultCertificateDto } from '../dtos';
import { CertificateOrientation, CertificateTemplate } from '../models';
import { Chance } from 'chance';
import { CertificateUpdateDto } from '../dtos/certificate-update.dto';
import { PaginateQuery } from 'nestjs-paginate';

describe('CertificatesController', () => {
  let controller: CertificatesController;
  let certificatesServiceMock: jest.Mocked<CertificatesService>;
  const chance = new Chance();

  beforeEach(async () => {
    certificatesServiceMock = {
      createCertificate: jest.fn().mockResolvedValue({}),
      updateCertificate: jest.fn().mockResolvedValue({}),
      getById: jest.fn().mockResolvedValue({}),
      list: jest.fn().mockResolvedValue({ data: [] }),
      toggleDefaultCertificate: jest.fn().mockResolvedValue(true),
      uploadFile: jest.fn(),
    } as unknown as jest.Mocked<CertificatesService>;

    controller = new CertificatesController(certificatesServiceMock);
  });

  it('should create a new certificate', async () => {
    const workspaceId = chance.guid();
    const certificateDto: CertificateCreateDto = {
      name: 'mock_certificate',
      orientation: CertificateOrientation.PORTRAIT,
      template: CertificateTemplate.MISSION,
    };
    const expectedDto: CertificateCreateDto = { ...certificateDto, workspaceId };
    const mockBackgroundImage = { buffer: Buffer.from('') } as Express.Multer.File;
    const mockBrandImage = { buffer: Buffer.from('') } as Express.Multer.File;

    await controller.createCertificate(workspaceId, certificateDto, {
      backgroundImage: [mockBackgroundImage],
      brandImage: [mockBrandImage],
    });
    expect(certificatesServiceMock.createCertificate).toHaveBeenCalledWith(
      expectedDto,
      mockBackgroundImage,
      mockBrandImage,
    );
  });

  it('should update an existing certificate', async () => {
    const certificateDto: CertificateUpdateDto = {
      name: 'mock_certificate',
      orientation: CertificateOrientation.PORTRAIT,
      template: CertificateTemplate.MISSION,
    };
    const certificateId = chance.guid();
    const workspaceId = chance.guid();
    const mockBackgroundImage = { buffer: Buffer.from('') } as Express.Multer.File;
    const mockBrandImage = { buffer: Buffer.from('') } as Express.Multer.File;

    await controller.updateCertificate(certificateId, workspaceId, certificateDto, {
      backgroundImage: [mockBackgroundImage],
      brandImage: [mockBrandImage],
    });
    expect(certificatesServiceMock.updateCertificate).toHaveBeenCalledWith(
      certificateId,
      workspaceId,
      certificateDto,
      mockBackgroundImage,
      mockBrandImage,
    );
  });

  it('should retrieve an existing certificate', async () => {
    const certificateId = chance.guid();
    const workspaceId = chance.guid();
    await controller.getById(certificateId, workspaceId);
    expect(certificatesServiceMock.getById).toHaveBeenCalledWith(certificateId, workspaceId);
  });

  it('should retrieve the certificates list', () => {
    const workspaceId = 'mock_workspace_id';
    const listParamsDto = { search: 'mock_search', page: 1, limit: 10 } as PaginateQuery;
    controller.list(workspaceId, listParamsDto);
    expect(certificatesServiceMock.list).toHaveBeenCalledWith(workspaceId, listParamsDto);
  });

  describe('toggleDefault', () => {
    it('should toggle the default certificate', () => {
      const workspaceId = chance.guid();
      const requestDto: SetDefaultCertificateDto = { certificateId: chance.guid() };
      controller.toggleDefault(workspaceId, requestDto);
      expect(certificatesServiceMock.toggleDefaultCertificate).toHaveBeenCalledWith(
        requestDto.certificateId,
        workspaceId,
      );
    });

    it('should throw an exception if the operation was not successful', () => {
      const workspaceId = chance.guid();
      const requestDto: SetDefaultCertificateDto = { certificateId: chance.guid() };
      certificatesServiceMock.toggleDefaultCertificate.mockResolvedValueOnce(false);

      expect(async () => {
        await controller.toggleDefault(workspaceId, requestDto);
      }).rejects.toThrow('Failure to toggle default certificate');
    });
  });
});
