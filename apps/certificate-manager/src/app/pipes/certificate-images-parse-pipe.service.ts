import { Injectable, ParseFilePipe, PipeTransform } from '@nestjs/common';

@Injectable()
export class CertificateImagesParsePipe extends ParseFilePipe implements PipeTransform<Express.Multer.File[]> {
  async transform(files: Express.Multer.File[] | { [key: string]: Express.Multer.File[] }) {
    if (!files) {
      return [];
    }
    for (const file of Object.values(files)) {
      await super.transform(file);
    }

    return files;
  }
}
