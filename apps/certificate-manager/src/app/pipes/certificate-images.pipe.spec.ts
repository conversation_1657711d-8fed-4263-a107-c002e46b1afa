import { CertificateImagesParsePipe } from './certificate-images-parse-pipe.service';

describe('CertificateImagesPipe', () => {
  let pipe: CertificateImagesParsePipe;

  beforeEach(() => {
    pipe = new CertificateImagesParsePipe();
  });

  it('should parse a record of files', async () => {
    const mockFiles = {
      firstFile: [
        {
          fieldname: 'image',
          originalname: 'test.jpg',
          encoding: '7bit',
          mimetype: 'image/jpeg',
          buffer: Buffer.from('test'),
          size: 4,
        } as Express.Multer.File,
      ],
      secondFile: [
        {
          fieldname: 'image2',
          originalname: 'test2.jpg',
          mimetype: 'image/jpeg',
          buffer: Buffer.from('test2'),
          size: 5,
        } as Express.Multer.File,
      ],
    } as Record<string, Express.Multer.File[]>;

    const result = await pipe.transform(mockFiles);
    expect(result).toEqual(mockFiles);
  });

  it('should parse array of image files', async () => {
    const mockFiles = [
      {
        fieldname: 'image1',
        originalname: 'test1.jpg',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test1'),
        size: 5,
      },
      {
        fieldname: 'image2',
        originalname: 'test2.jpg',
        mimetype: 'image/jpeg',
        buffer: Buffer.from('test2'),
        size: 5,
      },
    ] as Express.Multer.File[];

    const result = await pipe.transform(mockFiles);
    expect(result).toEqual(mockFiles);
  });

  it('should return empty array when input is falsy', async () => {
    expect(await pipe.transform(null)).toEqual([]);
    expect(await pipe.transform(undefined)).toEqual([]);
  });
});
