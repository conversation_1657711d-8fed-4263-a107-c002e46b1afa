import { ArgumentsHost, Catch, ExceptionFilter, HttpExceptionBody, HttpStatus } from '@nestjs/common';
import { LearnContentCertificateNotFoundException } from '../exceptions/learn-content-certificate-not-found.exception';
import { Response } from 'express';

@Catch(LearnContentCertificateNotFoundException)
export class LearnContentNotFoundFilter implements ExceptionFilter {
  catch(exception: LearnContentCertificateNotFoundException, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const httpStatus = HttpStatus.NOT_FOUND;

    const responseBody: HttpExceptionBody = {
      statusCode: httpStatus,
      error: 'Not Found',
      message: exception.message || 'content not found',
    };

    response.status(httpStatus).json(responseBody);
  }
}
