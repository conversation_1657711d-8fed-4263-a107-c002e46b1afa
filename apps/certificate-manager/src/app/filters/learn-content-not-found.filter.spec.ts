import { ArgumentsHost, NotFoundException } from '@nestjs/common';
import { LearnContentNotFoundFilter } from './learn-content-not-found.filter';
import { LearnContentCertificateNotFoundException } from '../exceptions/learn-content-certificate-not-found.exception';
import { Chance } from 'chance';

describe('LearnContentNotFoundFilter', () => {
  let filter: LearnContentNotFoundFilter;
  let mockResponse: any;
  let mockArgumentsHost: ArgumentsHost;
  const chance = new Chance();
  const workspaceId = chance.guid();
  const learnContentId = chance.guid();

  beforeEach(() => {
    filter = new LearnContentNotFoundFilter();

    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };

    mockArgumentsHost = {
      switchToHttp: jest.fn().mockReturnThis(),
      getResponse: jest.fn().mockReturnValue(mockResponse),
    } as unknown as ArgumentsHost;
  });

  it('should throw NotFoundException with the formatted message', () => {
    const exception = new LearnContentCertificateNotFoundException(learnContentId, workspaceId);

    try {
      filter.catch(exception, mockArgumentsHost);
    } catch (error) {
      expect(error).toBeInstanceOf(NotFoundException);
      expect(error.message).toBe(
        `Certificate for learn content with ID ${learnContentId} not found in workspace ${workspaceId}`,
      );
    }
  });

  it('should throw NotFoundException with default message if exception message is undefined', () => {
    const exception = new Error();

    try {
      filter.catch(exception, mockArgumentsHost);
    } catch (error) {
      expect(error).toBeInstanceOf(NotFoundException);
      expect(error.message).toBe('content not found');
    }
  });
});
