import { ClassSerializerInterceptor, Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';

import { TypeOrmErrorFilter } from '@keeps-node-apis/@core';
import { MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app/app.module';
import { CUSTOM_CERTIFICATES_GRPC_OPTIONS } from './app/custom-certificates-grpc-options';
import { LearnContentNotFoundFilter } from './app/filters/learn-content-not-found.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);
  app.useGlobalPipes(new ValidationPipe({ whitelist: true, transform: true }));
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));
  app.useGlobalFilters(new LearnContentNotFoundFilter(), new TypeOrmErrorFilter());

  app.enableCors({
    allowedHeaders: '*',
    origin: '*',
    credentials: true,
  });

  app.connectMicroservice<MicroserviceOptions>(CUSTOM_CERTIFICATES_GRPC_OPTIONS);
  await app.startAllMicroservices();

  const config = new DocumentBuilder()
    .setTitle('Certificate Manager')
    .setDescription('Manage Workspace Custom Certificates')
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('docs', app, document);

  const port = process.env.PORT || 3000;
  await app.listen(port);
  Logger.log(`🚀 Application is running on: http://localhost:${port}/${globalPrefix}`);
  Logger.log(`📝 Certificate Manager Docs is running on: http://localhost:${port}/docs`);
  Logger.log(`🚀 Certificate manager gRPC microservice is running on: grpc://localhost:50051`);
}

bootstrap();
