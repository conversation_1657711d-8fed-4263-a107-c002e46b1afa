{"name": "certificate-manager", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/certificate-manager/src", "projectType": "application", "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/certificate-manager", "main": "apps/certificate-manager/src/main.ts", "tsConfig": "apps/certificate-manager/tsconfig.app.json", "assets": ["apps/certificate-manager/src/assets"], "isolatedConfig": true, "webpackConfig": "apps/certificate-manager/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "certificate-manager:build"}, "configurations": {"development": {"buildTarget": "certificate-manager:build:development"}, "production": {"buildTarget": "certificate-manager:build:production"}}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/certificate-manager/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/certificate-manager/jest.config.ts", "passWithNoTests": true}, "configurations": {"dev": {"ci": true, "codeCoverage": true, "coverageReporters": ["html", "text-summary", "lcov"]}}, "defaultConfiguration": "dev"}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "certificate-manager", "hostUrl": "https://sonar.keepsdev.com", "projectKey": "certificate-manager-api", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "apps/certificate-manager/src/app/db/**,apps/certificate-manager/src/app/config/**,apps/certificate-manager/src/assets/**,libs/**", "extra": {"sonar.coverage.exclusions": "apps/certificate-manager/src/app/db/**,apps/certificate-manager/src/app/config/**,apps/certificate-manager/src/**/*.module.ts,apps/certificate-manager/src/main.ts,apps/certificate-manager/src/**/index.ts", "sonar.cpd.exclusions": "apps/certificate-manager/src/app/db/**,apps/certificate-manager/src/assets/**", "sonar.testExecutionReportPaths": "coverage/certificate-manager/jest-sonar.xml", "sonar.plugins.downloadOnlyRequired": "true"}}}, "build-migration-config": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "isolatedConfig": true, "webpackConfig": "apps/certificate-manager/webpack.config.js", "outputPath": "dist/apps/typeorm-migration", "main": "apps/certificate-manager/src/app/db/typeorm.config.ts", "tsConfig": "apps/certificate-manager/tsconfig.app.json"}}, "typeorm-generate-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/certificate-manager", "commands": ["npx typeorm migration:generate -d ../../dist/apps/typeorm-migration/main.js ./src/app/db/migrations/{args.migration-name}"]}, "dependsOn": ["build-migration-config"]}, "typeorm-run-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/certificate-manager", "commands": ["npx typeorm -d ../../dist/apps/typeorm-migration/main.js migration:run"]}, "dependsOn": ["build-migration-config"]}}, "tags": []}