version: '3.8'

services:
  api:
    image: keeps-certificate-manager:latest
    container_name: keeps-certificate-manager-api
    build:
      context: ../../
      dockerfile: apps/certificate-manager/.docker/dev/Dockerfile
    ports:
      - '3000:3000'
      - '50051:50051'
    env_file:
      - ./.env
    volumes:
      - ../../:/usr/src/app
    command: ./apps/certificate-manager/.docker/dev/start.sh
    depends_on:
      - db
    init: true

  db:
    image: keeps-certificate-manager-db
    container_name: keeps-certificate-manager-db
    tty: true
    build:
      context: .
      dockerfile: .docker/postgres/Dockerfile
    env_file:
      - ./.env
    volumes:
      - .docker/db-data:/var/lib/postgresql/data
      - .docker/postgres/:/docker-entrypoint-initdb.d
    ports:
      - '5432:5432'
