NODE_ENV=development
I18N_PATH=assets/i18n
TEMPLATES_PATH=assets/templates

# KEYCLOAK
AUTH_URL=https://iam.keepsdev.com/auth/
AUTH_REALM=keeps-dev
AUTH_CLIENT_ID=keeps-certificate-manager-api-stage
AUTH_CLIENT_SECRET=...
AUTH_DEBUG=true
AUTH_REALM_PUBLIC_KEY=*****

# USER AUTHORIZATION
KONQUEST_APPLICATION_ID=0abf08ea-d252-4d7c-ab45-ab3f9135c288

## MyAccount
MYACCOUNT_API_URL=https://learning-platform-api-stage.keepsdev.com/myaccount-v2
OLD_MYACCOUNT_API_URL=https://learning-platform-api-stage.keepsdev.com/myaccount
MYACCOUNT_SUPER_TOKEN=...

# DB
DB_HOST=db
DB_PORT=5432
DB_NAME=certificate_manager_dev_db
DB_USER=postgres
DB_PASS=postgres
DB_DIALECT=postgres
DB_DEBUG=true
# SET TRUE ONLY TO LOCAL DATABASE
MIGRATIONS_RUN=false

# POSTGRES
POSTGRES_PASSWORD=postgres

#AWS S3
AWS_S3_ACCESS_KEY_ID=
AWS_S3_SECRET_ACCESS_KEY=
AWS_S3_REGION="us-east-1"
AWS_S3_BUCKET=keeps-media-stage
AWS_S3_BUCKET_PATH=certificate-manager
AWS_S3_CDN_URL=https://media-stage.keepsdev.com
