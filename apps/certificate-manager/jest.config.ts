/* eslint-disable */
export default {
  displayName: 'certificate-manager',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/certificate-manager',
  coverageReporters: ['text-summary', ['text', { skipFull: true }], ['lcovonly', { projectRoot: __dirname }]],
  reporters: ['default', ['@casualbot/jest-sonar-reporter', { outputDirectory: 'coverage/certificate-manager' }]],
  coverageThreshold: {
    global: {
      branches: 0,
      functions: 0,
      lines: 0,
      statements: 0,
    },
  },
};
