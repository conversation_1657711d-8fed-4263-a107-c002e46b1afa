FROM node:20-slim

# Install latest chrome package and fonts to support major charsets.
RUN apt-get update \
    && apt-get install -y wget gnupg \
    && wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && sh -c 'echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google.list' \
    && apt-get update \
    && apt-get install -y google-chrome-stable fonts-ipafont-gothic fonts-freefont-ttf libxss1 \
      --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

ENV NODE_ENV=development \
    NX_NO_CLOUD=true \
    PUPPETEER_EXECUTABLE_PATH=/usr/bin/google-chrome \
    PATH=/usr/src/app/node_modules/.bin:$PATH

RUN npm install -g @nestjs/cli

USER node

WORKDIR /usr/src/app

EXPOSE 3000
EXPOSE 50051

CMD ["tail", "-f", "/dev/null"]
