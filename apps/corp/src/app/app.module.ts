import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DomainModule } from './domain/domain.module';
import { DomainV2Module } from './v2/domain/domain-v2.module';
import { CONFIG_SCHEMA } from './v2/infra/config/config-schema';
import { APP_GUARD } from '@nestjs/core';
import { RolesGuard, TenantGuard } from '@keeps-node-apis/@core';
import { AuthGuard, KeycloakConnectConfig, KeycloakConnectModule } from 'nest-keycloak-connect';
import AuthConfig from './v2/infra/config/auth.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      validationSchema: CONFIG_SCHEMA,
      validationOptions: {
        abortEarly: true,
      },
      isGlobal: true,
      cache: true,
      load: [AuthConfig],
    }),
    KeycloakConnectModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<KeycloakConnectConfig>('auth'),
      }),
    }),
    DomainModule,
    DomainV2Module,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: AuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: TenantGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
  ],
})
export class AppModule {}
