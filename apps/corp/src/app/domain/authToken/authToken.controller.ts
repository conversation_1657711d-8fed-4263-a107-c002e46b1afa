import { Body, Controller, Post } from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';
import { ExtractParams, RestParams, SkipTenant } from '@keeps-node-apis/@core';
import { AuthTokenDto } from './dto/authToken.dto';
import { AuthTokenService } from './authToken.service';
import { AuthToken } from './entities/authToken.entity';
import { Public } from 'nest-keycloak-connect';

@Controller('auth')
export class AuthTokenController {
  constructor(private readonly authTokenService: AuthTokenService) {}

  @Post()
  @Public()
  @SkipTenant()
  @ApiOkResponse({
    type: AuthToken,
  })
  create(@ExtractParams() params: RestParams, @Body() authTokenDto: AuthTokenDto) {
    return this.authTokenService.create(params, authTokenDto);
  }
}
