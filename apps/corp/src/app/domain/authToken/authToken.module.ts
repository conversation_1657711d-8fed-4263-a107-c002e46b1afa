import { Module } from '@nestjs/common';
import { AuthTokenService } from './authToken.service';
import { AuthTokenController } from './authToken.controller';
import { AuthModule } from '../../auth/auth.module';
import { RestModule } from '@keeps-node-apis/@core';

@Module({
  imports: [AuthModule, RestModule],
  controllers: [AuthTokenController],
  providers: [AuthTokenService],
})
export class AuthTokenModule {}
