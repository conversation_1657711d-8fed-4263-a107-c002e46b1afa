import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RestClient, RestParams } from '@keeps-node-apis/@core';
import { AuthTokenDto } from './dto/authToken.dto';
import { AuthToken } from './entities/authToken.entity';

@Injectable()
export class AuthTokenService {
  readonly baseUrl: any;
  constructor(
    private rest: RestClient,
    private configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get('MYACCOUNT_V2_URL') + '/auth/login';
  }
  create(params: RestParams, authTokenDto: AuthTokenDto) {
    return this.rest.post<AuthToken>(`${this.baseUrl}`, authTokenDto, params);
  }
}
