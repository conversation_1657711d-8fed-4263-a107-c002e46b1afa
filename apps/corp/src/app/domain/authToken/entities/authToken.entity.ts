import { ApiProperty } from '@nestjs/swagger';

export class AuthToken {
  @ApiProperty()
  access_token: string;

  @ApiProperty()
  expires_in: number;

  @ApiProperty()
  refresh_expires_in: number;

  @ApiProperty()
  refresh_token: string;

  @ApiProperty()
  token_type: string;

  @ApiProperty()
  session_state: string;

  @ApiProperty()
  scope: string;

  @ApiProperty()
  email: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  preferred_username: string;

  @ApiProperty()
  given_name: string;

  @ApiProperty()
  family_name: string;
}
