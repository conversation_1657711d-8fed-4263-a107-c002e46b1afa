import { ApiProperty } from '@nestjs/swagger';

export class GetMissionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  user_creator: {
    id: string;
    name: string;
    avatar: string;
  };

  @ApiProperty()
  live: boolean;

  @ApiProperty()
  presential: string;

  @ApiProperty()
  external: string;

  @ApiProperty()
  external_course_url: string;

  @ApiProperty()
  provider: string;

  @ApiProperty()
  learning_trail_linked: boolean;

  @ApiProperty()
  mission_type: {
    id: string;
    name: string;
    image: string;
  };

  @ApiProperty()
  mission_category: {
    id: string;
    name: string;
  };

  @ApiProperty()
  rating_avg: number;

  @ApiProperty()
  users_enrolled: number;

  @ApiProperty()
  enrollments_accepted: number;

  @ApiProperty()
  workspace_source_id: string;

  @ApiProperty()
  created_date: Date;

  @ApiProperty()
  updated_date: Date;

  @ApiProperty()
  deleted: boolean;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  holder_image: string;

  @ApiProperty()
  vertical_holder_image: string;

  @ApiProperty()
  thumb_image: string;

  @ApiProperty()
  summary: string;

  @ApiProperty()
  duration_time: string;

  @ApiProperty()
  points: string;

  @ApiProperty()
  enrollment_goal_duration_days: string;

  @ApiProperty()
  development_status: string;

  @ApiProperty()
  language: string;

  @ApiProperty()
  minimum_performance: number;
}
