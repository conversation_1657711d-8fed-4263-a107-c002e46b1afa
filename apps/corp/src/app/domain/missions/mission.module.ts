import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { RestModule } from '@keeps-node-apis/@core';
import { MissionController } from './mission.controller';
import { MissionService } from './mission.service';

@Module({
  imports: [AuthModule, RestModule],
  controllers: [MissionController],
  providers: [MissionService],
})
export class MissionsModule {}
