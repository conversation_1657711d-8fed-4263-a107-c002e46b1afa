import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';
import { AuthGuard } from 'nest-keycloak-connect';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';
import { ApiFilters } from '../../common/decorators/filter-api.decorator';
import { MissionService } from './mission.service';
import { GetMissionDto } from './dto/get-mission.dto';

@UseGuards(AuthGuard)
@Controller('missions')
export class MissionController {
  constructor(private readonly missionsService: MissionService) {}

  @Get()
  @ApiFilters(['search', 'name'])
  @ApiOkResponse({
    type: [GetMissionDto],
  })
  findAll(@ExtractParams() params: RestParams) {
    return this.missionsService.findAll(params);
  }

  @Get(':id')
  @ApiOkResponse({
    type: GetMissionDto,
  })
  findOne(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.missionsService.findOne(params, id);
  }
}
