import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RestClient, RestParams } from '@keeps-node-apis/@core';
import { GetMissionDto } from './dto/get-mission.dto';

@Injectable()
export class MissionService {
  readonly baseUrl: any;
  constructor(
    private rest: RestClient,
    private configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get('KONQUEST_URL') + '/missions';
  }

  findAll(params: RestParams) {
    return this.rest.get<GetMissionDto[]>(this.baseUrl, params);
  }

  findOne(params: RestParams, id: string) {
    return this.rest.get<GetMissionDto>(`${this.baseUrl}/${id}`, params);
  }
}
