import { Body, Controller, Delete, Get, Param, Patch, Put, Post, HttpCode } from '@nestjs/common';
import { ApiOkResponse, ApiBody, ApiBearerAuth, ApiNoContentResponse } from '@nestjs/swagger';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';
import { CreateUserRoleDto } from './dto/create-user-role.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { UsersService } from './users.service';
import { UserRole } from './entities/user.entity';
import { User } from '../workspaces/entities/user.entity';

@Controller('users')
@ApiBearerAuth()
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @Get()
  @ApiOkResponse({
    type: [User],
  })
  findAll(@ExtractParams() params: RestParams) {
    return this.usersService.findAll(params);
  }

  @Get(':id')
  findOne(@Param('id') id: string, @ExtractParams() params: RestParams) {
    return this.usersService.findOne(id, params);
  }

  @Patch(':id')
  partialUpdate(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto, @ExtractParams() params: RestParams) {
    return this.usersService.partialUpdate(id, updateUserDto, params);
  }

  @Put(':id')
  update(@Param('id') id: string, @Body() updateUserDto: UpdateUserDto, @ExtractParams() params: RestParams) {
    return this.usersService.update(id, updateUserDto, params);
  }

  @Delete('/roles/:id')
  @HttpCode(204)
  @ApiNoContentResponse()
  removeUserRole(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.usersService.deleteUserRole(id, params);
  }

  @Post('/roles')
  @ApiBody({ type: CreateUserRoleDto })
  @ApiOkResponse({
    type: UserRole,
  })
  createUserRole(@ExtractParams() params: RestParams, @Body() createUserRoleDto: CreateUserRoleDto) {
    return this.usersService.createUserRole(params, createUserRoleDto);
  }
}
