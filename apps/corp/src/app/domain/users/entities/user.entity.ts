import { ApiProperty } from '@nestjs/swagger';
import { UserRoleStatic } from '../../workspaces/entities/user.entity';
import { UserWorkspace } from '../../workspaces/entities/user-workspace-role.entity';
import { Role } from '../../workspaces/entities/role.entity';
import { Defer } from '../../../common/utils/defer.generic';

export class UserRole {
  @ApiProperty()
  id: string;

  @ApiProperty({
    type: Role,
  })
  roles: Role;

  @ApiProperty({
    type: () => UserRoleStatic,
  })
  user: Defer<UserRoleStatic>;

  @ApiProperty({
    type: () => UserWorkspace,
  })
  workspace: Defer<UserWorkspace>;
}
