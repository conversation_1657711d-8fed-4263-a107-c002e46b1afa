import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateUserRoleDto } from './dto/create-user-role.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { RestClient, RestParams } from '@keeps-node-apis/@core';
import { UserRole } from './entities/user.entity';
import { User } from '../workspaces/entities/user.entity';

@Injectable()
export class UsersService {
  readonly baseUrl: any;
  constructor(
    private httpClient: RestClient,
    private configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get('MYACCOUNT_URL') + '/users';
  }

  findAll(params: RestParams) {
    return this.httpClient.get<User[]>(this.baseUrl, params);
  }

  findOne(id: string, params: RestParams) {
    return this.httpClient.get<User>(`${this.baseUrl}/${id}`, params);
  }

  partialUpdate(id: string, updateUserDto: UpdateUserDto, params: RestParams) {
    return this.httpClient.patch<User>(`${this.baseUrl}/${id}`, updateUserDto, params);
  }

  update(id: string, updateUserDto: UpdateUserDto, params: RestParams) {
    return this.httpClient.put<User>(`${this.baseUrl}/${id}`, updateUserDto, params);
  }

  deleteUserRole(id: string, params: RestParams) {
    console.log('[UsersService] deleteUserRole', id);
    return this.httpClient.delete<any>(`${this.baseUrl}/roles/${id}`, params);
  }
  createUserRole(params: RestParams, createUserRoleDto: CreateUserRoleDto) {
    return this.httpClient.post<UserRole>(`${this.baseUrl}/roles`, createUserRoleDto, params);
  }
}
