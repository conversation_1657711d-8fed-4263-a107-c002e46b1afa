import { IsString, IsEmail, IsOptional, IsBoolean, IsUUID, IsDateString, IsUrl } from 'class-validator';

export class UpdateUserDto {
  @IsString()
  @IsOptional()
  name?: string;

  @IsString()
  @IsOptional()
  nickname?: string;

  @IsEmail()
  @IsOptional()
  secondary_email?: string;

  @IsString()
  @IsOptional()
  phone?: string;

  @IsString()
  @IsOptional()
  gender?: string;

  @IsDateString()
  @IsOptional()
  birthday?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  country?: string;

  @IsString()
  @IsOptional()
  ein?: string;

  @IsUrl()
  @IsOptional()
  avatar?: string;

  @IsBoolean()
  @IsOptional()
  status?: boolean;

  @IsString()
  @IsOptional()
  job?: string;

  @IsUUID()
  @IsOptional()
  job_id?: string;

  @IsString()
  @IsOptional()
  director?: string;

  @IsString()
  @IsOptional()
  manager?: string;

  @IsString()
  @IsOptional()
  area_of_activity?: string;

  @IsUUID()
  @IsOptional()
  job_position?: string;

  @IsString()
  @IsOptional()
  job_function?: string;

  @IsUUID()
  @IsOptional()
  language_id?: string;

  @IsUUID()
  @IsOptional()
  related_user_leader_id?: string;

  @IsBoolean()
  @IsOptional()
  email_verified?: boolean;

  @IsString()
  @IsOptional()
  time_zone?: string;

  @IsDateString()
  @IsOptional()
  admission_date?: string;

  @IsString()
  @IsOptional()
  contract_type?: string;

  @IsString()
  @IsOptional()
  cpf?: string;

  @IsString()
  @IsOptional()
  education?: string;

  @IsString()
  @IsOptional()
  ethnicity?: string;

  @IsString()
  @IsOptional()
  hierarchical_level?: string;

  @IsString()
  @IsOptional()
  marital_status?: string;
}
