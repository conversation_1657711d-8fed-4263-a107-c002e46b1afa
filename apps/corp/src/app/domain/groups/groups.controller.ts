import { Controller, Get, Post, Param, UseGuards, Body } from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';
import { AuthGuard } from 'nest-keycloak-connect';
import { ApiFilters } from '../../common/decorators/filter-api.decorator';
import { GroupService } from './groups.service';
import { GetGroupsDto } from './dto/get-groups.dto';
import { UserGroupLinkDto } from './dto/create-groups-link.dto';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';

@UseGuards(AuthGuard)
@Controller('groups')
export class GroupController {
  constructor(private readonly groupsService: GroupService) {}

  @Get()
  @ApiFilters(['search'])
  @ApiOkResponse({
    type: [GetGroupsDto],
  })
  findAll(@ExtractParams() params: RestParams) {
    return this.groupsService.findAll(params);
  }

  @Post(':id/users')
  createUsersLink(
    @ExtractParams() params: RestParams,
    @Param('id') id: string,
    @Body() createUserDto: UserGroupLinkDto,
  ) {
    return this.groupsService.createUsersLink(params, id, createUserDto);
  }
}
