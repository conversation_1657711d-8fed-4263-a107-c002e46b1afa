import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsOptional, IsString, IsDate } from 'class-validator';

export class UserGroupLinkDto {
  @ApiProperty()
  @IsString({ each: true })
  users: string[];

  @ApiProperty()
  @IsOptional()
  @IsDate()
  enrollment_goal_date?: Date;

  @ApiProperty()
  @IsOptional()
  @IsBoolean()
  enrollment_required_mission?: boolean;

  @ApiProperty()
  @IsOptional()
  @IsString()
  regulatory_compliance_cycle_id?: string;
}
