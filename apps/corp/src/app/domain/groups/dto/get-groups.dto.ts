import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsString } from 'class-validator';

export class GetGroupsDto {
  @ApiProperty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsNumber()
  users: number;

  @ApiProperty()
  @IsNumber()
  missions: number;

  @ApiProperty()
  @IsNumber()
  learning_trails: number;

  @ApiProperty()
  @IsNumber()
  channels: number;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  workspace: string;

  @ApiProperty()
  @IsBoolean()
  deleted: boolean;
}
