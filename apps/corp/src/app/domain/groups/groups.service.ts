import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RestClient, RestParams } from '@keeps-node-apis/@core';
import { UserGroupLinkDto } from './dto/create-groups-link.dto';
import { GetGroupsDto } from './dto/get-groups.dto';

@Injectable()
export class GroupService {
  readonly baseUrl: any;
  constructor(
    private httpClient: RestClient,
    private configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get('KONQUEST_URL') + '/groups';
  }

  findAll(params: RestParams) {
    return this.httpClient.get<GetGroupsDto[]>(this.baseUrl, params);
  }

  createUsersLink(params: RestParams, id: string, createUserDto: UserGroupLinkDto) {
    return this.httpClient.post(`${this.baseUrl}/${id}/users`, createUserDto, params);
  }
}
