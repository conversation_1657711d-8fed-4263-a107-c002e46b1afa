import { Module } from '@nestjs/common';
import { WorkspacesService } from './workspaces.service';
import { WorkspacesController } from './workspaces.controller';
import { AuthModule } from '../../auth/auth.module';
import { RestModule } from '@keeps-node-apis/@core';

@Module({
  imports: [AuthModule, RestModule],
  controllers: [WorkspacesController],
  providers: [WorkspacesService],
})
export class WorkspacesModule {}
