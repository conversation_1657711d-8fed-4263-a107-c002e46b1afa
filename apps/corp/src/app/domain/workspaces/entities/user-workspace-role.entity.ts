import { ApiProperty, PickType } from '@nestjs/swagger';
import { Role } from './role.entity';
import { Workspace } from './workspace.entity';
import { Defer } from '../../../common/utils/defer.generic';

export class UserWorkspaceRole {
  @ApiProperty()
  id: string; // '1a863592-6847-4e66-ae63-c08a3d5954ca';

  @ApiProperty()
  role: Role;

  @ApiProperty({
    type: () => UserWorkspace,
  })
  workspace: Defer<UserWorkspace>;

  @ApiProperty()
  self_sign_up: boolean;
}

// PickType (@nestjs/swagger) seleciona os campos especificos da entidade.
export class UserWorkspace extends PickType(Workspace, ['id', 'name', 'icon_url', 'logo_url'] as const) {}
