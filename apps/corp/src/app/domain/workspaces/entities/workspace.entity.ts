import { ApiProperty } from '@nestjs/swagger';

export class Workspace {
  @ApiProperty()
  id: string; // "7b2c5110-14d8-4a55-b984-be4eb3b3fdbf",

  @ApiProperty()
  name: string; // "Cypress Teste",

  @ApiProperty()
  duns_number: string; // null,

  @ApiProperty()
  doc_number: string; // null,

  @ApiProperty()
  description: string; // null,

  @ApiProperty()
  status: boolean; // true,

  @ApiProperty()
  address: string; // null,

  @ApiProperty()
  city: string; // null,

  @ApiProperty()
  state: string; // null,

  @ApiProperty()
  post_code: string; // null,

  @ApiProperty()
  country: string; // null,

  @ApiProperty()
  icon_url: string; // "https://s3.amazonaws.com/keeps.myaccount.media.hml/company-icon/7b2c5110-14d8-4a55-b984-be4eb3b3fdbf",

  @ApiProperty()
  icon_svg_url: string; // null,

  @ApiProperty()
  logo_url: string; // "https://s3.amazonaws.com/keeps.myaccount.media.hml/company-logo/7b2c5110-14d8-4a55-b984-be4eb3b3fdbf",

  @ApiProperty()
  theme_id: string; // "theme-grey",

  @ApiProperty()
  theme_dark: boolean; // true,

  @ApiProperty()
  created_date: string; // "2022-02-23T21:22:35.169762",

  @ApiProperty()
  updated_date: string; // "2022-11-30T18:25:30.149757",

  @ApiProperty()
  company: string;
}
