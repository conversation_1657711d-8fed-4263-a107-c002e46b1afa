import { ApiProperty, OmitType, PickType } from '@nestjs/swagger';
import { Language } from './language.entity';
import { UserWorkspaceRole } from './user-workspace-role.entity';
import { Defer } from '../../../common/utils/defer.generic';

export class User {
  @ApiProperty()
  id: string; // 'a277acd5-4181-4bb7-a412-8b65463ef871';

  @ApiProperty()
  name: string; // '<PERSON>';

  @ApiProperty()
  nickname: string;

  @ApiProperty()
  email: string; // '<EMAIL>';

  @ApiProperty()
  email_verified: boolean;

  @ApiProperty()
  secondary_email: string; // '<EMAIL>';

  @ApiProperty()
  phone: string; // '************';

  @ApiProperty()
  avatar: string; // 'https://s3.amazonaws.com/keeps.myaccount.media.hml/user-avatar/a277acd5-4181-4bb7-a412-8b65463ef871-4.jpg';

  @ApiProperty()
  gender: string; // 'FEMALE';

  @ApiProperty()
  job: string;

  @ApiProperty()
  birthday: string; // '1977-06-16';

  @ApiProperty()
  address: string; // 'Floripa';

  @ApiProperty()
  country: string; // 'Brasil';

  @ApiProperty()
  ein: string;

  @ApiProperty()
  status: boolean;

  @ApiProperty()
  time_zone: string; // 'america/sao_paulo';

  @ApiProperty()
  created_date: string; // '2021-10-19T19:13:22.750958';

  @ApiProperty()
  updated_date: string; // '2023-03-01T20:57:20.935315';

  @ApiProperty({
    type: [UserWorkspaceRole],
  })
  roles: UserWorkspaceRole[];

  @ApiProperty()
  profile: any;

  @ApiProperty()
  language: Language;

  @ApiProperty({
    type: () => RelatedUser,
  })
  related_user_leader: Defer<RelatedUser>;

  password: string;

  temporary_password: boolean;

  director: string;

  manager: string;

  area_of_activity: string;

  job_function: string;
}

// OmitType (@nestjs/swagger) exclui os campos especificos da entidade.
export class RelatedUser extends OmitType(User, ['roles', 'profile', 'language', 'related_user_leader'] as const) {
  @ApiProperty()
  language: string; // 'ea636f50-fdc4-49b0-b2de-9e5905de456b';

  @ApiProperty()
  related_user_leader: string; // '67a1d5aa-5b29-4c5c-b717-2d50c348156f';
}

export class UserRoleStatic extends PickType(User, [
  'id',
  'name',
  'avatar',
  'country',
  'ein',
  'email',
  'job',
  'language',
  'phone',
] as const) {}
