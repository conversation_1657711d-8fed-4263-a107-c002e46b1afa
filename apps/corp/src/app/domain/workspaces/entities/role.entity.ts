import { ApiProperty } from '@nestjs/swagger';
import { Defer } from '../../../common/utils/defer.generic';

export class Role {
  @ApiProperty()
  id: string; // '3d010792-7119-4e14-bea3-5258a31f1ddc';

  @ApiProperty()
  name: string; // 'Smartzap Admin';

  @ApiProperty()
  key: string; // 'admin';

  @ApiProperty({
    type: () => RoleApplication,
  })
  application: Defer<RoleApplication>;
}

export class RoleApplication {
  @ApiProperty()
  id: string; // '84d6715e-9b75-436d-ad44-b74c5a7f6729';

  @ApiProperty()
  name: string; // 'Smartzap';
}
