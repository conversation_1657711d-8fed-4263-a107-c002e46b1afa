import { ApiProperty } from '@nestjs/swagger';

export class Company {
  @ApiProperty()
  id: string; // "fcd88158-39e8-4490-82a7-814804190584",

  @ApiProperty()
  name: string; // "Keeps",

  @ApiProperty()
  description: string; // "BOM DYA",

  @ApiProperty()
  status: boolean; // true,

  @ApiProperty()
  address: string; // null,

  @ApiProperty()
  city: string; // "Zona leste",

  @ApiProperty()
  state: string; // null,

  @ApiProperty()
  post_code: string; // null,

  @ApiProperty()
  country: string; // null,

  @ApiProperty()
  icon_url: string; // null,

  @ApiProperty()
  created_date: string; // "2022-07-14T17:33:59.243520",

  @ApiProperty()
  updated_date: string; // "2022-07-14T17:33:59.243532"
}
