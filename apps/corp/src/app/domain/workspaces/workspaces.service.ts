import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CreateWorkspaceUserDto } from './dto/create-workspace-user.dto';
import { UpdateUserWorkspaceDto } from './dto/update-workspace-user.dto';
import { RestClient, RestParams } from '@keeps-node-apis/@core';
import { User } from './entities/user.entity';
import { Workspace } from './entities/workspace.entity';
import { PageResponse } from '../../common/dto/page-response.dto';

@Injectable()
export class WorkspacesService {
  readonly baseUrl: any;
  constructor(
    private httpClient: RestClient,
    private configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get('MYACCOUNT_URL') + '/workspaces';
  }

  findAll(params: RestParams) {
    console.log('[WorkspacesService] findAll', params);
    return this.httpClient.get<Workspace[]>(this.baseUrl, params);
  }

  findOne(params: RestParams, id: string) {
    console.log('[WorkspacesService] findOne', params, id);
    return this.httpClient.get<Workspace>(`${this.baseUrl}/${id}`, params);
  }

  findUsers(params: RestParams, id: string) {
    console.log('[WorkspacesService] findUsers', params, id);
    return this.httpClient.get<PageResponse<User>>(`${this.baseUrl}/${id}/users`, params);
  }

  createUser(params: RestParams, id: string, createWorkspaceUser: CreateWorkspaceUserDto) {
    console.log('[WorkspacesService] createUser', params, id, createWorkspaceUser);
    return this.httpClient.post<User[]>(`${this.baseUrl}/${id}/users`, createWorkspaceUser, params);
  }

  findOneUser(params: RestParams, id: string, userId: string) {
    console.log('[WorkspacesService] findOneUser', params, id, userId);
    return this.httpClient.get<User>(`${this.baseUrl}/${id}/users/${userId}`, params);
  }

  updateUser(params: RestParams, id: string, idUser: string, updateUserWorkspaceDto: UpdateUserWorkspaceDto) {
    return this.httpClient.put<User>(`${this.baseUrl}/${id}/users/${idUser}`, updateUserWorkspaceDto, params);
  }

  partialUpdateUser(params: RestParams, id: string, idUser: string, updateUserWorkspaceDto: UpdateUserWorkspaceDto) {
    return this.httpClient.patch<User>(`${this.baseUrl}/${id}/users/${idUser}`, updateUserWorkspaceDto, params);
  }
}
