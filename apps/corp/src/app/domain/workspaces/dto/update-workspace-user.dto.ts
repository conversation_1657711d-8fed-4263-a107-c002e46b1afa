import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsOptional } from 'class-validator';

export class UpdateUserWorkspaceDto {
  @IsOptional()
  @ApiProperty()
  address: string;

  @IsOptional()
  @ApiProperty()
  birthday: string;

  @IsOptional()
  @ApiProperty()
  gender: string;

  @IsOptional()
  @ApiProperty()
  job: string;

  @IsOptional()
  @ApiProperty()
  name: string;

  @IsOptional()
  @ApiProperty()
  nickname: string;

  @IsOptional()
  @ApiProperty()
  phone: string;

  @IsOptional()
  @IsEmail()
  @ApiProperty()
  secondary_email: string;
}
