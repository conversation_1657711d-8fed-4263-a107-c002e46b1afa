import { ApiProperty, IntersectionType, OmitType, PartialType, PickType } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { ArrayMinSize, IsArray, IsEmail } from 'class-validator';
import { User } from '../entities/user.entity';

export class CreateUserWorkspaceRequiredDto extends PickType(User, ['name', 'email'] as const) {}

export class CreateUserWorkspaceOptionalDto extends OmitType(PartialType(User), [
  'id',
  'name',
  'email',
  'email_verified',
  'ein',
  'status',
  'created_date',
  'updated_date',
  'roles',
  'language',
  'related_user_leader',
  'password',
  'temporary_password',
] as const) {}

export class CreateUserWorkspaceDto extends IntersectionType(
  CreateUserWorkspaceRequiredDto,
  CreateUserWorkspaceOptionalDto,
) {
  @ApiProperty({ required: false })
  language: string;

  @IsEmail()
  @ApiProperty({ required: false })
  related_user_leader: string;
}

export class CreateWorkspaceUserDto {
  @IsArray()
  @ArrayMinSize(1)
  @ApiProperty()
  permissions: string[];

  @IsArray()
  @ArrayMinSize(1)
  @ApiProperty({
    type: [CreateUserWorkspaceDto],
  })
  @Type(() => CreateUserWorkspaceDto)
  users: CreateUserWorkspaceDto[];
}
