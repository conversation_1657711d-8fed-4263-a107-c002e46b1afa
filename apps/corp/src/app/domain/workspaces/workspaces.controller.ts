import { <PERSON>, Get, Param, UseGuards, Post, Body, Put, Patch } from '@nestjs/common';
import { ApiOkResponse, ApiBody } from '@nestjs/swagger';
import { AuthGuard } from 'nest-keycloak-connect';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';
import { CreateWorkspaceUserDto } from './dto/create-workspace-user.dto';
import { UpdateUserWorkspaceDto } from './dto/update-workspace-user.dto';
import { User } from './entities/user.entity';
import { Workspace } from './entities/workspace.entity';
import { WorkspacesService } from './workspaces.service';
import { ApiFilters } from '../../common/decorators/filter-api.decorator';
import { ApiPageResponse } from '../../common/decorators/page-response-api.decorator';

@UseGuards(AuthGuard)
@Controller('workspaces')
export class WorkspacesController {
  constructor(private readonly workspacesService: WorkspacesService) {}

  @Get()
  @ApiFilters(['search', 'name'])
  @ApiOkResponse({
    type: [Workspace],
  })
  findAll(@ExtractParams() params: RestParams) {
    return this.workspacesService.findAll(params);
  }

  @Get(':id')
  @ApiOkResponse({
    type: Workspace,
  })
  findOne(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.workspacesService.findOne(params, id);
  }

  @Get(':id/users')
  @ApiFilters(['search', 'email', 'name', 'application_id', 'role_id'])
  @ApiPageResponse(User)
  findWorkspaceUsers(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.workspacesService.findUsers(params, id);
  }

  @Post(':id/users')
  @ApiBody({ type: CreateWorkspaceUserDto })
  @ApiOkResponse({
    type: User,
    isArray: true,
  })
  createWorkspaceUser(
    @ExtractParams() params: RestParams,
    @Body() createWorkspaceUserDto: CreateWorkspaceUserDto,
    @Param('id') id: string,
  ) {
    createWorkspaceUserDto.users = createWorkspaceUserDto.users.map((user) => ({
      ...user,
      profile: {
        director: user.director,
        manager: user.manager,
        area_of_activity: user.area_of_activity,
        job_function: user.job_function,
      },
    }));

    return this.workspacesService.createUser(params, id, createWorkspaceUserDto);
  }

  @Get(':id/users/:userId')
  @ApiOkResponse({
    type: User,
  })
  findOneUser(@ExtractParams() params: RestParams, @Param('id') id: string, @Param('userId') userId: string) {
    return this.workspacesService.findOneUser(params, id, userId);
  }

  @Put(':id/users/:userId')
  @ApiBody({ type: UpdateUserWorkspaceDto })
  @ApiOkResponse({
    type: User,
  })
  updateWorkspaceUser(
    @ExtractParams() params: RestParams,
    @Param('id') id: string,
    @Param('userId') userId: string,
    @Body() updateUserWorkspaceDto: UpdateUserWorkspaceDto,
  ) {
    return this.workspacesService.updateUser(params, id, userId, updateUserWorkspaceDto);
  }

  @Patch(':id/users/:userId')
  @ApiBody({ type: UpdateUserWorkspaceDto })
  @ApiOkResponse({
    type: User,
  })
  partialUpdateWorkspaceUser(
    @ExtractParams() params: RestParams,
    @Param('id') id: string,
    @Param('userId') userId: string,
    @Body() updateUserWorkspaceDto: UpdateUserWorkspaceDto,
  ) {
    return this.workspacesService.partialUpdateUser(params, id, userId, updateUserWorkspaceDto);
  }
}
