import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RestClient, RestParams } from '@keeps-node-apis/@core';

@Injectable()
export abstract class BaseService {
  protected abstract getBaseUrl(): string;

  constructor(
    protected rest: RestClient,
    protected configService: ConfigService,
  ) {}

  findAll(params: RestParams) {
    return this.rest.get(this.getBaseUrl(), params);
  }

  findOne(params: RestParams, id: string) {
    return this.rest.get(`${this.getBaseUrl()}/${id}`, params);
  }
}
