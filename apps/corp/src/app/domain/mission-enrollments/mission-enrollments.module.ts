import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { RestModule } from '@keeps-node-apis/@core';
import { MissionEnrollmentController } from './mission-enrollments.controller';
import { MissionEnrollmentService } from './mission-enrollments.service';

@Module({
  imports: [AuthModule, RestModule],
  controllers: [MissionEnrollmentController],
  providers: [MissionEnrollmentService],
})
export class MissionEnrollmentModule {}
