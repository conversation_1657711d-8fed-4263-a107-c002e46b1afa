import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';
import { AuthGuard } from 'nest-keycloak-connect';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';
import { ApiFilters } from '../../common/decorators/filter-api.decorator';
import { MissionEnrollmentService } from './mission-enrollments.service';

@UseGuards(AuthGuard)
@Controller('mission-enrollments')
export class MissionEnrollmentController {
  constructor(private readonly missionEnrollmentService: MissionEnrollmentService) {}

  @Get()
  @ApiFilters(['search', 'name'])
  findAll(@ExtractParams() params: RestParams) {
    return this.missionEnrollmentService.findAll(params);
  }

  @Get(':id')
  findOne(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.missionEnrollmentService.findOne(params, id);
  }

  @Post()
  @ApiOkResponse({ description: 'Enrollments created' })
  enrollBatch(@ExtractParams() params: RestParams, @Body() data: any) {
    return this.missionEnrollmentService.enrollBatch(params, data);
  }
}
