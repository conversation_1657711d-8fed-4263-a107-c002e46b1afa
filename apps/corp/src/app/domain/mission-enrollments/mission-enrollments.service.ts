import { Injectable } from '@nestjs/common';
import { BaseService } from '../abstract.service';
import { RestParams } from '@keeps-node-apis/@core';

@Injectable()
export class MissionEnrollmentService extends BaseService {
  protected getBaseUrl(): string {
    return this.configService.get('KONQUEST_URL') + '/mission-enrollments';
  }

  enroll(params: RestParams, data: any) {
    return this.rest.post(this.getBaseUrl(), data, params);
  }

  enrollBatch(params: RestParams, data: any) {
    const batchUrl = this.getBaseUrl() + '/batch';
    return this.rest.post(batchUrl, data, params);
  }
}
