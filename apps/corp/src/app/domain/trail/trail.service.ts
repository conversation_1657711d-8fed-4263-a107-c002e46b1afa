import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RestClient, RestParams } from '@keeps-node-apis/@core';

@Injectable()
export class TrailService {
  readonly baseUrl: any;
  constructor(
    private rest: RestClient,
    private configService: ConfigService,
  ) {
    this.baseUrl = this.configService.get('KONQUEST_URL') + '/learning-trails';
  }

  findAll(params: RestParams) {
    return this.rest.get(this.baseUrl, params);
  }

  findOne(params: RestParams, id: string) {
    return this.rest.get(`${this.baseUrl}/${id}`, params);
  }
}
