import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'nest-keycloak-connect';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';
import { ApiFilters } from '../../common/decorators/filter-api.decorator';
import { TrailService } from './trail.service';

@UseGuards(AuthGuard)
@Controller('trails')
export class TrailController {
  constructor(private readonly trailsService: TrailService) {}

  @Get()
  @ApiFilters(['search', 'name'])
  findAll(@ExtractParams() params: RestParams) {
    return this.trailsService.findAll(params);
  }

  @Get(':id')
  findOne(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.trailsService.findOne(params, id);
  }
}
