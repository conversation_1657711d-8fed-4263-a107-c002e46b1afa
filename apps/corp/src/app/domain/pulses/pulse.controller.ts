import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { AuthGuard } from 'nest-keycloak-connect';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';
import { ApiFilters } from '../../common/decorators/filter-api.decorator';
import { PulseService } from './pulse.service';

@UseGuards(AuthGuard)
@Controller('pulses')
export class PulseController {
  constructor(private readonly pulsesService: PulseService) {}

  @Get()
  @ApiFilters(['search', 'name'])
  findAll(@ExtractParams() params: RestParams) {
    return this.pulsesService.findAll(params);
  }

  @Get(':id')
  findOne(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.pulsesService.findOne(params, id);
  }
}
