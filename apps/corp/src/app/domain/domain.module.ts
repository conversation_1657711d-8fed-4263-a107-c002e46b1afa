import { Module } from '@nestjs/common';
import { AuthTokenModule } from './authToken/authToken.module';
import { UsersModule } from './users/users.module';
import { WorkspacesModule } from './workspaces/workspaces.module';
import { MissionsModule } from './missions/mission.module';
import { PulseModule } from './pulses/pulse.module';
import { MissionEnrollmentModule } from './mission-enrollments/mission-enrollments.module';
import { TrailEnrollmentModule } from './trail-enrollments/trail-enrollments.module';
import { TrailModule } from './trail/trail.module';
import { GroupsModule } from './groups/groups.module';

@Module({
  imports: [
    AuthTokenModule,
    UsersModule,
    WorkspacesModule,
    MissionsModule,
    PulseModule,
    TrailModule,
    MissionEnrollmentModule,
    TrailEnrollmentModule,
    GroupsModule,
  ],
})
export class DomainModule {}
