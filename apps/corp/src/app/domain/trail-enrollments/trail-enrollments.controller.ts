import { Body, Controller, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiOkResponse } from '@nestjs/swagger';
import { AuthGuard } from 'nest-keycloak-connect';
import { ExtractParams, RestParams } from '@keeps-node-apis/@core';
import { ApiFilters } from '../../common/decorators/filter-api.decorator';
import { TrailEnrollmentService } from './trail-enrollments.service';

@UseGuards(AuthGuard)
@Controller('learning-trail-enrollments')
export class TrailEnrollmentController {
  constructor(private readonly trailEnrollmentService: TrailEnrollmentService) {}

  @Get()
  @ApiFilters(['search', 'name'])
  findAll(@ExtractParams() params: RestParams) {
    return this.trailEnrollmentService.findAll(params);
  }

  @Get(':id')
  findOne(@ExtractParams() params: RestParams, @Param('id') id: string) {
    return this.trailEnrollmentService.findOne(params, id);
  }

  @Post()
  @ApiOkResponse({ description: 'Enrollments created' })
  enrollBatch(@ExtractParams() params: RestParams, @Body() data: any) {
    return this.trailEnrollmentService.enrollBatch(params, data);
  }
}
