import { Module } from '@nestjs/common';
import { AuthModule } from '../../auth/auth.module';
import { RestModule } from '@keeps-node-apis/@core';
import { TrailEnrollmentController } from './trail-enrollments.controller';
import { TrailEnrollmentService } from './trail-enrollments.service';

@Module({
  imports: [AuthModule, RestModule],
  controllers: [TrailEnrollmentController],
  providers: [TrailEnrollmentService],
})
export class TrailEnrollmentModule {}
