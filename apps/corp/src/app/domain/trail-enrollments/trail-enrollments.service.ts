import { Injectable } from '@nestjs/common';
import { BaseService } from '../abstract.service';
import { RestParams } from '@keeps-node-apis/@core';

@Injectable()
export class TrailEnrollmentService extends BaseService {
  protected getBaseUrl(): string {
    return this.configService.get('KONQUEST_URL') + '/learning-trail-enrollments';
  }

  enrollBatch(params: RestParams, data: any) {
    const batchUrl = this.getBaseUrl() + '/batch/v2';
    return this.rest.post(batchUrl, data, params);
  }

  enroll(params: RestParams, data: any) {
    return this.rest.post(this.getBaseUrl(), data, params);
  }
}
