import { applyDecorators } from '@nestjs/common';
import { ApiQuery } from '@nestjs/swagger';

/**
 * Document query parameters used as filters in the endpoint.
 *
 * It's a simple and easy implementation, but for more detailed documentation
 * is better to use ApiQuery() for each filter.
 */
export const ApiFilters = (filters: string[], type = 'string', required = false) => {
  const decorators: MethodDecorator[] = filters.map((filter) =>
    ApiQuery({
      name: filter,
      type,
      required,
    }),
  );

  return applyDecorators(...decorators);
};
