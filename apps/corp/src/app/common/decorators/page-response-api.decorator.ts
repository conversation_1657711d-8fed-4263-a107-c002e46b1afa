import { applyDecorators, Type } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, ApiQuery, getSchemaPath } from '@nestjs/swagger';
import { PageResponse } from '../dto/page-response.dto';

/**
 * Document for a generic PageResponse endpoint.
 *
 * Ref: https://aalonso.dev/blog/how-to-generate-generics-dtos-with-nestjsswagger-422g
 */
export const ApiPageResponse = <T extends Type<unknown>>(type: T) =>
  applyDecorators(
    ApiQuery({
      name: 'page',
      type: 'number',
      required: false,
      description: 'Número da página. Começa em 1',
    }),
    ApiQuery({
      name: 'per_page',
      type: 'number',
      required: false,
      description: 'Tamanho da página.',
    }),
    ApiExtraModels(PageResponse, type),
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(PageResponse) },
          {
            properties: {
              results: {
                type: 'array',
                items: { $ref: getSchemaPath(type) },
              },
            },
          },
        ],
      },
    }),
  );
