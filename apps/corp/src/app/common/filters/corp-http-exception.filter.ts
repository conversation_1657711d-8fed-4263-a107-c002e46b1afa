import { ArgumentsHost, Catch, ExceptionFilter, HttpStatus, Logger } from '@nestjs/common';
import { Request, Response } from 'express';
import { HttpClientError } from '@keeps-node-apis/@core';

@Catch(HttpClientError)
export class CorpHttpExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(CorpHttpExceptionFilter.name);

  catch(exception: HttpClientError, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    let status = HttpStatus.INTERNAL_SERVER_ERROR;
    let errorResponse: any = {
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    if (exception instanceof HttpClientError) {
      status = exception.status;
      errorResponse = {
        ...errorResponse,
        statusCode: status,
        message: `HTTP Error: ${status ?? 'Unknown'} status for request to ${request.url}`,
      };
    } else {
      errorResponse = {
        ...errorResponse,
        statusCode: status,
        message: 'Internal server error',
      };
    }

    if (status >= 500) {
      this.logger.error(`${request.method} ${request.url} - ${status}`, exception.stack);
    } else {
      this.logger.warn(`${request.method} ${request.url} - ${status}:`, exception.stack);
    }

    response.status(status).json(errorResponse);
  }
}
