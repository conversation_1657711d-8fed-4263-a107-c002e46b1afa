import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PassportModule } from '@nestjs/passport';
import { KeycloakConnectModule } from 'nest-keycloak-connect';

@Module({
  imports: [
    PassportModule,
    ConfigModule,
    KeycloakConnectModule.registerAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        authServerUrl: configService.get('AUTH_URL'),
        realm: configService.get('AUTH_REALM'),
        resource: configService.get('AUTH_CLIENT_ID'),
        secret: configService.get('AUTH_CLIENT_SECRET'),
        realmPublicKey: configService.get('AUTH_REALM_PUBLIC_KEY'),
        bearerOnly: true,
      }),
    }),
  ],
  exports: [KeycloakConnectModule],
})
export class AuthModule {}
