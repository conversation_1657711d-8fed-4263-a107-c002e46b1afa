import { Inject, Injectable, Logger } from '@nestjs/common';
import { HttpClient, HttpClientConfig, JwtService, TenantService } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class CorpHttpClient {
  private readonly KONQUEST_URL: string;
  private readonly logger = new Logger(CorpHttpClient.name);

  constructor(
    @Inject('HttpClient') private readonly httpClient: HttpClient,
    private readonly tenantService: TenantService,
    private readonly jwtService: JwtService,
    readonly configService: ConfigService,
  ) {
    this.KONQUEST_URL = configService.get('KONQUEST_URL');
  }

  post<T>(url: string, body: any) {
    const httpConfig: HttpClientConfig = { headers: this.createHeaders(url) };
    this.logRequest(url, body, httpConfig);
    return this.httpClient.post<T>(url, body, httpConfig);
  }

  get<T>(url: string, params?: Record<string, any>) {
    const headers = this.createHeaders(url);
    const httpConfig: HttpClientConfig = { headers, params };
    this.logRequest(url, null, httpConfig);
    return this.httpClient.get<T>(url, httpConfig);
  }

  patch<T>(url: string, body?: any) {
    const httpConfig: HttpClientConfig = { headers: this.createHeaders(url) };
    this.logRequest(url, body, httpConfig);
    return this.httpClient.patch<T>(url, body, httpConfig);
  }

  put<T>(url: string, body?: any) {
    const httpConfig: HttpClientConfig = { headers: this.createHeaders(url) };
    this.logRequest(url, body, httpConfig);
    return this.httpClient.put<T>(url, body, httpConfig);
  }

  delete<T>(url: string) {
    const httpConfig: HttpClientConfig = { headers: this.createHeaders(url) };
    this.logRequest(url, null, httpConfig);
    return this.httpClient.delete<T>(url, httpConfig);
  }

  private createHeaders(url: string): Record<string, string> {
    const headers: Record<string, string> = {
      'x-client': this.tenantService.getTenantId(),
    };

    const jwt = this.jwtService.getJwt();
    if (jwt) {
      const shouldAddBearerPrefix = !url.startsWith(this.KONQUEST_URL);
      headers['Authorization'] = shouldAddBearerPrefix ? `Bearer ${jwt}` : jwt;
    }

    return headers;
  }

  private logRequest(url: string, body: any, config: HttpClientConfig) {
    this.logger.log(`HTTP Client request - ${url}`);
    if (config) {
      this.logger.log(JSON.stringify(config));
    }
    if (body) {
      this.logger.log(JSON.stringify(body));
    }
  }
}
