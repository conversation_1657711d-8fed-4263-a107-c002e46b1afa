import Joi from 'joi';

export const CONFIG_SCHEMA = Joi.object({
  NODE_ENV: Joi.string().valid('development', 'production').default('development'),
  KONQUEST_URL: Joi.string().uri().required(),
  MYACCOUNT_URL: Joi.string().uri().required(),
  MYACCOUNT_V2_URL: Joi.string().uri().required(),
  AUTH_URL: Joi.string().uri().required(),
  AUTH_REALM: Joi.string().required().default('keeps-dev'),
  AUTH_CLIENT_ID: Joi.string().required().default('keeps-corp-api-stage'),
  AUTH_CLIENT_SECRET: Joi.string().required(),
  AUTH_REALM_PUBLIC_KEY: Joi.string().required(),
});
