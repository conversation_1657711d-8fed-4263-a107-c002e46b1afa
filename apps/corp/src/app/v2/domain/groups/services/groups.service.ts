import { Injectable } from '@nestjs/common';
import { GroupsListParamsDto } from '../dtos/groups-list-params.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';
import { GroupDto } from '../dtos/group.dto';
import { GroupsListResponseDto } from '../dtos/groups-list-response.dto';
import { GroupEnrollDto } from '../dtos/group-enroll.dto';
import { KonquestGroupEnrollResponseDto } from '../dtos/konquest-group-enroll-response.dto';
import { GroupEnrollErrorDto, GroupEnrollResponseDto } from '../dtos/group-enroll-response.dto';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';

@Injectable()
export class GroupsService extends KonquestBaseService<GroupDto> {
  private readonly basePath = `${this.KONQUEST_URL}/groups`;

  async list(params: GroupsListParamsDto): Promise<GroupsListResponseDto> {
    const queryParams = this.buildKonquestListParams(params);
    const result = await this.http.get<KonquestResponseDto<GroupDto>>(this.basePath, queryParams);
    return this.buildListResult(result, params);
  }

  async batchEnroll(groupEnrollDto: GroupEnrollDto): Promise<GroupEnrollResponseDto> {
    const result = await this.http.post<KonquestGroupEnrollResponseDto>(
      `${this.basePath}/${groupEnrollDto.id}/users`,
      groupEnrollDto,
    );
    return this.buildEnrollResponse(result);
  }

  private buildEnrollResponse(result: KonquestGroupEnrollResponseDto): GroupEnrollResponseDto {
    const errors: GroupEnrollErrorDto[] = result.group_user_errors?.map((error) => ({
      error: error.error.detail,
      user_id: error.user.id,
    }));
    return { errors };
  }
}
