import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class GroupEnrollErrorDto {
  @ApiPropertyOptional({ description: 'User id' })
  @Expose()
  user_id: string;

  @ApiPropertyOptional({ description: 'Error message' })
  @Expose()
  error: string;
}

export class GroupEnrollResponseDto {
  @ApiPropertyOptional({ description: 'List of errors', type: GroupEnrollErrorDto, isArray: true })
  @Expose()
  errors: GroupEnrollErrorDto[];
}
