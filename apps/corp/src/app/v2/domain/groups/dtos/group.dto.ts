import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class GroupDto {
  @ApiProperty({ description: 'The group id', example: '123e4567-e89b-12d3-a456-426614174000' })
  @Expose()
  id: string;

  @ApiProperty({ description: 'The group name', example: 'Management' })
  @Expose()
  name: string;

  @ApiProperty({ description: 'The total of users in the group', example: 10 })
  @Expose()
  users: number;

  @ApiProperty({ description: 'The total of missions in the group', example: 10 })
  @Expose()
  missions: number;

  @ApiProperty({ description: 'The total of learning trails in the group', example: 10 })
  @Expose()
  learing_trails: number;

  @ApiProperty({ description: 'The creation date of the group', example: '2021-01-01T00:00:00.000Z' })
  @Expose()
  created_date: string;

  @ApiProperty({ description: 'The date of the last group update', example: '2021-01-01T00:00:00.000Z' })
  @Expose()
  updated_date: string;

  @ApiProperty({ description: 'The date of the group deletion', example: '2021-01-01T00:00:00.000Z' })
  @Expose()
  deleted_date: string;

  @ApiProperty({ description: 'The group deletion status', example: true })
  @Expose()
  deleted: boolean;

  @ApiProperty({ description: 'The group description', example: 'The management group' })
  @Expose()
  description: string;
}
