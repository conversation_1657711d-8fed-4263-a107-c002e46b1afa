import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsBoolean, IsDate, IsOptional, IsString, IsUUID } from 'class-validator';

export class GroupEnrollDto {
  @ApiProperty({ description: 'The group id' })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'List of user ids to associate with the group and enroll in the group courses and learning trails',
  })
  @IsArray()
  @IsUUID('4', { each: true })
  users: string[];

  @ApiPropertyOptional({ description: 'Enrollment goal date' })
  @Type(() => Date)
  @IsOptional()
  @IsDate()
  enrollment_goal_date?: Date;

  @ApiPropertyOptional({
    description: 'Whether the enrollment conclusion is required for the courses and trails of the group',
  })
  @IsOptional()
  @IsBoolean()
  enrollment_required_mission?: boolean;

  @ApiPropertyOptional({ description: 'Regulatory compliance cycle id' })
  @IsOptional()
  @IsString()
  regulatory_compliance_cycle_id?: string;
}
