import { Expose } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';
import { GroupDto } from './group.dto';

export class GroupsListResponseDto {
  @Expose()
  @ApiPropertyOptional({ type: [GroupDto], description: 'List of groups' })
  items: GroupDto[];

  @Expose()
  @ApiPropertyOptional({ type: PaginationMetaDto, description: 'Pagination meta data' })
  meta: PaginationMetaDto;
}
