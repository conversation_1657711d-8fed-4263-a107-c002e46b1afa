import { Body, Controller, Get, HttpStatus, Post, Query } from '@nestjs/common';
import { KONQUEST_ADMIN_ROLES, MYACCOUNT_ADMIN_ROLES, Roles, Serialize } from '@keeps-node-apis/@core';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GroupsService } from '../services/groups.service';
import { GroupsListResponseDto } from '../dtos/groups-list-response.dto';
import { GroupsListParamsDto } from '../dtos/groups-list-params.dto';
import { GroupEnrollDto } from '../dtos/group-enroll.dto';
import { GroupEnrollResponseDto } from '../dtos/group-enroll-response.dto';

@ApiTags('Groups')
@Roles([...MYACCOUNT_ADMIN_ROLES, ...KONQUEST_ADMIN_ROLES])
@Controller({ path: 'groups', version: '2' })
export class GroupsController {
  constructor(private readonly groupsService: GroupsService) {}

  @Get()
  @ApiOperation({ summary: 'List groups' })
  @ApiResponse({ type: GroupsListResponseDto, status: HttpStatus.OK, description: 'List of groups' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(GroupsListResponseDto)
  list(@Query() params: GroupsListParamsDto) {
    return this.groupsService.list(params);
  }

  @Post('batch-enroll')
  @ApiOperation({
    summary: 'Add users to a group in batch',
    description: 'Add users the users to the group and enrolls them in the group courses and learning trails',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The result of the batch enroll operation',
    type: GroupEnrollResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiBody({ type: GroupEnrollDto })
  @Serialize(GroupEnrollResponseDto)
  enrollUsers(@Body() groupEnrollDto: GroupEnrollDto) {
    return this.groupsService.batchEnroll(groupEnrollDto);
  }
}
