import { Injectable } from '@nestjs/common';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';
import { MissionEnrollmentDto } from '../dtos/mission-enrollment.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';
import { EnrollmentsListParamsDto } from '../dtos/enrollments-list-params.dto';
import { MissionBatchEnrollmentCreateDto } from '../dtos/mission-batch-enrollment-create.dto';

@Injectable()
export class MissionEnrollmentsService extends KonquestBaseService<MissionEnrollmentDto> {
  private readonly basePath = `${this.KONQUEST_URL}/mission-enrollments`;

  async list(params: EnrollmentsListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    const enrollmentsList = await this.http.get<KonquestResponseDto<MissionEnrollmentDto>>(this.basePath, queryParams);
    return this.buildListResult(enrollmentsList, params);
  }

  getById(id: string) {
    return this.http.get<MissionEnrollmentDto>(`${this.basePath}/${id}`);
  }

  async batchEnroll(missionEnrollDto: MissionBatchEnrollmentCreateDto) {
    return this.http.post<void>(`${this.basePath}/batch/v2`, missionEnrollDto);
  }
}
