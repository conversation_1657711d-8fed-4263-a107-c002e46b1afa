import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';
import { MissionEnrollmentDto } from './mission-enrollment.dto';

export class MissionEnrollmentListResponseDto {
  @ApiPropertyOptional({ type: [MissionEnrollmentDto], description: 'List of mission enrollments' })
  @Expose()
  @Type(() => MissionEnrollmentDto)
  items: MissionEnrollmentDto[];

  @ApiPropertyOptional({ type: PaginationMetaDto, description: 'Pagination meta data' })
  @Expose()
  meta: PaginationMetaDto;
}
