import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';
import { TrailEnrollmentDto } from './trail-enrollment.dto';

export class TrailEnrollmentListResponseDto {
  @ApiPropertyOptional({ type: [TrailEnrollmentDto], description: 'List of learning trail enrollments' })
  @Expose()
  @Type(() => TrailEnrollmentDto)
  items: TrailEnrollmentDto[];

  @ApiPropertyOptional({ type: PaginationMetaDto, description: 'Pagination meta data' })
  @Expose()
  meta: PaginationMetaDto;
}
