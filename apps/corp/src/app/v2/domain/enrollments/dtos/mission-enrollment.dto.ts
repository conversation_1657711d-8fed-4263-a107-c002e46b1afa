import { ApiPropertyOptional, IntersectionType } from '@nestjs/swagger';
import { BaseEnrollmentDto } from './base-enrollment.dto';
import { Expose, Type } from 'class-transformer';
import { BasicMissionDto } from '../../missions/dtos/mission.dto';

export class MissionEnrollmentDto extends IntersectionType(BaseEnrollmentDto) {
  @ApiPropertyOptional({
    description: 'Indicates if the user has evaluated the mission upon enrollment completion',
    example: true,
  })
  @Expose()
  evaluated: boolean;

  @ApiPropertyOptional({
    description: 'Soft deletion date of the mission enrollment record (if applicable)',
    example: '2023-07-01T12:00:00Z',
  })
  @Expose()
  deleted_date: string;

  @ApiPropertyOptional({
    description: 'Comment provided by the user for giving up (if applicable)',
    example: 'I did not have enough time to complete it.',
  })
  @Expose()
  give_up_comment: string;

  @ApiPropertyOptional({
    description: 'Approval message for successfully completing the mission',
    example: 'Congratulations on finishing this mission!',
  })
  @Expose()
  approve_msg: string;

  @ApiPropertyOptional({
    description: 'URL of the certificate provider that issued the certificate',
    example: 'https://example.com/providers/certificate-provider',
  })
  @Expose()
  certificate_provider_url: string;

  @ApiPropertyOptional({
    description: 'Total number of questions in the mission',
    example: 20,
  })
  @Expose()
  total_mission_questions: number;

  @ApiPropertyOptional({
    description: 'Total number of questions correctly answered by the user',
    example: 18,
  })
  @Expose()
  total_correct_answers: number;

  @ApiPropertyOptional({
    description: 'Details of the mission associated with the enrollment',
    type: BasicMissionDto,
  })
  @Expose()
  @Type(() => BasicMissionDto)
  mission: BasicMissionDto;
}
