import { ApiProperty, IntersectionType } from '@nestjs/swagger';
import { BatchEnrollmentCreateDto } from './batch-enrollment-create.dto';
import { IsArray, IsUUID } from 'class-validator';

export class MissionBatchEnrollmentCreateDto extends IntersectionType(BatchEnrollmentCreateDto) {
  @ApiProperty({
    description: 'List of mission ids to enroll',
  })
  @IsArray()
  @IsUUID('4', { each: true })
  missions: string[];
}
