import { ApiPropertyOptional } from '@nestjs/swagger';
import { ENROLLMENT_STATUS } from '../../shared/enums/enrollment-status.enum';
import { Expose, Type } from 'class-transformer';
import { BasicUserDto } from '../../users/dtos/user.dto';

export class BaseEnrollmentDto {
  @ApiPropertyOptional({
    description: 'Unique identifier of the enrollment',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({
    description: 'Creation date of the enrollment record',
    example: '2023-05-01T12:00:00Z',
  })
  @Expose()
  created_date: string;

  @ApiPropertyOptional({
    description: 'Last updated date of the enrollment record',
    example: '2023-06-01T12:00:00Z',
  })
  @Expose()
  updated_date: string;

  @ApiPropertyOptional({
    description: 'Performance of the user in the enrollment (value between 0 and 1)',
    example: 0.85,
  })
  @Expose()
  performance: number;

  @ApiPropertyOptional({
    description: 'Date when the user started',
    example: '2023-05-02T12:00:00Z',
  })
  @Expose()
  start_date: string;

  @ApiPropertyOptional({
    description: 'Date when the user finished',
    example: '2023-06-15T14:00:00Z',
  })
  @Expose()
  end_date: string;

  @ApiPropertyOptional({
    description: 'Indicates if the user has given up',
    example: false,
  })
  @Expose()
  give_up: boolean;

  @ApiPropertyOptional({
    description: 'Indicates if the enrollment is required',
    example: true,
  })
  @Expose()
  required: boolean;

  @ApiPropertyOptional({
    description: 'Current status of the enrollment',
    enum: ENROLLMENT_STATUS,
    example: ENROLLMENT_STATUS.ENROLLED,
  })
  @Expose()
  status: ENROLLMENT_STATUS;

  @ApiPropertyOptional({
    description: 'Date by which the user should to complete the enrollment',
    example: '2023-06-15T23:59:59Z',
  })
  @Expose()
  goal_date: string;

  @ApiPropertyOptional({
    description: 'Details of the enrolled user',
    type: BasicUserDto,
  })
  @Expose()
  @Type(() => BasicUserDto)
  user: BasicUserDto;

  @ApiPropertyOptional({
    description: 'Indicates if the enrollment was created with a regulatory compliance rule',
    example: false,
  })
  @Expose()
  normative: boolean;

  @ApiPropertyOptional({
    description: 'Progress percentage in the enrollment (value between 0 and 1)',
    example: 0.55,
  })
  @Expose()
  progress: number;

  @ApiPropertyOptional({
    description: 'URL of the certificate generated upon completion',
    example: 'https://example.com/certificates/12345.pdf',
  })
  @Expose()
  certificate_url: string;

  @ApiPropertyOptional({
    description: 'Points scored in enrollment',
    example: 150,
  })
  @Expose()
  points: number;
}
