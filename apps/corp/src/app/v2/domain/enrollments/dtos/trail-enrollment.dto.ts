import { ApiPropertyOptional, IntersectionType } from '@nestjs/swagger';
import { BaseEnrollmentDto } from './base-enrollment.dto';
import { Expose, Type } from 'class-transformer';
import { BasicTrailDto } from '../../trails/dtos/trail.dto';

export class TrailEnrollmentDto extends IntersectionType(BaseEnrollmentDto) {
  @ApiPropertyOptional({
    description: 'Details of the trail associated with the enrollment',
    type: BasicTrailDto,
  })
  @Expose()
  @Type(() => BasicTrailDto)
  learning_trail: BasicTrailDto;
}
