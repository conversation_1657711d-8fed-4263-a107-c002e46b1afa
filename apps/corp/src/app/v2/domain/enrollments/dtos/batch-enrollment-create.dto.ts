import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON>y, IsBoolean, IsDate, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

export class BatchEnrollmentCreateDto {
  @ApiProperty({
    description: 'List of user ids to enroll',
  })
  @IsArray()
  @IsUUID('4', { each: true })
  users: string[];

  @ApiProperty({ description: 'Enrollment goal date' })
  @Type(() => Date)
  @IsDate()
  goal_date: Date;

  @ApiProperty({
    description: 'Whether the enrollment conclusion is required',
  })
  @IsBoolean()
  required: boolean;
}
