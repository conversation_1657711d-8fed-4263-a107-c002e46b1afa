import { ApiProperty, IntersectionType } from '@nestjs/swagger';
import { BatchEnrollmentCreateDto } from './batch-enrollment-create.dto';
import { IsArray, IsUUID } from 'class-validator';

export class TrailBatchEnrollmentCreateDto extends IntersectionType(BatchEnrollmentCreateDto) {
  @ApiProperty({
    description: 'List of learning trails ids to enroll',
  })
  @IsArray()
  @IsUUID('4', { each: true })
  learning_trails: string[];
}
