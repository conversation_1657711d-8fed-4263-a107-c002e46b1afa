import { Body, Controller, Get, HttpCode, HttpStatus, Param, Post, Query } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { TrailEnrollmentsService } from '../services/trail-enrollments.service';
import { TrailEnrollmentListResponseDto } from '../dtos/trail-enrollment-list-response.dto';
import { TrailEnrollmentDto } from '../dtos/trail-enrollment.dto';
import { Serialize } from '@keeps-node-apis/@core';
import { EnrollmentsListParamsDto } from '../dtos/enrollments-list-params.dto';
import { TrailBatchEnrollmentCreateDto } from '../dtos/trail-batch-enrollment-create.dto';

@ApiTags('Trail Enrollments')
@Controller({ path: 'trail-enrollments', version: '2' })
export class TrailEnrollmentsController {
  constructor(private readonly trailEnrollmentsService: TrailEnrollmentsService) {}

  @Get()
  @ApiOperation({ summary: 'List learning trail enrollments' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of learning trail enrollments',
    type: TrailEnrollmentListResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(TrailEnrollmentListResponseDto)
  list(@Query() params: EnrollmentsListParamsDto) {
    return this.trailEnrollmentsService.list(params);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get learning trail enrollment by id' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Trail Enrollment UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The selected learning trail enrollment',
    type: TrailEnrollmentDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Trail enrollment not found' })
  @Serialize(TrailEnrollmentDto)
  getById(@Param('id') id: string) {
    return this.trailEnrollmentsService.getById(id);
  }

  @Post()
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiOperation({
    summary: 'Enroll users in batch',
    description: 'Enroll users in batch in the provided learning trails',
  })
  @ApiResponse({ status: HttpStatus.ACCEPTED, description: 'The request will be processed' })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiBody({ type: TrailBatchEnrollmentCreateDto })
  async batchEnroll(@Body() trailEnrollDto: TrailBatchEnrollmentCreateDto) {
    await this.trailEnrollmentsService.batchEnroll(trailEnrollDto);
  }
}
