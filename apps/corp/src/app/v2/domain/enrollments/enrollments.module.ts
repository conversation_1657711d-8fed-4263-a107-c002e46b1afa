import { Module } from '@nestjs/common';
import { TrailEnrollmentsService } from './services/trail-enrollments.service';
import { MissionEnrollmentsService } from './services/mission-enrollments.service';
import { CorpHttpModule } from '../../infra';
import { MissionEnrollmentsController } from './controllers/mission-enrollments.controller';
import { TrailEnrollmentsController } from './controllers/trail-enrollments.controller';

@Module({
  imports: [CorpHttpModule],
  providers: [TrailEnrollmentsService, MissionEnrollmentsService],
  controllers: [MissionEnrollmentsController, TrailEnrollmentsController],
})
export class EnrollmentsModule {}
