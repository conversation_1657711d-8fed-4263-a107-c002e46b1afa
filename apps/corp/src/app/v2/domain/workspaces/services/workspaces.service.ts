import { Injectable } from '@nestjs/common';
import { CorpHttpClient } from '../../../infra';
import { WorkspaceDto } from '../dtos/workspace.dto';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class WorkspacesService {
  private readonly MY_ACCOUNT_V2_URL: string;

  constructor(
    private readonly http: CorpHttpClient,
    readonly configService: ConfigService,
  ) {
    this.MY_ACCOUNT_V2_URL = configService.get('MYACCOUNT_V2_URL');
  }

  list() {
    return this.http.get<WorkspaceDto[]>(`${this.MY_ACCOUNT_V2_URL}/workspaces`);
  }
}
