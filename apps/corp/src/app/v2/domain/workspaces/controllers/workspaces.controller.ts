import { Controller, Get } from '@nestjs/common';
import { KONQUEST_ADMIN_ROLES, MYACCOUNT_ADMIN_ROLES, Roles, Serialize, SkipTenant } from '@keeps-node-apis/@core';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { WorkspaceDto } from '../dtos/workspace.dto';
import { WorkspacesService } from '../services/workspaces.service';

@ApiTags('Workspaces')
@Roles([...MYACCOUNT_ADMIN_ROLES, ...KONQUEST_ADMIN_ROLES])
@Controller({ path: 'workspaces', version: '2' })
export class WorkspacesController {
  constructor(private readonly workspaceService: WorkspacesService) {}

  @Get()
  @SkipTenant()
  @ApiOperation({ summary: 'List accessible workspaces by the authenticated user' })
  @ApiResponse({ status: 200, description: 'List of workspaces', type: WorkspaceDto, isArray: true })
  @ApiResponse({ status: 400, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @Serialize(WorkspaceDto)
  list() {
    return this.workspaceService.list();
  }
}
