import { Expose } from 'class-transformer';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class WorkspaceDto {
  @Expose()
  @ApiPropertyOptional({ type: String, description: 'The unique identifier of the workspace' })
  id: string;

  @Expose()
  @ApiPropertyOptional({ type: String, description: 'The name of the workspace' })
  name: string;

  @Expose()
  @ApiPropertyOptional({ type: String, description: 'The URL of the workspace logo' })
  logo_url: string;

  @Expose()
  @ApiPropertyOptional({ type: String, description: 'The URL of the workspace icon' })
  icon_url: string;

  @Expose()
  @ApiPropertyOptional({ type: String, description: 'The custom color for the workspace' })
  custom_color: string;

  @Expose()
  @ApiPropertyOptional({ type: String, description: 'The ID of the workspace theme' })
  theme_id: string;
}
