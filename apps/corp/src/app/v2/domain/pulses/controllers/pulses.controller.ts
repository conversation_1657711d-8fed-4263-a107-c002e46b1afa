import { Controller, Get, HttpStatus, Param, Query } from '@nestjs/common';
import { PulsesService } from '../services/pulses.service';
import { PulsesListParamDto } from '../dtos/pulses-list-param.dto';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Serialize } from '@keeps-node-apis/@core';
import { PulsesListResponseDto } from '../dtos/pulses-list-response.dto';
import { PulseDto } from '../dtos/pulse.dto';

@ApiTags('Pulses')
@Controller({ path: 'pulses', version: '2' })
export class PulsesController {
  constructor(private readonly pulsesService: PulsesService) {}

  @Get()
  @ApiOperation({ summary: 'List pulses' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of pulses',
    type: PulsesListResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(PulsesListResponseDto)
  list(@Query() params: PulsesListParamDto) {
    return this.pulsesService.list(params);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get pulse by id' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Pulse uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The selected pulse',
    type: PulseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Pulse not found' })
  @Serialize(PulseDto)
  getById(@Param('id') id: string) {
    return this.pulsesService.getById(id);
  }
}
