import { Injectable } from '@nestjs/common';
import { PulsesListParamDto } from '../dtos/pulses-list-param.dto';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';
import { PulseDto } from '../dtos/pulse.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';

@Injectable()
export class PulsesService extends KonquestBaseService<PulseDto> {
  private readonly basePath = `${this.KONQUEST_URL}/pulses`;

  async list(params: PulsesListParamDto) {
    const queryParams = this.buildKonquestListParams(params);
    const pulsesList = await this.http.get<KonquestResponseDto<PulseDto>>(this.basePath, queryParams);
    return this.buildListResult(pulsesList, params);
  }

  getById(id: string) {
    return this.http.get<PulseDto>(`${this.basePath}/${id}`);
  }
}
