import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class PulseDto {
  @ApiPropertyOptional({
    description: 'The unique identifier of the pulse',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({
    description: 'The name of the pulse',
    example: 'Weekly Project Standup',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'The unique identifier of the learn content related to the pulse',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @Expose()
  learn_content_uuid: string;

  @ApiPropertyOptional({
    description: 'The description of the pulse',
    example: 'A weekly session to discuss project updates and blockers.',
  })
  @Expose()
  description: string;

  @ApiPropertyOptional({
    description: 'The average pulse rating',
    example: 4.5,
  })
  @Expose()
  rating_avg: number;

  @ApiPropertyOptional({
    description: 'The number of pulse ratings',
    example: 120,
  })
  @Expose()
  rating_count: number;

  @ApiPropertyOptional({
    description: 'Creation date of the pulse',
    example: '2023-10-01T10:15:30Z',
  })
  @Expose()
  created_date: string;

  @ApiPropertyOptional({
    description: 'Last update date of the pulse',
    example: '2023-10-10T14:45:00Z',
  })
  @Expose()
  updated_date: string;

  @ApiPropertyOptional({
    description: 'The pulse duration in seconds',
    example: 3600,
  })
  @Expose()
  duration: number;

  @ApiPropertyOptional({
    description: 'The pulse holder image URL',
    example: 'https://example.com/images/pulse-holder.png',
  })
  @Expose()
  holder_image: string;

  @ApiPropertyOptional({
    description: 'The number of pulse views',
    example: 1500,
  })
  @Expose()
  views: number;

  @ApiPropertyOptional({
    description: 'The pulse language',
    example: 'pt-BR',
  })
  @Expose()
  language: string;
}
