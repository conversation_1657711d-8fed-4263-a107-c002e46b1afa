import { PulseDto } from './pulse.dto';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class PulsesListResponseDto {
  @ApiPropertyOptional({ type: [PulseDto], description: 'List of pulse items' })
  @Expose()
  @Type(() => PulseDto)
  items: PulseDto[];

  @ApiPropertyOptional({ description: 'Pagination metadata information' })
  @Expose()
  meta: PaginationMetaDto;
}
