import { Injectable } from '@nestjs/common';
import { AuthRequestDto, AuthResponseDto } from '../dtos/authentication.dto';
import { CorpHttpClient } from '../../../infra';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class AuthService {
  private readonly MY_ACCOUNT_V2_URL: string;

  constructor(
    private readonly http: CorpHttpClient,
    readonly configService: ConfigService,
  ) {
    this.MY_ACCOUNT_V2_URL = configService.get('MYACCOUNT_V2_URL');
  }

  authenticate(authRequestDto: AuthRequestDto) {
    return this.http.post<AuthResponseDto>(`${this.MY_ACCOUNT_V2_URL}/auth/login`, authRequestDto);
  }
}
