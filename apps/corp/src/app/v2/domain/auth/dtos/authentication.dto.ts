import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO for handling login request data.
 * Contains the necessary credentials for user authentication.
 */
export class AuthRequestDto {
  @ApiProperty({
    description: 'The user name or email address',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  username!: string;

  @ApiProperty({
    description: 'The user password',
    example: 'SecurePassword123!',
  })
  @IsString()
  @IsNotEmpty()
  @Expose()
  password: string;
}

/**
 * DTO for handling login response data.
 * Contains the access token issued after successful authentication.
 */
export class AuthResponseDto {
  @ApiPropertyOptional({
    description: 'The access token',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @Expose()
  access_token: string;
}
