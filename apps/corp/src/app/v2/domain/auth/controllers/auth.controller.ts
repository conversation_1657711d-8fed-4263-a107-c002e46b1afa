import { Body, Controller, Post } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Public } from 'nest-keycloak-connect';
import { AuthRequestDto, AuthResponseDto } from '../dtos/authentication.dto';
import { AuthService } from '../services/auth.service';

@ApiTags('Authentication')
@Controller({ path: 'auth', version: '2' })
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post()
  @Public()
  @ApiOperation({ summary: 'Generate access token', description: 'Authenticate user and generate access token' })
  @ApiBody({ type: AuthRequestDto })
  @ApiResponse({ status: 200, description: 'User authenticated successfully', type: AuthResponseDto })
  @ApiResponse({ status: 400, description: 'Invalid credentials or missing fields' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  authenticate(@Body() authTokenDto: AuthRequestDto) {
    return this.authService.authenticate(authTokenDto);
  }
}
