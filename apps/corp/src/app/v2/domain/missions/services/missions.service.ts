import { Injectable } from '@nestjs/common';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';
import { MissionDto } from '../dtos/mission.dto';
import { MissionListParamsDto } from '../dtos/mission-list-params.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';

@Injectable()
export class MissionsService extends KonquestBaseService<MissionDto> {
  private readonly basePath = `${this.KONQUEST_URL}/missions`;

  async list(params: MissionListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    const pulsesList = await this.http.get<KonquestResponseDto<MissionDto>>(this.basePath, queryParams);
    return this.buildListResult(pulsesList, params);
  }

  getById(id: string) {
    return this.http.get<MissionDto>(`${this.basePath}/${id}`);
  }
}
