import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { CategoryDto } from '../../shared/dtos/category.dto';
import { ContentTypeDto } from '../../shared/dtos/content-type.dto';
import { Expose, Type } from 'class-transformer';
import { UserCreatorDto } from '../../shared/dtos/user-creator.dto';

enum MISSION_MODEL {
  INTERNAL = 'INTERNAL',
  EXTERNAL_PROVIDER = 'EXTERNAL_PROVIDER',
  LIVE = 'LIVE',
  PRESENTIAL = 'PRESENTIAL',
  SCORM = 'SCORM',
}

enum DEVELOPMENT_STATUS {
  CLOSED = 'CLOSED',
  PROCESSING = 'PROCESSING',
  DONE = 'DONE',
  IN_REVIEW = 'IN_REVIEW',
  EXPIRED = 'EXPIRED',
  INACTIVATED = 'INACTIVATED',
  IN_PROGRESS = 'IN_PROGRESS',
  INACTIVATED_BY_INTEGRATION = 'INACTIVATED_BY_INTEGRATION',
}

export class MissionDto {
  @ApiPropertyOptional({
    description: 'Unique identifier of the mission',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({
    description: 'Name of the mission',
    example: 'Introduction to Programming',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Detailed description of the mission',
    example: 'This mission covers the basics of programming concepts and practices.',
  })
  @Expose()
  description: string;

  @ApiPropertyOptional({
    description: 'Short summary of the mission',
    example: 'Covers programming basics.',
  })
  @Expose()
  summary: string;

  @ApiPropertyOptional({
    description: 'Category information for the mission',
    type: CategoryDto,
  })
  @Expose()
  @Type(() => CategoryDto)
  mission_category: CategoryDto;

  @ApiPropertyOptional({
    description: 'Number of users enrolled in the mission',
    example: 150,
  })
  @Expose()
  users_enrolled: number;

  @ApiPropertyOptional({
    description: 'Number of users who finished the mission',
    example: 150,
  })
  @Expose()
  users_finished: number;

  @ApiPropertyOptional({
    description: 'The average user rating for the mission',
    example: 4.5,
  })
  @Expose()
  rating_avg: number;

  @ApiPropertyOptional({
    description: 'Details of the user who created the mission',
    type: UserCreatorDto,
  })
  @Expose()
  @Type(() => UserCreatorDto)
  user_creator: UserCreatorDto;

  @ApiPropertyOptional({
    description: 'Language in which the mission is available',
    example: 'pt-BR',
  })
  @Expose()
  language: string;

  @ApiPropertyOptional({
    description: 'The current development status of the mission',
    example: DEVELOPMENT_STATUS.IN_PROGRESS,
    enum: DEVELOPMENT_STATUS,
  })
  @Expose()
  development_status: DEVELOPMENT_STATUS;

  @ApiPropertyOptional({
    description: 'The expiration date of the mission (if applicable)',
    example: '2024-12-31T23:59:59Z',
  })
  @Expose()
  expiration_date: string;

  @ApiPropertyOptional({
    description: 'The minimum required performance score for the mission (between 0 and 1)',
    example: 0.75,
  })
  @Expose()
  minimum_performance: number;

  @ApiPropertyOptional({
    description: 'The model of the mission',
    example: MISSION_MODEL.INTERNAL,
    enum: MISSION_MODEL,
  })
  @Expose()
  mission_model: MISSION_MODEL;

  @ApiPropertyOptional({
    description: 'URL for the holder image of the mission',
    example: 'https://example.com/images/mission/holder.jpg',
  })
  @Expose()
  holder_image: string;

  @ApiPropertyOptional({
    description: 'URL for the vertical holder image of the mission',
    example: 'https://example.com/images/mission/vertical_holder.jpg',
  })
  @Expose()
  vertical_holder_image: string;

  @ApiPropertyOptional({
    description: 'URL for the thumb image of the mission',
    example: 'https://example.com/images/mission/thumb.jpg',
  })
  @Expose()
  thumb_image: string;

  @ApiPropertyOptional({
    description: 'The creation timestamp of the mission',
    example: '2024-01-15T10:30:00Z',
  })
  @Expose()
  created_date: string;

  @ApiPropertyOptional({
    description: 'The last updated timestamp of the mission',
    example: '2024-02-01T15:45:00Z',
  })
  @Expose()
  updated_date: string;

  @ApiPropertyOptional({
    description: 'Type of visibility of the mission in the workspace',
    type: ContentTypeDto,
  })
  @Expose()
  @Type(() => ContentTypeDto)
  mission_type: ContentTypeDto;
}

@Expose()
export class BasicMissionDto extends PickType(MissionDto, ['id', 'name'] as const) {}
