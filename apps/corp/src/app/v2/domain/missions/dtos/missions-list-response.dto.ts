import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { MissionDto } from './mission.dto';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';

export class MissionsListResponseDto {
  @ApiPropertyOptional({ type: [MissionDto], description: 'List of missions' })
  @Expose()
  @Type(() => MissionDto)
  items: MissionDto[];

  @ApiPropertyOptional({ type: PaginationMetaDto, description: 'Pagination meta data' })
  @Expose()
  meta: PaginationMetaDto;
}
