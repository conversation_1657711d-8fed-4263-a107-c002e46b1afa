import { Controller, Get, HttpStatus, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MissionsService } from '../services/missions.service';
import { MissionsListResponseDto } from '../dtos/missions-list-response.dto';
import { Serialize } from '@keeps-node-apis/@core';
import { MissionListParamsDto } from '../dtos/mission-list-params.dto';
import { MissionDto } from '../dtos/mission.dto';

@ApiTags('Missions')
@Controller({ path: 'missions', version: '2' })
export class MissionsController {
  constructor(private readonly missionsService: MissionsService) {}

  @Get()
  @ApiOperation({ summary: 'List missions' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of missions',
    type: MissionsListResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(MissionsListResponseDto)
  list(@Query() params: MissionListParamsDto) {
    return this.missionsService.list(params);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get mission by id' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Mission UUID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The selected mission',
    type: MissionDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Mission not found' })
  @Serialize(MissionDto)
  getById(@Param('id') id: string) {
    return this.missionsService.getById(id);
  }
}
