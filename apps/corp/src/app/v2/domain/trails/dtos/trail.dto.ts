import { UserCreatorDto } from '../../shared/dtos/user-creator.dto';
import { ContentTypeDto } from '../../shared/dtos/content-type.dto';
import { ApiPropertyOptional, PickType } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';

export class TrailDto {
  @ApiPropertyOptional({
    description: 'Unique identifier of the trail',
    example: '123e4567-e89b-12d3-a456-426614174001',
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({
    description: 'Name of the trail',
    example: 'Basic Programming Trail',
  })
  @Expose()
  name: string;

  @ApiPropertyOptional({
    description: 'Detailed description of the trail',
    example: 'This trail includes beginner-level programming content and exercises.',
  })
  @Expose()
  description: string;

  @ApiPropertyOptional({
    description: 'Details of the user who created the trail',
    type: UserCreatorDto,
  })
  @Expose()
  @Type(() => UserCreatorDto)
  user_creator: UserCreatorDto;

  @ApiPropertyOptional({
    description: 'Type of visibility of the trail in the workspace',
    type: ContentTypeDto,
  })
  @Expose()
  @Type(() => ContentTypeDto)
  learning_trail_type: ContentTypeDto;

  @ApiPropertyOptional({
    description: 'Total number of missions in the trail',
    example: 5,
  })
  @Expose()
  count_missions: number;

  @ApiPropertyOptional({
    description: 'Total number of pulses in the trail',
    example: 10,
  })
  @Expose()
  count_pulses: number;

  @ApiPropertyOptional({
    description: 'Number of users enrolled in the trail',
    example: 150,
  })
  @Expose()
  users_enrolled: number;

  @ApiPropertyOptional({
    description: 'Number of users who finished the trail',
    example: 80,
  })
  @Expose()
  users_finished: number;

  @ApiPropertyOptional({
    description: 'Creation date of the trail',
    example: '2023-05-10T14:48:00Z',
  })
  @Expose()
  created_date: string;

  @ApiPropertyOptional({
    description: 'Last updated date of the trail',
    example: '2023-06-15T12:00:00Z',
  })
  @Expose()
  updated_date: string;

  @ApiPropertyOptional({
    description: 'Estimated total duration of the trail in minutes',
    example: 120,
  })
  @Expose()
  duration_time: number;

  @ApiPropertyOptional({
    description: 'Total points available in the trail',
    example: 500,
  })
  @Expose()
  points: number;

  @ApiPropertyOptional({
    description: 'Language in which the trail is available',
    example: 'en-US',
  })
  @Expose()
  language: string;

  @ApiPropertyOptional({
    description: 'Indicates if the trail is currently active',
    example: true,
  })
  @Expose()
  is_active: boolean;

  @ApiPropertyOptional({
    description: 'Expiration date of the trail (if applicable)',
    example: '2024-12-31T23:59:59Z',
  })
  @Expose()
  expiration_date: string;
}

export class BasicTrailDto extends PickType(TrailDto, ['id', 'name'] as const) {}
