import { TrailDto } from './trail.dto';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';

export class TrailsListResponseDto {
  @ApiPropertyOptional({ type: [TrailDto], description: 'List of trails' })
  @Expose()
  @Type(() => TrailDto)
  items: TrailDto[];

  @ApiPropertyOptional({ type: PaginationMetaDto, description: 'Pagination meta data' })
  @Expose()
  meta: PaginationMetaDto;
}
