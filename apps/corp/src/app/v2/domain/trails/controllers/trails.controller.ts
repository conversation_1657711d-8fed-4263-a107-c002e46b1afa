import { Controller, Get, HttpStatus, Param, Query } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { TrailsService } from '../services/trails.service';
import { TrailsListResponseDto } from '../dtos/trails-list-response.dto';
import { Serialize } from '@keeps-node-apis/@core';
import { TrailListParamsDto } from '../dtos/trail-list-params.dto';
import { TrailDto } from '../dtos/trail.dto';

@ApiTags('Learning Trails')
@Controller({ path: 'trails', version: '2' })
export class TrailsController {
  constructor(private readonly trailsService: TrailsService) {}

  @Get()
  @ApiOperation({ summary: 'List learning trails' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of learning trails',
    type: TrailsListResponseDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @Serialize(TrailsListResponseDto)
  list(@Query() params: TrailListParamsDto) {
    return this.trailsService.list(params);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get trail by id' })
  @ApiParam({
    name: 'id',
    type: String,
    description: 'The Trail UUID',
    example: '456e7890-f12a-34d6-b789-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'The selected learning trail',
    type: TrailDto,
  })
  @ApiResponse({ status: HttpStatus.BAD_REQUEST, description: 'Invalid query parameters or missing fields' })
  @ApiResponse({ status: HttpStatus.UNAUTHORIZED, description: 'Unauthorized' })
  @ApiResponse({ status: HttpStatus.NOT_FOUND, description: 'Trail not found' })
  @Serialize(TrailDto)
  getById(@Param('id') id: string) {
    return this.trailsService.getById(id);
  }
}
