import { Injectable } from '@nestjs/common';
import { KonquestBaseService } from '../../shared/services/konquest-base-service';
import { TrailDto } from '../dtos/trail.dto';
import { TrailListParamsDto } from '../dtos/trail-list-params.dto';
import { KonquestResponseDto } from '../../shared/dtos/konquest-response.dto';

@Injectable()
export class TrailsService extends KonquestBaseService<TrailDto> {
  private readonly basePath = `${this.KONQUEST_URL}/learning-trails`;

  async list(params: TrailListParamsDto) {
    const queryParams = this.buildKonquestListParams(params);
    const trailsList = await this.http.get<KonquestResponseDto<TrailDto>>(this.basePath, queryParams);
    return this.buildListResult(trailsList, params);
  }

  getById(id: string) {
    return this.http.get<TrailDto>(`${this.basePath}/${id}`);
  }
}
