import { UserDto } from './user.dto';
import { Expose } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { PaginationMetaDto } from '../../shared/dtos/pagination-meta.dto';

export class UsersListResponseDto {
  @Expose()
  @ApiProperty({ type: [UserDto], description: 'List of users' })
  items: UserDto[];

  @Expose()
  @ApiProperty({ type: PaginationMetaDto, description: 'Pagination meta data' })
  meta: PaginationMetaDto;
}
