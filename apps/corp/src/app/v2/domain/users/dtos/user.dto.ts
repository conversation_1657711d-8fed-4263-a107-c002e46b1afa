import { ApiProperty, PickType } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class UserDto {
  @ApiProperty({
    description: 'Unique identifier of the user',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Full name of the user',
    example: '<PERSON>',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Primary email address',
    example: '<EMAIL>',
  })
  @Expose()
  email: string;

  @ApiProperty({
    description: 'Phone number or null',
    example: '+****************',
  })
  @Expose()
  phone: string;

  @ApiProperty({
    description: "URL of the user's avatar/profile picture or null",
    example: 'https://example.com/avatars/user123.jpg',
  })
  @Expose()
  avatar: string;

  @ApiProperty({
    description: 'User status (active/inactive)',
    example: true,
  })
  @Expose()
  status: boolean;

  @ApiProperty({
    description: 'User creation timestamp or null',
    example: '2024-01-15T10:30:00Z',
  })
  @Expose()
  created_date: string | null;
}

export class BasicUserDto extends PickType(UserDto, ['id', 'name', 'email'] as const) {}
