import { IsBoolean, IsDate<PERSON>tring, IsEmail, IsOptional, IsString, IsTimeZone, IsUUID } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class UserCreateDto {
  @IsEmail()
  @ApiProperty({ description: 'The email of the user', example: '<EMAIL>' })
  email: string;

  @IsString()
  @ApiProperty({ description: 'The name of the user', example: '<PERSON>' })
  name: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user EIN', example: '123456789', required: false })
  ein?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user job', example: 'Software Engineer', required: false })
  job?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user password', example: 'securePassword12345', required: false })
  password?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user nickname', example: '<PERSON>', required: false })
  nickname?: string;

  @IsEmail()
  @IsOptional()
  @ApiProperty({ description: 'The user secondary email', example: '<EMAIL>', required: false })
  secondaryEmail?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user phone', example: '+55 00 9 9999-9999', required: false })
  phone?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user gender', example: 'female', required: false })
  gender?: string;

  @IsDateString()
  @IsOptional()
  @ApiProperty({ description: 'The user birthday', example: '1990-01-01', required: false })
  birthday?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user address', example: 'Rua dos bobos, 123', required: false })
  address?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user avatar', example: 'https://example.com/avatar.png', required: false })
  avatar?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ description: 'The user activation status', example: true, required: false })
  status?: boolean;

  @IsUUID()
  @IsOptional()
  @ApiProperty({
    description: 'The user language id',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  languageId?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user country', example: 'Brazil', required: false })
  country?: string;

  @IsUUID()
  @IsOptional()
  @ApiProperty({ description: 'The user leader id', example: '123e4567-e89b-12d3-a456-************', required: false })
  relatedUserLeaderId?: string;

  @IsBoolean()
  @IsOptional()
  @ApiProperty({ description: 'Whether the user email is verified or not', example: true, required: false })
  emailVerified?: boolean;

  @IsString()
  @IsTimeZone()
  @ApiProperty({ description: 'The user timezone in the IANA format', example: 'America/Sao_Paulo', required: false })
  @IsOptional()
  timeZone: string;

  @IsDateString()
  @IsOptional()
  @ApiProperty({ description: 'The date of admission', example: '2021-01-01', required: false })
  admissionDate?: string;

  @IsString()
  @IsOptional()
  @ApiProperty({ description: 'The user CPF', example: '123.456.789-00', required: false })
  cpf?: string;
}
