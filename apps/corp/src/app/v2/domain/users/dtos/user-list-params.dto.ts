import { ApiProperty, IntersectionType } from '@nestjs/swagger';
import { IsEnum, IsOptional } from 'class-validator';
import { BaseListParamsDto } from '../../shared/dtos/base-list-params.dto';

export enum UserListSortOrder {
  ASC = 'asc',
  DESC = 'desc',
}

export enum UserListSortParam {
  NAME = 'name',
  EMAIL = 'email',
  CREATED_DATE = 'createdDate',
}

export class UserListParamsDto extends IntersectionType(BaseListParamsDto) {
  @ApiProperty({
    description: 'Parameter to sort users by',
    example: 'name',
    enum: UserListSortParam,
    required: false,
  })
  @IsEnum(UserListSortParam)
  @IsOptional()
  sort: UserListSortParam;

  @ApiProperty({
    description: 'Sort order',
    example: 'asc',
    required: false,
    enum: UserListSortOrder,
  })
  @IsEnum(UserListSortOrder)
  @IsOptional()
  order: UserListSortOrder;
}
