import { UserCreateDto } from './user-create.dto';
import { ApiProperty } from '@nestjs/swagger';
import { ArrayNotEmpty, IsArray, IsOptional, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class BatchUserCreateRoles {
  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the user role in My Account',
    required: false,
  })
  myAccountUser: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the admin role in My Account',
    required: false,
  })
  myAccountAdmin: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the user role in Konquest',
    required: false,
  })
  konquestUser: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the admin role in Konquest',
    required: false,
  })
  konquestAdmin: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the super admin role in Konquest',
    required: false,
  })
  konquestSuperAdmin: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the contributor role in Konquest',
    required: false,
  })
  konquestContributor: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the instructor role in Konquest',
    required: false,
  })
  konquestInstructor: boolean;

  @ApiProperty({
    type: Boolean,
    description: 'Whether the users should be assigned the curator role in Konquest',
    required: false,
  })
  konquestCurator: boolean;
}

export class BatchUserCreateDto {
  @ApiProperty({ type: UserCreateDto, isArray: true, description: 'Non empty list of users to be created' })
  @IsArray()
  @ArrayNotEmpty()
  @ValidateNested({ each: true })
  @Type(() => UserCreateDto)
  users: UserCreateDto[];

  @ApiProperty({
    type: BatchUserCreateRoles,
    required: false,
    description:
      'Roles configuration for the users to be created, only roles on the same level or bellow the current authenticated user can be assigned',
  })
  @IsOptional()
  roles?: BatchUserCreateRoles;
}
