import { Injectable } from '@nestjs/common';
import { CorpHttpClient } from '../../../infra';

import { KONQUEST_ROLES, MYACCOUNT_ROLES } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';
import { UserListParamsDto, UserListSortOrder } from '../dtos/user-list-params.dto';
import { UsersListResponseDto } from '../dtos/users-list-response.dto';
import { MyAccountUsersListResponseDto } from '../dtos/my-account-users-list.dto';
import { BatchUserCreateDto, BatchUserCreateRoles } from '../dtos/batch-user-create.dto';

const USERS_LIST_SELECTION = 'id,name,email,phone,avatar,status,createdDate';

@Injectable()
export class UsersService {
  private readonly basePath: string;

  constructor(
    private readonly http: CorpHttpClient,
    readonly configService: ConfigService,
  ) {
    this.basePath = `${this.configService.get('MYACCOUNT_V2_URL')}/users`;
  }

  async list(params: UserListParamsDto): Promise<UsersListResponseDto> {
    const queryParams = this.buildMyAccountListParams(params);
    const response = await this.http.get<MyAccountUsersListResponseDto>(this.basePath, queryParams);
    return this.buildListResult(response);
  }

  batchCreate(batchUserCreateDto: BatchUserCreateDto) {
    const body = { users: batchUserCreateDto.users, permissions: this.buildRolesList(batchUserCreateDto.roles) };
    // We deliberately ignore the result of this API call, as in the future, this API will work in the background.
    // noinspection JSIgnoredPromiseFromCall
    this.http.post(this.basePath, body);
  }

  private buildMyAccountListParams(params: UserListParamsDto) {
    const queryParams: Record<string, string | number> = {};
    const order = params?.order || UserListSortOrder.DESC;
    const sort = params?.sort;

    queryParams.sortBy = sort ? `${sort}:${order.toUpperCase()}` : '';
    queryParams.select = USERS_LIST_SELECTION;

    queryParams.page = params?.page;
    queryParams.limit = params?.per_page;
    queryParams.search = params?.search;

    return queryParams;
  }

  private buildListResult(myAccountResponse: MyAccountUsersListResponseDto) {
    const { items_per_page, total_items, total_pages, current_page } = myAccountResponse.meta;
    return { items: myAccountResponse.data, meta: { items_per_page, total_items, total_pages, current_page } };
  }

  private buildRolesList(batchRoles: BatchUserCreateRoles) {
    const roles: string[] = [];

    if (!batchRoles) {
      return [];
    }

    // My account roles
    if (batchRoles.myAccountUser) {
      roles.push(MYACCOUNT_ROLES.ACCOUNT_ADMIN);
    }

    if (batchRoles.myAccountAdmin) {
      roles.push(MYACCOUNT_ROLES.COMPANY_ADMIN);
    }

    // Konquest roles
    if (batchRoles.konquestUser) {
      roles.push(KONQUEST_ROLES.USER);
    }

    if (batchRoles.konquestAdmin) {
      roles.push(KONQUEST_ROLES.ADMIN);
    }

    if (batchRoles.konquestSuperAdmin) {
      roles.push(KONQUEST_ROLES.SUPER_ADMIN);
    }

    if (batchRoles.konquestContributor) {
      roles.push(KONQUEST_ROLES.CONTENT);
    }

    if (batchRoles.konquestCurator) {
      roles.push(KONQUEST_ROLES.CURATOR);
    }

    if (batchRoles.konquestInstructor) {
      roles.push(KONQUEST_ROLES.INSTRUCTOR);
    }

    return roles;
  }
}
