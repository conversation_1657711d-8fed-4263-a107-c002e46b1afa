import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class PaginationMetaDto {
  @ApiProperty({ description: 'Number of items per page', example: 10 })
  @Expose()
  items_per_page: number;

  @ApiProperty({ description: 'Total number of items', example: 100 })
  @Expose()
  total_items: number;

  @ApiProperty({ description: 'Total number of pages', example: 10 })
  @Expose()
  total_pages: number;

  @ApiProperty({ description: 'Current page number', example: 1 })
  @Expose()
  current_page: number;
}
