import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class ContentTypeDto {
  @ApiPropertyOptional({
    description: 'Unique identifier of the content type',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({
    description: 'Name of the content type',
    example: 'Closed for Workspace',
  })
  @Expose()
  name: string;
}
