import { ApiPropertyOptional } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class CategoryDto {
  @ApiPropertyOptional({
    description: 'Unique identifier of the category',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  id: string;

  @ApiPropertyOptional({
    description: 'Name of the category',
    example: 'Work',
  })
  @Expose()
  name: string;
}
