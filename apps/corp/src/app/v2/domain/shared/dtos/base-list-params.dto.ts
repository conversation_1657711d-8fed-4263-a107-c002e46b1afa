import { ApiProperty } from '@nestjs/swagger';
import { IsInt, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class BaseListParamsDto {
  @ApiProperty({
    description: 'Page number to retrieve, if invalid, the default page number will be used',
    default: 1,
    example: 1,
    required: false,
    type: Number,
  })
  @IsNumber()
  @IsInt()
  @IsOptional()
  @Type(() => Number)
  page = 1;

  @ApiProperty({
    description: 'Number of records per page',
    default: 20,
    example: 20,
    maximum: 100,
    required: false,
    type: Number,
  })
  @IsNumber()
  @IsInt()
  @IsOptional()
  @Type(() => Number)
  per_page = 20;

  @ApiProperty({
    description: 'Search term to filter by',
    required: false,
  })
  @IsString()
  @IsOptional()
  search: string;
}
