import { ConfigService } from '@nestjs/config';
import { BaseListParamsDto } from '../dtos/base-list-params.dto';
import { KonquestResponseDto } from '../dtos/konquest-response.dto';
import { Injectable } from '@nestjs/common';
import { CorpHttpClient } from '../../../infra';

@Injectable()
export class KonquestBaseService<T> {
  protected readonly KONQUEST_URL: string;

  constructor(
    protected readonly configService: ConfigService,
    protected readonly http: CorpHttpClient,
  ) {
    this.KONQUEST_URL = this.configService.get('KONQUEST_URL');
  }

  protected buildKonquestListParams(params: BaseListParamsDto) {
    const queryParams: Record<string, string | number> = {};
    queryParams.per_page = params.per_page;
    queryParams.page = params.page;
    queryParams.search = params.search;

    return queryParams;
  }

  protected buildListResult(
    result: KonquestResponseDto<T>,
    params: BaseListParamsDto,
  ): {
    items: T[];
    meta: { items_per_page: number; current_page: number; total_items: number; total_pages: number };
  } {
    const { count, results } = result;
    return {
      items: results,
      meta: {
        items_per_page: params.per_page,
        current_page: params.page,
        total_items: count,
        total_pages: Math.ceil(count / params.per_page),
      },
    };
  }
}
