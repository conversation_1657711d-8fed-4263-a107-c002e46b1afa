import { Module } from '@nestjs/common';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { WorkspacesModule } from './workspaces/workspaces.module';
import { GroupsModule } from './groups/groups.module';
import { PulsesModule } from './pulses/pulses.module';
import { MissionsModule } from './missions/missions.module';
import { TrailsModule } from './trails/trails.module';
import { EnrollmentsModule } from './enrollments/enrollments.module';

// When generating the docs, we need to specify which modules should be used
// otherwise, all controllers will be mapped by the swagger doc builder
export const V2_MODULES = [
  AuthModule,
  UsersModule,
  WorkspacesModule,
  GroupsModule,
  PulsesModule,
  MissionsModule,
  TrailsModule,
  EnrollmentsModule,
];

@Module({ imports: V2_MODULES })
export class DomainV2Module {}
