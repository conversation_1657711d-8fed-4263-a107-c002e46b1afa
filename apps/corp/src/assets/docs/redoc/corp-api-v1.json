{"openapi": "3.0.0", "servers": [{"url": "https://corp-api.keepsdev.com/v1", "description": "URL"}], "info": {"title": "Keeps Corp API", "version": "1.0", "description": ">  ## Instruções de uso\n  >  ## Pré-requisitos: \n    > - Postman (ou similar)\n    > - x-client da workspace \n    > - Usu<PERSON>rio e senha com permissão de admin na workspace.\n  >  ## Passos:\n    > -  Criar/Importar collection no postman\n    > - Configurar variáveis de ambiente:\n    > - CONTENT_TYPE_JSON = application/json\n    > - ACCESS_TOKEN = gerado pela chamada no endpoint auth\n    > - CORP_API = https://corp-api.keepsdev.com/v1\n    > - X_CLIENT = x-client da workspace"}, "tags": [], "paths": {"/auth": {"post": {"summary": "Generate token", "operationId": "auth<PERSON><PERSON><PERSON>", "description": "Authentication on aplication", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthTokenDto"}}}}, "tags": ["Authentication"], "responses": {"201": {"description": "Default response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Auth"}}}}, "401": {"description": "Invalid user credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Auth-error"}}}}}}}, "/users": {"get": {"summary": "Get all users", "description": "List all users in the workspace", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "schema": {"type": "string"}}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "schema": {"type": "integer"}}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "schema": {"type": "integer"}}], "tags": ["Users"], "operationId": "getAllUsers", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/{id}": {"get": {"summary": "Get user by id", "description": "List a specific user", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}, {"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}], "tags": ["Users"], "operationId": "getUserById", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "patch": {"summary": "Partial user update", "operationId": "partialUserUpdate", "description": "Partial user update", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}, {"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}], "tags": ["Users"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"summary": "Full user update", "operationId": "fullUserUpdate", "description": "Full user update", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}, {"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}], "tags": ["Users"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/users/roles": {"post": {"summary": "Set role to user", "operationId": "users_roles", "description": "Add user permission to workspace", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRole"}}}}, "tags": ["Users"], "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddRoleResponse"}}}}}, "400": {"description": "Not a valid UUID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddRoleError"}}}}}}}, "/users/roles/{id}": {"delete": {"summary": "Remove user role", "operationId": "removeUserRole", "description": "Remove user permission to workspace", "parameters": [{"name": "id", "description": "Role ID", "required": true, "in": "path", "schema": {"type": "string", "format": "uuid"}}], "tags": ["Users"], "responses": {"204": {"description": ""}}}}, "/workspaces": {"get": {"summary": "Get all workspaces", "description": "List all workspaces you have access to", "parameters": [], "tags": ["Workspace"], "operationId": "getAllWorkspaces", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Workspace"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/workspaces/{id}": {"get": {"summary": "Get workspace by id", "description": "List a specific workspace", "parameters": [{"name": "id", "in": "path", "description": "Workspace ID", "required": true, "schema": {"type": "string"}}], "tags": ["Workspace"], "operationId": "getWorkspaceById", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Workspace"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/workspaces/{id}/users": {"get": {"summary": "Get all users from workspace", "description": "List all workspace users", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}], "tags": ["Workspace"], "operationId": "getWorkspaceUsers", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkspaceUsersList"}}}}}, "404": {"description": "Not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"summary": "Add users to the workspace", "description": "Add users to the workspace", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceUserCreate"}}}}, "tags": ["Workspace"], "operationId": "addWorkspaceUsers", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceUserCreateResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/workspaces/{id}/users/{user_id}": {"get": {"summary": "Get a user from workspace", "description": "List a workspace user", "parameters": [{"name": "id", "in": "path", "description": "Workspace ID", "required": true, "schema": {"type": "string"}}, {"name": "user_id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}], "tags": ["Workspace"], "operationId": "getWorkspaceUserById", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/User"}}}}}, "404": {"description": "Not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "patch": {"summary": "Partial workspace user update", "operationId": "partialWorkspaceUserUpdate", "description": "Partial workspace user update", "parameters": [{"name": "id", "in": "path", "description": "Workspace ID", "required": true, "schema": {"type": "string"}}, {"name": "user_id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}], "tags": ["Workspace"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceUserUpdate"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "put": {"summary": "Full workspace user update", "operationId": "fullWorkspaceUserUpdate", "description": "Full workspace user update", "parameters": [{"name": "id", "in": "path", "description": "Workspace ID", "required": true, "schema": {"type": "string"}}, {"name": "user_id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "string"}}], "tags": ["Workspace"], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkspaceUserUpdate"}}}}, "responses": {"200": {"description": "Successful operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/missions": {"get": {"tags": ["Missions"], "summary": "Get all missions", "description": "List all workspace missions", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "schema": {"type": "string"}}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "schema": {"type": "integer"}}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "schema": {"type": "integer"}}], "operationId": "getAllMissions", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": ["string", "null"], "format": "uri"}, "previous": {"type": ["string", "null"], "format": "uri"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/MissionList"}}}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/missions/{id}": {"get": {"tags": ["Missions"], "summary": "Get mission by id", "description": "List a specific mission", "parameters": [{"name": "id", "in": "path", "description": "Mission ID", "required": true, "schema": {"type": "string"}}], "operationId": "getMissionById", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MissionList"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/trails": {"get": {"tags": ["Trails"], "summary": "Get all trails", "description": "List all trails", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "schema": {"type": "string"}}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "schema": {"type": "integer"}}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "schema": {"type": "integer"}}], "operationId": "getAllTrails", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": ["string", "null"], "format": "uri"}, "previous": {"type": ["string", "null"], "format": "uri"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/TrailList"}}}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/trails/{id}": {"get": {"tags": ["Trails"], "summary": "Get trail by id", "description": "List a specific trail", "parameters": [{"name": "id", "in": "path", "description": "Trail ID", "required": true, "schema": {"type": "string"}}], "operationId": "getTrailById", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrailList"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/mission-enrollments": {"get": {"tags": ["Mission-enrollments"], "summary": "Get all mission enrollments", "description": "List all mission enrollments in the workspace", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "schema": {"type": "string"}}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "schema": {"type": "integer"}}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "schema": {"type": "integer"}}], "operationId": "missionEnrollmentsList", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": ["string", "null"], "format": "uri"}, "previous": {"type": ["string", "null"], "format": "uri"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/MissionEnrollList"}}}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["Mission-enrollments"], "summary": "Create mission enrollment", "description": "Create an enrollment in a mission", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MissionEnroll"}}}}, "operationId": "missionEnroll", "responses": {"201": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"enrollment_errors": {"type": "array", "items": {"type": "object"}}}}}}}, "400": {"description": "Not a valid UUID or date", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MissionEnrollError"}}}}}}}, "/mission-enrollments/{id}": {"get": {"tags": ["Mission-enrollments"], "summary": "Get a mission enrollment", "description": "List a specific trail enrollment", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "id", "in": "path", "description": "Enrollment ID", "required": true, "schema": {"type": "string"}}], "operationId": "missionEnrollmentList", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MissionEnrollList"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/learning-trail-enrollments": {"get": {"tags": ["Learning-trail-enrollments"], "summary": "Get all trails enrollments", "description": "List all trails enrollments in the workspace", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "search", "in": "query", "description": "A search term.", "required": false, "schema": {"type": "string"}}, {"name": "ordering", "in": "query", "description": "Which field to use when ordering the results.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "A page number within the paginated result set.", "required": false, "schema": {"type": "integer"}}, {"name": "per_page", "in": "query", "description": "Number of results to return per page.", "required": false, "schema": {"type": "integer"}}], "operationId": "", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": ["string", "null"], "format": "uri"}, "previous": {"type": ["string", "null"], "format": "uri"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/TrailEnrollList"}}}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}, "post": {"tags": ["Learning-trail-enrollments"], "summary": "Create trail enrollment", "description": "Create an enrollment in a trail", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrailEnroll"}}}}, "operationId": "traillEnroll", "responses": {"201": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"enrollment_errors": {"type": "array", "items": {"type": "object"}}}}}}}, "400": {"description": "Not a valid UUID or date", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrailEnrollError"}}}}}}}, "/learning-trail-enrollments/{id}": {"get": {"tags": ["Learning-trail-enrollments"], "summary": "Get a trail enrollment", "description": "List a specific trail enrollment", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "id", "in": "path", "description": "Enrollment ID", "required": true, "schema": {"type": "string"}}], "operationId": "trailEnrollmentList", "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TrailEnrollList"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/groups": {"get": {"tags": ["Groups"], "summary": "Get all groups", "description": "List all groups in the workspace", "operationId": "groupList", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "search", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/GroupsList"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/groups/{id}/users": {"post": {"tags": ["Groups"], "summary": "Group enroll", "description": "Enroll in missions and trails from the group", "operationId": "groupEnroll", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "id", "description": "Group ID", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupLink"}}}}, "responses": {"201": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"group_user_errors": {"type": "array", "items": {"type": "string"}}, "group_user_warnings": {"type": "array", "items": {"type": "string"}}}}}}}, "400": {"description": "Not a valid UUID or date", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserGroupLinkError"}}}}}}}, "/pulses": {"get": {"tags": ["Pulses"], "summary": "Get all pulses", "description": "List all pulses in the workspace", "operationId": "pulsesList", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "search", "required": false, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": ["string", "null"], "format": "uri"}, "previous": {"type": ["string", "null"], "format": "uri"}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/pulseList"}}}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}, "/pulses/{id}": {"get": {"tags": ["Pulses"], "summary": "Get pulse by id", "description": "Get a especific pulse", "operationId": "pulseList", "parameters": [{"name": "X-Client", "in": "header", "required": true, "schema": {"type": "string"}, "description": "Workspace ID"}, {"name": "id", "description": "Pulse ID", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Default response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/pulseList"}}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Error"}}}}}}}}, "components": {"schemas": {"Error": {"type": "object", "properties": {"status_code": {"type": "string"}, "detail": {"type": "string"}}, "xml": {"name": "User"}}, "AuthTokenDto": {"type": "object", "properties": {"username": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "password123"}}, "required": ["username", "password"]}, "Auth": {"type": "object", "properties": {"access_token": {"type": "string", "format": "int64"}, "expires_in": {"type": "integer"}, "refresh_expires_in": {"type": "integer"}, "refresh_token": {"type": "string"}, "token_type": {"type": "string"}, "not-before-policy": {"type": "integer"}, "session_state": {"type": "string"}, "scope": {"type": "string"}}}, "Auth-error": {"type": "object", "properties": {"error": {"type": "string", "format": "int64"}, "error_description": {"type": "string"}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "key": {"type": "string"}, "application": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}}}, "workspace": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "icon_url": {"type": "string", "format": "uri"}, "logo_url": {"type": "string", "format": "uri"}}}, "self_sign_up": {"type": "boolean"}}}}, "profiles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "job": {"type": "string"}, "director": {"type": "string"}, "manager": {"type": "string"}, "area_of_activity": {"type": "string"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "job_position": {"type": "string", "format": "uuid"}, "job_function": {"type": ["string", "null"], "format": "uuid"}}}}, "created": {"type": "boolean"}, "job": {"type": "string"}, "job_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "nickname": {"type": ["string", "null"]}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "secondary_email": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "avatar": {"type": ["string", "null"]}, "gender": {"type": ["string", "null"]}, "birthday": {"type": ["string", "null"], "format": "date"}, "address": {"type": ["string", "null"]}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "cpf": {"type": ["string", "null"]}, "admission_date": {"type": ["string", "null"], "format": "date"}, "ethnicity": {"type": ["string", "null"]}, "marital_status": {"type": ["string", "null"]}, "education": {"type": ["string", "null"]}, "hierarchical_level": {"type": ["string", "null"]}, "contract_type": {"type": ["string", "null"]}, "status": {"type": "boolean"}, "time_zone": {"type": "string"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "language": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "status": {"type": "boolean"}, "created_date": {"type": ["string", "null"], "format": "date-time"}, "updated_date": {"type": ["string", "null"], "format": "date-time"}}}, "related_user_leader": {"type": ["string", "null"]}}}, "UpdateUserDto": {"type": "object", "properties": {"name": {"type": "string"}, "nickname": {"type": ["string", "null"]}, "secondary_email": {"type": ["string", "null"], "format": "email"}, "phone": {"type": ["string", "null"]}, "gender": {"type": ["string", "null"]}, "birthday": {"type": ["string", "null"], "format": "date"}, "address": {"type": ["string", "null"]}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "avatar": {"type": ["string", "null"], "format": "uri"}, "status": {"type": "boolean"}, "job": {"type": ["string", "null"]}, "job_id": {"type": ["string", "null"], "format": "uuid"}, "director": {"type": ["string", "null"]}, "manager": {"type": ["string", "null"]}, "area_of_activity": {"type": ["string", "null"]}, "job_position": {"type": ["string", "null"], "format": "uuid"}, "job_function": {"type": ["string", "null"], "format": "uuid"}, "language_id": {"type": "string", "format": "uuid"}, "related_user_leader_id": {"type": ["string", "null"], "format": "uuid"}, "email_verified": {"type": "boolean"}, "time_zone": {"type": "string"}, "admission_date": {"type": ["string", "null"], "format": "date"}, "contract_type": {"type": ["string", "null"]}, "cpf": {"type": ["string", "null"]}, "education": {"type": ["string", "null"]}, "ethnicity": {"type": ["string", "null"]}, "hierarchical_level": {"type": ["string", "null"]}, "marital_status": {"type": ["string", "null"]}}}, "CreateUserRole": {"type": "object", "properties": {"role": {"type": "string", "format": "uuid"}, "user": {"type": "string", "format": "uuid"}, "workspace": {"type": "string", "format": "uuid"}}, "required": ["role", "user", "workspace"]}, "AddRoleResponse": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "ein": {"type": ["string", "null"]}, "country": {"type": ["string", "null"]}, "language": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "status": {"type": "boolean"}}}, "avatar": {"type": "string", "format": "uri"}}}, "role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "key": {"type": "string"}, "application": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}}}, "workspace": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "icon_url": {"type": "string", "format": "uri"}, "logo_url": {"type": "string", "format": "uri"}}}}}, "AddRoleError": {"type": "object", "properties": {"user": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "role": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "workspace": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "status_code": {"type": "integer", "example": 400}}}, "Workspace": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "company": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "duns_number": {"type": ["string", "null"]}, "doc_number": {"type": ["string", "null"]}, "description": {"type": ["string", "null"]}, "status": {"type": "boolean"}, "hash_id": {"type": "string"}, "address": {"type": ["string", "null"]}, "city": {"type": ["string", "null"]}, "state": {"type": ["string", "null"]}, "post_code": {"type": ["string", "null"]}, "country": {"type": ["string", "null"]}, "icon_url": {"type": ["string", "null"], "format": "uri"}, "icon_svg_url": {"type": ["string", "null"], "format": "uri"}, "logo_url": {"type": ["string", "null"], "format": "uri"}, "theme_id": {"type": ["string", "null"]}, "theme_dark": {"type": "boolean"}, "use_own_smtp": {"type": "boolean"}, "enable_email_notifications": {"type": "boolean"}, "smtp_host": {"type": ["string", "null"]}, "smtp_port": {"type": ["number", "null"]}, "smtp_secure": {"type": "boolean"}, "smtp_auth_user": {"type": ["string", "null"]}, "smtp_auth_pass": {"type": ["string", "null"]}, "smtp_sender_email": {"type": ["string", "null"], "format": "email"}, "smtp_reject_unauthorized": {"type": "boolean"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "logout_url": {"type": ["string", "null"], "format": "uri"}, "custom_color": {"type": ["string", "null"]}, "custom_login_url": {"type": ["string", "null"], "format": "uri"}, "notify_slack": {"type": "boolean"}, "notify_teams": {"type": "boolean"}, "alura_integration_active": {"type": "boolean"}, "allow_list_public_categories": {"type": "boolean"}, "allow_list_public_channel": {"type": "boolean"}, "allow_create_public_channel": {"type": "boolean"}, "allow_list_paid_channel": {"type": "boolean"}, "allow_create_paid_channel": {"type": "boolean"}, "need_approve_channel": {"type": "boolean"}, "allow_list_public_mission": {"type": "boolean"}, "allow_create_public_mission": {"type": "boolean"}, "allow_list_paid_mission": {"type": "boolean"}, "allow_create_paid_mission": {"type": "boolean"}, "need_approve_mission": {"type": "boolean"}, "enrollment_goal_duration_days": {"type": "number"}, "min_performance_certificate": {"type": "number"}}}, "WorkspaceUserCreate": {"type": "object", "properties": {"permissions": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "users": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "nickname": {"type": "string"}, "email": {"type": "string", "format": "email"}, "password": {"type": "string"}, "temporary_password": {"type": "boolean"}, "secondary_email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "gender": {"type": ["string", "null"]}, "birthday": {"type": "string", "format": "date"}, "address": {"type": "string"}, "country": {"type": "string"}, "ein": {"type": "string"}, "avatar": {"type": "string", "format": "uri"}, "status": {"type": "boolean"}, "job": {"type": "string"}, "job_id": {"type": "string", "format": "uuid"}, "director": {"type": "string"}, "manager": {"type": "string"}, "area_of_activity": {"type": "string"}, "job_position": {"type": "string", "format": "uuid"}, "job_function": {"type": "string", "format": "uuid"}, "language_id": {"type": "string", "format": "uuid"}, "related_user_leader_id": {"type": "string", "format": "uuid"}, "email_verified": {"type": "boolean"}, "time_zone": {"type": "string"}, "admission_date": {"type": "string", "format": "date"}, "contract_type": {"type": "string"}, "cpf": {"type": "string"}, "education": {"type": "string"}, "ethnicity": {"type": "string"}, "hierarchical_level": {"type": "string"}, "marital_status": {"type": "string"}}}}}, "required": ["permissions", "users"]}, "WorkspaceUserCreateResponse": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "key": {"type": "string"}, "application": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}}}, "workspace": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "icon_url": {"type": ["string", "null"], "format": "uri"}, "logo_url": {"type": ["string", "null"], "format": "uri"}}}, "self_sign_up": {"type": "boolean"}}}}, "profiles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "job": {"type": "string"}, "director": {"type": ["string", "null"]}, "manager": {"type": ["string", "null"]}, "area_of_activity": {"type": ["string", "null"]}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "job_position": {"type": "string", "format": "uuid"}, "job_function": {"type": ["string", "null"], "format": "uuid"}}}}, "created": {"type": "boolean"}, "job": {"type": "string"}, "job_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "nickname": {"type": "string"}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "secondary_email": {"type": "string", "format": "email"}, "phone": {"type": "string"}, "avatar": {"type": "string", "format": "uri"}, "gender": {"type": ["string", "null"]}, "birthday": {"type": "string", "format": "date"}, "address": {"type": "string"}, "country": {"type": "string"}, "ein": {"type": "string"}, "cpf": {"type": "string"}, "admission_date": {"type": "string", "format": "date"}, "ethnicity": {"type": "string"}, "marital_status": {"type": "string"}, "education": {"type": "string"}, "hierarchical_level": {"type": "string"}, "contract_type": {"type": "string"}, "status": {"type": "boolean"}, "time_zone": {"type": "string"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "language": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "status": {"type": "boolean"}, "created_date": {"type": ["string", "null"], "format": "date-time"}, "updated_date": {"type": ["string", "null"], "format": "date-time"}}}, "related_user_leader": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "nickname": {"type": "string"}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "secondary_email": {"type": ["string", "null"], "format": "email"}, "phone": {"type": "string"}, "avatar": {"type": ["string", "null"], "format": "uri"}, "gender": {"type": ["string", "null"]}, "birthday": {"type": ["string", "null"], "format": "date"}, "address": {"type": "string"}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "cpf": {"type": ["string", "null"]}, "admission_date": {"type": ["string", "null"], "format": "date"}, "ethnicity": {"type": ["string", "null"]}, "marital_status": {"type": ["string", "null"]}, "education": {"type": ["string", "null"]}, "hierarchical_level": {"type": ["string", "null"]}, "contract_type": {"type": ["string", "null"]}, "status": {"type": "boolean"}, "time_zone": {"type": "string"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "language": {"type": "string", "format": "uuid"}, "related_user_leader": {"type": ["object", "null"]}}}}}}, "WorkspaceUserUpdate": {"type": "object", "properties": {"address": {"type": "string"}, "birthday": {"type": "string", "format": "date"}, "gender": {"type": "string"}, "job": {"type": "string"}, "name": {"type": "string"}, "nickname": {"type": "string"}, "phone": {"type": "string"}, "secondary_email": {"type": "string", "format": "email"}}}, "WorkspaceUsersList": {"type": "object", "properties": {"count": {"type": "integer"}, "next": {"type": ["string", "null"], "format": "uri"}, "previous": {"type": ["string", "null"], "format": "uri"}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "roles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "role": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "key": {"type": "string"}, "application": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}}}, "workspace": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "icon_url": {"type": ["string", "null"], "format": "uri"}, "logo_url": {"type": ["string", "null"], "format": "uri"}}}, "self_sign_up": {"type": "boolean"}}}}, "profiles": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "job": {"type": "string"}, "director": {"type": ["string", "null"]}, "manager": {"type": ["string", "null"]}, "area_of_activity": {"type": ["string", "null"]}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "job_position": {"type": ["string", "null"], "format": "uuid"}, "job_function": {"type": ["string", "null"], "format": "uuid"}}}}, "created": {"type": "boolean"}, "job": {"type": "string"}, "job_id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "nickname": {"type": "string"}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "secondary_email": {"type": ["string", "null"], "format": "email"}, "phone": {"type": "string"}, "avatar": {"type": ["string", "null"], "format": "uri"}, "gender": {"type": ["string", "null"]}, "birthday": {"type": "string", "format": "date"}, "address": {"type": "string"}, "country": {"type": ["string", "null"]}, "ein": {"type": "string"}, "cpf": {"type": ["string", "null"]}, "admission_date": {"type": ["string", "null"], "format": "date"}, "ethnicity": {"type": ["string", "null"]}, "marital_status": {"type": ["string", "null"]}, "education": {"type": ["string", "null"]}, "hierarchical_level": {"type": ["string", "null"]}, "contract_type": {"type": ["string", "null"]}, "status": {"type": "boolean"}, "time_zone": {"type": "string"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "language": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "status": {"type": "boolean"}, "created_date": {"type": ["string", "null"], "format": "date-time"}, "updated_date": {"type": ["string", "null"], "format": "date-time"}}}, "related_user_leader": {"type": ["object", "null"], "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "nickname": {"type": "string"}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "secondary_email": {"type": ["string", "null"], "format": "email"}, "phone": {"type": "string"}, "avatar": {"type": ["string", "null"], "format": "uri"}, "gender": {"type": ["string", "null"]}, "birthday": {"type": ["string", "null"], "format": "date"}, "address": {"type": "string"}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "cpf": {"type": ["string", "null"]}, "admission_date": {"type": ["string", "null"], "format": "date"}, "ethnicity": {"type": ["string", "null"]}, "marital_status": {"type": ["string", "null"]}, "education": {"type": ["string", "null"]}, "hierarchical_level": {"type": ["string", "null"]}, "contract_type": {"type": ["string", "null"]}, "status": {"type": "boolean"}, "time_zone": {"type": "string"}, "created_date": {"type": ["string", "null"], "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "language": {"type": "string", "format": "uuid"}, "related_user_leader": {"type": ["object", "null"]}}}}}}}}, "MissionList": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "user_creator": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "avatar": {"type": "string", "format": "uri"}}}, "live": {"type": ["boolean", "null"]}, "presential": {"type": ["boolean", "null"]}, "external": {"type": ["boolean", "null"]}, "external_course_url": {"type": ["string", "null"], "format": "uri"}, "provider": {"type": ["string", "null"]}, "provider_mission_type": {"type": ["string", "null"]}, "learning_trail_linked": {"type": "boolean"}, "is_owner": {"type": "boolean"}, "mission_type": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "image": {"type": "string", "format": "uri"}}}, "mission_category": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}}}, "stages": {"type": "integer"}, "rating_avg": {"type": "number"}, "users_enrolled": {"type": "integer"}, "is_contributor": {"type": "boolean"}, "bookmark_id": {"type": ["string", "null"], "format": "uuid"}, "enrollments_accepted": {"type": "integer"}, "workspace_min_performance": {"type": "number"}, "workspace_source_id": {"type": "string", "format": "uuid"}, "is_active": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}}, "enrollment": {"type": ["object", "null"]}, "content_resume": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "image": {"type": "string", "format": "uri"}, "image_cover": {"type": "string", "format": "uri"}, "count": {"type": "integer"}, "mission": {"type": "string", "format": "uuid"}}}}, "contributors": {"type": "array", "items": {"type": "object"}}, "users_finished": {"type": "integer"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string"}, "holder_image": {"type": ["string", "null"], "format": "uri"}, "vertical_holder_image": {"type": "string", "format": "uri"}, "thumb_image": {"type": "string", "format": "uri"}, "summary": {"type": "string"}, "duration_time": {"type": "integer"}, "points": {"type": "integer"}, "enrollment_goal_duration_days": {"type": "integer"}, "internal_code": {"type": ["string", "null"]}, "_is_active": {"type": "boolean"}, "development_status": {"type": "string"}, "language": {"type": "string"}, "expiration_date": {"type": ["string", "null"], "format": "date-time"}, "mission_model": {"type": "string"}, "required_evaluation": {"type": "boolean"}, "assessment_type": {"type": "string"}, "allow_self_enrollment_renewal": {"type": "boolean"}, "allow_self_reproved_enrollment_renewal": {"type": "boolean"}, "minimum_performance": {"type": "number"}}}, "TrailList": {"type": "object", "properties": {"id": {"type": "string"}, "user_creator": {"type": "object", "properties": {"id": {"type": "string"}, "job": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "avatar": {"type": "string"}, "status": {"type": "boolean"}, "phone": {"type": "string"}, "last_access_date": {"type": "string", "format": "date-time"}, "language_id": {"type": "string"}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "time_zone": {"type": "string"}, "related_user_leader": {"type": "string"}}}, "learning_trail_type": {"type": "object", "properties": {"id": {"type": "string"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string"}, "image": {"type": "string"}}}, "count_missions": {"type": "integer"}, "count_pulses": {"type": "integer"}, "users_enrolled": {"type": "integer"}, "users_finished": {"type": "integer"}, "is_owner": {"type": "boolean"}, "enrolled": {"type": "boolean"}, "enrollment": {"type": "object", "properties": {"points": {"type": ["integer", "null"]}, "end_date": {"type": ["string", "null"], "format": "date-time"}, "goal_date": {"type": ["string", "null"], "format": "date"}, "give_up": {"type": "boolean"}, "give_up_comment": {"type": "string"}, "status": {"type": ["string", "null"]}, "performance": {"type": ["integer", "null"]}, "progress": {"type": ["number", "null"]}, "required": {"type": "boolean"}, "certificate_url": {"type": "string"}}}, "steps": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "learning_trail": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}, "mission": {"type": "object", "properties": {"id": {"type": "string"}, "learning_trail_linked": {"type": "boolean"}, "stages": {"type": "integer"}, "rating_avg": {"type": "number"}, "users_enrolled": {"type": "integer"}, "user_creator": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "avatar": {"type": "string"}}}, "is_contributor": {"type": "boolean"}, "bookmark_id": {"type": ["string", "null"]}, "is_owner": {"type": "boolean"}, "enrollment": {"type": ["object", "null"]}, "mission_category": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "tags": {"type": "array", "items": {"type": "string"}}, "external": {"type": ["object", "null"], "properties": {"id": {"type": "integer"}, "provider": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}}, "required": ["id", "name", "description", "icon"]}, "course_url": {"type": "string"}, "course_type": {"type": ["string", "null"]}}}, "live": {"type": ["boolean", "null"]}, "presential": {"type": ["boolean", "null"]}, "external_course_url": {"type": ["string", "null"]}, "provider": {"type": ["object", "null"], "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "icon": {"type": "string"}}}, "mission_type": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "image": {"type": "string"}}}, "provider_mission_type": {"type": ["string", "null"]}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string"}, "holder_image": {"type": ["string", "null"]}, "vertical_holder_image": {"type": ["string", "null"]}, "thumb_image": {"type": ["string", "null"]}, "summary": {"type": ["string", "null"]}, "duration_time": {"type": "integer"}, "points": {"type": "integer"}, "enrollment_goal_duration_days": {"type": ["integer", "null"]}, "internal_code": {"type": ["string", "null"]}, "_is_active": {"type": "boolean"}, "development_status": {"type": "string"}, "language": {"type": "string"}, "expiration_date": {"type": ["string", "null"], "format": "date-time"}, "mission_model": {"type": "string"}, "required_evaluation": {"type": "boolean"}, "assessment_type": {"type": "string"}, "allow_self_enrollment_renewal": {"type": "boolean"}, "allow_self_reproved_enrollment_renewal": {"type": "boolean"}, "minimum_performance": {"type": "integer"}}}, "pulse": {"type": ["object", "null"]}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "order": {"type": "integer"}}}}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string"}, "holder_image": {"type": "string"}, "thumb_image": {"type": "string"}, "duration_time": {"type": "integer"}, "points": {"type": "integer"}, "is_active": {"type": "boolean"}, "language": {"type": "string"}, "expiration_date": {"type": ["string", "null"], "format": "date-time"}}}, "MissionEnrollList": {"type": "object", "properties": {"id": {"type": "string"}, "enrolled_count": {"type": "integer"}, "in_progress": {"type": "boolean"}, "mission": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "description": {"type": "string"}, "user_creator": {"type": "string"}, "learning_trail_linked": {"type": "boolean"}, "mission_model": {"type": "string"}, "holder_image": {"type": "string"}, "vertical_holder_image": {"type": "string"}, "external": {"type": ["object", "null"]}, "required_evaluation": {"type": "boolean"}, "development_status": {"type": "string"}}}, "user": {"type": "object", "properties": {"id": {"type": "string"}, "job": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "avatar": {"type": "string"}, "status": {"type": "boolean"}, "phone": {"type": "string"}, "last_access_date": {"type": "string", "format": "date-time"}, "language_id": {"type": "string"}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "time_zone": {"type": "string"}, "related_user_leader": {"type": "string"}}}, "evaluated": {"type": "boolean"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "points": {"type": ["integer", "null"]}, "performance": {"type": ["integer", "null"]}, "start_date": {"type": "string", "format": "date-time"}, "end_date": {"type": ["string", "null"], "format": "date-time"}, "goal_date": {"type": "string", "format": "date"}, "give_up": {"type": "boolean"}, "give_up_comment": {"type": ["string", "null"]}, "status": {"type": "string"}, "required": {"type": "boolean"}, "normative": {"type": "boolean"}, "progress": {"type": "integer"}, "certificate_url": {"type": ["string", "null"]}, "assessment_type": {"type": ["string", "null"]}, "certificate_provider_url": {"type": ["string", "null"]}, "approve_msg": {"type": ["string", "null"]}, "total_mission_questions": {"type": ["integer", "null"]}, "total_correct_answers": {"type": ["integer", "null"]}}}, "MissionEnroll": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "string"}}, "goal_date": {"type": "string", "format": "date-time"}, "required": {"type": "boolean"}, "missions": {"type": "array", "items": {"type": "string"}}}, "required": ["users", "goal_date", "required", "missions"]}, "MissionEnrollError": {"type": "object", "properties": {"user": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "missions": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "goal_date": {"type": "array", "items": {"type": "string", "example": "Invalid format for date. Use one of the following formats: YYYY-MM-DD, YYYY-MM-DD hh:mm:ss, YYYY-MM-DD hh:mm:ss.uuuuuu, YYYY-MM-DD hh:mm, YYYY-MM-DDThh:mm:ss.uuuuuZ, YYYY-MM-DDThh:mm"}}}}, "TrailEnroll": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "goal_date": {"type": "string", "format": "date-time"}, "required": {"type": "boolean"}, "learning_trails": {"type": "array", "items": {"type": "string", "format": "uuid"}}}, "required": ["users", "goal_date", "required", "learning_trails"]}, "TrailEnrollList": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "learning_trail": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "user_creator": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "job": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "avatar": {"type": "string", "format": "uri"}, "status": {"type": "boolean"}, "phone": {"type": "string"}, "last_access_date": {"type": "string", "format": "date-time"}, "language_id": {"type": "string", "format": "uuid"}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "time_zone": {"type": "string"}, "related_user_leader": {"type": "string", "format": "uuid"}}}, "learning_trail_type": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string"}, "image": {"type": "string", "format": "uri"}}}, "count_missions": {"type": "integer"}, "count_pulses": {"type": "integer"}, "users_enrolled": {"type": "integer"}, "users_finished": {"type": "integer"}, "is_owner": {"type": "boolean"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string"}, "holder_image": {"type": "string", "format": "uri"}, "thumb_image": {"type": "string", "format": "uri"}, "duration_time": {"type": "integer"}, "points": {"type": "integer"}, "is_active": {"type": "boolean"}, "language": {"type": "string"}, "expiration_date": {"type": ["string", "null"], "format": "date-time"}}}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": ["string", "null"], "format": "date-time"}, "deleted": {"type": "boolean"}, "points": {"type": ["integer", "null"]}, "performance": {"type": ["integer", "null"]}, "start_date": {"type": "string", "format": "date-time"}, "end_date": {"type": ["string", "null"], "format": "date-time"}, "goal_date": {"type": "string", "format": "date"}, "give_up": {"type": "boolean"}, "give_up_comment": {"type": ["string", "null"]}, "required": {"type": "boolean"}, "normative": {"type": "boolean"}, "progress": {"type": "integer"}, "certificate_url": {"type": ["string", "null"], "format": "uri"}, "assessment_type": {"type": ["string", "null"]}, "status": {"type": "string"}, "user": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "avatar": {"type": ["string", "null"], "format": "uri"}, "status": {"type": "boolean"}, "phone": {"type": "string"}, "last_access_date": {"type": ["string", "null"], "format": "date-time"}, "language_id": {"type": "string", "format": "uuid"}, "country": {"type": ["string", "null"]}, "ein": {"type": ["string", "null"]}, "time_zone": {"type": "string"}, "related_user_leader": {"type": ["string", "null"], "format": "uuid"}}}, "workspace": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "hash_id": {"type": "string"}, "allow_list_public_categories": {"type": "boolean"}, "allow_list_public_channel": {"type": "boolean"}, "allow_create_public_channel": {"type": "boolean"}, "allow_list_paid_channel": {"type": "boolean"}, "allow_create_paid_channel": {"type": "boolean"}, "need_approve_channel": {"type": "boolean"}, "allow_list_public_mission": {"type": "boolean"}, "allow_create_public_mission": {"type": "boolean"}, "allow_list_paid_mission": {"type": "boolean"}, "allow_create_paid_mission": {"type": "boolean"}, "need_approve_mission": {"type": "boolean"}, "enrollment_goal_duration_days": {"type": "integer"}, "status": {"type": "boolean"}, "icon_url": {"type": ["string", "null"], "format": "uri"}, "logo_url": {"type": ["string", "null"], "format": "uri"}, "custom_color": {"type": ["string", "null"]}, "notify_slack": {"type": "boolean"}, "notify_teams": {"type": "boolean"}, "alura_integration_active": {"type": "boolean"}, "min_performance_certificate": {"type": "integer"}}}}}, "TrailEnrollError": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "learning_trails": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "goal_date": {"type": "array", "items": {"type": "string", "example": "Invalid format for date. Use one of the following formats: YYYY-MM-DD, YYYY-MM-DD hh:mm:ss, YYYY-MM-DD hh:mm:ss.uuuuuu, YYYY-MM-DD hh:mm, YYYY-MM-DDThh:mm:ss.uuuuuZ, YYYY-MM-DDThh:mm"}}}}, "GroupsList": {"type": "object", "properties": {"count": {"type": "integer", "example": 1}, "next": {"type": ["string", "null"], "format": "uri", "example": null}, "previous": {"type": ["string", "null"], "format": "uri", "example": null}, "results": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid", "example": "fabacd46-9874-448c-b005-baed7c411d69"}, "users": {"type": "integer", "example": 1}, "missions": {"type": "integer", "example": 3}, "learning_trails": {"type": "integer", "example": 1}, "channels": {"type": "integer", "example": 0}, "created_date": {"type": "string", "format": "date-time", "example": "2024-06-03T09:40:15.424768-03:00"}, "updated_date": {"type": "string", "format": "date-time", "example": "2024-06-03T09:40:15.424798-03:00"}, "deleted_date": {"type": ["string", "null"], "format": "date-time", "example": null}, "deleted": {"type": "boolean", "example": false}, "name": {"type": "string", "example": "01"}, "description": {"type": ["string", "null"], "example": null}, "workspace": {"type": "string", "format": "uuid", "example": "fa33a6d0-4b8b-46ae-b1f0-77199c396008"}}}}}}, "UserGroupLink": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "string", "format": "uuid"}}, "enrollment_goal_date": {"format": "date-time", "type": "string"}, "enrollment_required_mission": {"type": "boolean"}, "regulatory_compliance_cycle_id": {"type": "string", "format": "uuid"}}, "required": ["users", "enrollment_goal_date", "enrollment_required_mission", "regulatory_compliance_cycle_id"]}, "UserGroupLinkError": {"type": "object", "properties": {"users": {"type": "array", "items": {"type": "string", "example": "Must be a valid UUID"}}, "enrollment_goal_date": {"type": "array", "items": {"type": "string", "example": "Invalid format for date. Use one of the following formats: YYYY-MM-DD, YYYY-MM-DD hh:mm:ss, YYYY-MM-DD hh:mm:ss.uuuuuu, YYYY-MM-DD hh:mm, YYYY-MM-DDThh:mm:ss.uuuuuZ, YYYY-MM-DDThh:mm"}}}}, "pulseList": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "rating_avg": {"type": "number", "nullable": true}, "rating_count": {"type": "integer", "nullable": true}, "channels": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string"}, "category": {"type": "string"}, "channel_category_id": {"type": "string", "format": "uuid"}, "language": {"type": "string"}}}}, "bookmark_id": {"type": "string", "format": "uuid"}, "tags": {"type": "array", "items": {"type": "string"}}, "user_creator": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "job": {"type": "string"}, "name": {"type": "string"}, "email": {"type": "string", "format": "email"}, "email_verified": {"type": "boolean"}, "avatar": {"type": "string", "format": "uri"}, "status": {"type": "boolean"}, "phone": {"type": "string"}, "last_access_date": {"type": "string", "format": "date-time"}, "language_id": {"type": "string", "format": "uuid"}, "country": {"type": "string", "nullable": true}, "ein": {"type": "string", "nullable": true}, "time_zone": {"type": "string"}, "related_user_leader": {"type": "string", "format": "uuid"}}}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": "string", "format": "date-time", "nullable": true}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "holder_image": {"type": "string", "nullable": true}, "duration_time": {"type": "integer"}, "points": {"type": "integer"}, "views": {"type": "integer", "nullable": true}, "learn_content_uuid": {"type": "string", "format": "uuid"}, "language": {"type": "string"}, "status": {"type": "string"}, "is_active": {"type": "boolean"}, "pulse_type": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "created_date": {"type": "string", "format": "date-time"}, "updated_date": {"type": "string", "format": "date-time"}, "deleted_date": {"type": "string", "format": "date-time", "nullable": true}, "deleted": {"type": "boolean"}, "name": {"type": "string"}, "description": {"type": "string"}, "image": {"type": "string", "format": "uri"}, "image_cover": {"type": "string", "format": "uri"}}}}}, "securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}, "api-key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "X-API-Key", "in": "header"}}, "links": {}, "callbacks": {}}, "security": [{"bearer": []}]}}