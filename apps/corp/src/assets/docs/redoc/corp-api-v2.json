{"openapi": "3.0.0", "servers": [{"url": "https://corp-api.keepsdev.com/v2", "description": "URL"}], "info": {"title": "Keeps Corp API", "version": "2.0", "description": ">  ## Instruções de uso\n  >  ## Pré-requisitos: \n    > - Postman (ou similar)\n    > - x-client da workspace \n    > - Usu<PERSON>rio e senha com permissão de admin na workspace.\n  >  ## Passos:\n    > -  Criar/Importar collection no postman\n    > - Configurar variáveis de ambiente:\n    > - CONTENT_TYPE_JSON = application/json\n    > - ACCESS_TOKEN = gerado pela chamada no endpoint auth\n    > - CORP_API = https://corp-api.keepsdev.com/v2\n    > - X_CLIENT = x-client da workspace"}, "tags": [], "paths": {}, "components": {}}