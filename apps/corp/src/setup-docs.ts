import { NestExpressApplication } from '@nestjs/platform-express';
import { join } from 'path';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { V2_MODULES } from './app/v2/domain/domain-v2.module';
import redoc from 'redoc-express';

export const V1_DOC_PATH = '/docs';
export const V2_DOC_PATH = '/docs-v2';

const V2_DOC_DESCRIPTION = `
## Instruções de uso
### Requisitos
Ao interagir com os endpoints da API, é necessário fornecer dois cabeçalhos obrigatórios em todas as requests:
- Header **x-client** com o valor do UUID da workspace desejada.
- Header **Authorization** com o valor do JWT de um usuário autenticado e com as devidas permissões na workspace.

\`\`\`shel
curl -X GET https://corp-api.keepsdev.com/v2/users \\
-H "x-client: 1234-abcd..." \\
-H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0...
 \`\`\`

> É possível gerar um token de autenticação através do endpoint [/auth](/docs-v2#tag/Authentication) da API.

`;

export function setupDocs(app: NestExpressApplication) {
  setupV1Docs(app);
  setupV2Docs(app);
}

function setupV1Docs(app: NestExpressApplication) {
  // Provide V1 docs schema definition
  app.useStaticAssets(join(__dirname, 'assets'), {
    prefix: '/assets/',
  });

  const redocOptions = {
    title: 'Corp API - V1',
    version: '1.0',
    specUrl: '/assets/docs/redoc/corp-api-v1.json',
  };

  console.log('Setting up doc v1');
  app.use('/docs', redoc(redocOptions));
}

function setupV2Docs(app: NestExpressApplication) {
  const v2DocsOptions = new DocumentBuilder()
    .setTitle('Corp API - V2')
    .setDescription(V2_DOC_DESCRIPTION)
    .addGlobalParameters({ in: 'header', name: 'x-client', description: 'Tenant ID', schema: { type: 'string' } })
    .setVersion('2.0')
    .build();
  const v2Document = SwaggerModule.createDocument(app, v2DocsOptions, {
    include: V2_MODULES,
  });
  SwaggerModule.setup('api-v2', app, v2Document);
  const redocOptions = {
    title: 'Corp API - V2',
    version: '2.0',
    specUrl: '/api-v2-json',
  };
  app.use('/docs-v2', redoc(redocOptions));
}
