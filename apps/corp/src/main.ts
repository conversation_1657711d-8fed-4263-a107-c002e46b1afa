import { ClassSerializerInterceptor, Logger, ValidationPipe, VersioningType } from '@nestjs/common';
import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app/app.module';
import { NestExpressApplication } from '@nestjs/platform-express';
import { setupDocs, V1_DOC_PATH, V2_DOC_PATH } from './setup-docs';
import { CorpHttpExceptionFilter } from './app/common/filters/corp-http-exception.filter';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  app.enableVersioning({
    type: VersioningType.URI,
    defaultVersion: '1',
  });

  // As we deal with both V1 and V2 versions of this API, we don't specify the exclusion strategy as `excludeAll`
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  app.useGlobalFilters(new CorpHttpExceptionFilter());

  const port = process.env.PORT || 3000;
  setupDocs(app);

  await app.listen(port);
  Logger.log(`🚀 Application is running on: http://localhost:${port}`);
  Logger.log(`📘 V1 Docs running on: http://localhost:${port}${V1_DOC_PATH}`);
  Logger.log(`📙 V2 Docs running on: http://localhost:${port}${V2_DOC_PATH}`);
}

bootstrap();
