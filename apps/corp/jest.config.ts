/* eslint-disable */
export default {
  displayName: 'corp',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/corp',
  coverageReporters: ['text-summary', ['text', { skipFull: true }], ['lcovonly', { projectRoot: __dirname }]],
  reporters: ['default', ['@casualbot/jest-sonar-reporter', { outputDirectory: 'coverage/corp' }]],
  coverageThreshold: {
    global: {
      branches: 0,
      functions: 0,
      lines: 0,
      statements: 0,
    },
  },
};
