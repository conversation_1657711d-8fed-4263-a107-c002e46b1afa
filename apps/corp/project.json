{"name": "corp", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/corp/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/corp", "main": "apps/corp/src/main.ts", "tsConfig": "apps/corp/tsconfig.app.json", "assets": ["apps/corp/src/assets"], "isolatedConfig": true, "webpackConfig": "apps/corp/webpack.config.js"}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "corp:build"}, "configurations": {"development": {"buildTarget": "corp:build:development"}, "production": {"buildTarget": "corp:build:production"}}}, "lint": {"executor": "@nx/eslint:lint", "outputs": ["{options.outputFile}"]}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/corp/jest.config.ts"}, "configurations": {"dev": {"ci": true, "codeCoverage": true, "coverageReporters": ["html", "text-summary", "lcov"]}}, "defaultConfiguration": "dev"}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "corp-api", "hostUrl": "https://sonar.keepsdev.com", "projectKey": "corp-api", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "extra": {"sonar.testExecutionReportPaths": "coverage/corp/jest-sonar.xml", "sonar.plugins.downloadOnlyRequired": "true"}}}}}