{"info": {"_postman_id": "43a5f034-72e5-4739-b06b-4a87ec3f29a0", "name": "Corp", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "26061083"}, "item": [{"name": "Authentication", "item": [{"name": "<PERSON><PERSON>", "event": [{"listen": "test", "script": {"exec": ["var jsonData = JSON.parse(responseBody);\r", "pm.environment.set(\"ACCESS_TOKEN\", jsonData['access_token']);"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [{"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}, {"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\"username\":\"<EMAIL>\", \"password\":\"password\"}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/auth/", "host": ["{{CORP_API}}"], "path": ["v1", "auth", ""]}}, "response": []}], "description": "Use for application authentication"}, {"name": "User", "item": [{"name": "List users", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status test\", function () {\r", "    pm.response.to.have.status(200);\r", "});"], "type": "text/javascript", "packages": {}}}], "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}, {"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}], "url": {"raw": "{{CORP_API}}/v1/users", "host": ["{{CORP_API}}"], "path": ["v1", "users"]}}, "response": []}, {"name": "List a user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/users/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "users", "{id}"]}}, "response": []}, {"name": "User update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}, {"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"string\",\r\n  \"nickname\": \"string\",\r\n  \"secondary_email\": \"<EMAIL>\",\r\n  \"phone\": \"string\",\r\n  \"gender\": \"string\",\r\n  \"birthday\": \"2019-08-24\",\r\n  \"address\": \"string\",\r\n  \"country\": \"string\",\r\n  \"ein\": \"string\",\r\n  \"avatar\": \"http://example.com\",\r\n  \"status\": true,\r\n  \"job\": \"string\",\r\n  \"job_id\": \"453bd7d7-5355-4d6d-a38e-d9e7eb218c3f\",\r\n  \"director\": \"string\",\r\n  \"manager\": \"string\",\r\n  \"area_of_activity\": \"string\",\r\n  \"job_position\": \"631f5393-bdba-412a-8f90-2b05914b63df\",\r\n  \"job_function\": \"string\",\r\n  \"language_id\": \"e5383c4a-ba2f-4900-a483-8de374094cdf\",\r\n  \"related_user_leader_id\": \"be56247e-ce01-422f-a036-6effdf0ac4c1\",\r\n  \"email_verified\": true,\r\n  \"time_zone\": \"string\",\r\n  \"admission_date\": \"2019-08-24\",\r\n  \"contract_type\": \"string\",\r\n  \"cpf\": \"string\",\r\n  \"education\": \"string\",\r\n  \"ethnicity\": \"string\",\r\n  \"hierarchical_level\": \"string\",\r\n  \"marital_status\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/users/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "users", "{id}"]}}, "response": []}, {"name": "Partial user update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}, {"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"string\",\r\n  \"nickname\": \"string\",\r\n  \"secondary_email\": \"<EMAIL>\",\r\n  \"phone\": \"string\",\r\n  \"gender\": \"string\",\r\n  \"birthday\": \"2019-08-24\",\r\n  \"address\": \"string\",\r\n  \"country\": \"string\",\r\n  \"ein\": \"string\",\r\n  \"avatar\": \"http://example.com\",\r\n  \"status\": true,\r\n  \"job\": \"string\",\r\n  \"job_id\": \"453bd7d7-5355-4d6d-a38e-d9e7eb218c3f\",\r\n  \"director\": \"string\",\r\n  \"manager\": \"string\",\r\n  \"area_of_activity\": \"string\",\r\n  \"job_position\": \"631f5393-bdba-412a-8f90-2b05914b63df\",\r\n  \"job_function\": \"string\",\r\n  \"language_id\": \"e5383c4a-ba2f-4900-a483-8de374094cdf\",\r\n  \"related_user_leader_id\": \"be56247e-ce01-422f-a036-6effdf0ac4c1\",\r\n  \"email_verified\": true,\r\n  \"time_zone\": \"string\",\r\n  \"admission_date\": \"2019-08-24\",\r\n  \"contract_type\": \"string\",\r\n  \"cpf\": \"string\",\r\n  \"education\": \"string\",\r\n  \"ethnicity\": \"string\",\r\n  \"hierarchical_level\": \"string\",\r\n  \"marital_status\": \"string\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/users/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "users", "{id}"]}}, "response": []}, {"name": "Add permission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}, {"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"role\": \"543e2fa5-dae3-497f-aa96-e06da8fcb379\",\r\n  \"user\": \"76f62a58-5404-486d-9afc-07bded328704\",\r\n  \"workspace\": \"3f216741-15dd-4e46-b5ac-0077a2640e89\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/users/roles", "host": ["{{CORP_API}}"], "path": ["v1", "users", "roles"]}}, "response": []}, {"name": "Delete permission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}, {"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/users/roles/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "users", "roles", "{id}"]}}, "response": []}], "description": "Use to manage users in the application"}, {"name": "Workspace", "item": [{"name": "List workspaces", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}, {"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/workspaces", "host": ["{{CORP_API}}"], "path": ["v1", "workspaces"]}}, "response": []}, {"name": "List a workspace", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/workspaces/{{WORKSPACE_ID}}", "host": ["{{CORP_API}}"], "path": ["v1", "workspaces", "{{WORKSPACE_ID}}"]}}, "response": []}, {"name": "List workspace users", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "Content-Type", "value": "{{CONTENT_TYPE_JSON}}"}, {"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{CORP_API}}/v1/workspaces/{{WORKSPACE_ID}}/users", "host": ["{{CORP_API}}"], "path": ["v1", "workspaces", "{{WORKSPACE_ID}}", "users"]}}, "response": []}, {"name": "List a workspace user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}, {"key": "Authorization", "value": "{{AUTHORIZATION}}", "disabled": true}], "url": {"raw": "{{CORP_API}}/v1/workspaces/{{WORKSPACE_ID}}/users/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "workspaces", "{{WORKSPACE_ID}}", "users", "{id}"]}}, "response": []}, {"name": "Add workspace users", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}, {"key": "Authorization", "value": "{{AUTHORIZATION}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"permissions\": [\r\n    \"497f6eca-6276-4993-bfeb-53cbbbba6f08\"\r\n  ],\r\n  \"users\": [\r\n    {\r\n      \"name\": \"string\",\r\n      \"nickname\": \"string\",\r\n      \"email\": \"<EMAIL>\",\r\n      \"password\": \"string\",\r\n      \"secondary_email\": \"<EMAIL>\",\r\n      \"phone\": \"string\",\r\n      \"gender\": \"string\",\r\n      \"birthday\": \"2019-08-24\",\r\n      \"address\": \"string\",\r\n      \"country\": \"string\",\r\n      \"ein\": \"string\",\r\n      \"avatar\": \"http://example.com\",\r\n      \"status\": true,\r\n      \"job\": \"string\",\r\n      \"job_id\": \"453bd7d7-5355-4d6d-a38e-d9e7eb218c3f\",\r\n      \"director\": \"string\",\r\n      \"manager\": \"string\",\r\n      \"area_of_activity\": \"string\",\r\n      \"job_position\": \"631f5393-bdba-412a-8f90-2b05914b63df\",\r\n      \"job_function\": \"string\",\r\n      \"language_id\": \"e5383c4a-ba2f-4900-a483-8de374094cdf\",\r\n      \"related_user_leader_id\": \"be56247e-ce01-422f-a036-6effdf0ac4c1\",\r\n      \"email_verified\": true,\r\n      \"time_zone\": \"string\",\r\n      \"admission_date\": \"2019-08-24\",\r\n      \"contract_type\": \"string\",\r\n      \"cpf\": \"string\",\r\n      \"education\": \"string\",\r\n      \"ethnicity\": \"string\",\r\n      \"hierarchical_level\": \"string\",\r\n      \"marital_status\": \"string\"\r\n    }\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/workspaces/{{WORKSPACE_ID}}/users", "host": ["{{CORP_API}}"], "path": ["v1", "workspaces", "{{WORKSPACE_ID}}", "users"]}}, "response": []}, {"name": "Update workspace user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "PUT", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"address\": \"string\",\r\n  \"birthday\": \"2019-08-24\",\r\n  \"gender\": \"string\",\r\n  \"job\": \"string\",\r\n  \"name\": \"string\",\r\n  \"nickname\": \"string\",\r\n  \"phone\": \"string\",\r\n  \"secondary_email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/workspaces/{{WORKSPACE_ID}}/users/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "workspaces", "{{WORKSPACE_ID}}", "users", "{id}"]}}, "response": []}, {"name": "Partial update workspace user", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "PATCH", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"address\": \"string\",\r\n  \"birthday\": \"2019-08-24\",\r\n  \"gender\": \"string\",\r\n  \"job\": \"string\",\r\n  \"name\": \"string\",\r\n  \"nickname\": \"string\",\r\n  \"phone\": \"string\",\r\n  \"secondary_email\": \"<EMAIL>\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_APIi}}/v1/workspaces/{{WORKSPACE_ID}}/users/{id}", "host": ["{{CORP_APIi}}"], "path": ["v1", "workspaces", "{{WORKSPACE_ID}}", "users", "{id}"]}}, "response": []}], "description": "Use to manage the workspace"}, {"name": "Mission", "item": [{"name": "List missions", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/missions", "host": ["{{CORP_API}}"], "path": ["v1", "missions"]}}, "response": []}, {"name": "List a mission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/missions/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "missions", "{id}"]}}, "response": []}], "description": "Use to list the missions"}, {"name": "Trail", "item": [{"name": "List trails", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/trails/", "host": ["{{CORP_API}}"], "path": ["v1", "trails", ""]}}, "response": []}, {"name": "List a trail", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{CORP_API}}/v1/trails/{d}", "host": ["{{CORP_API}}"], "path": ["v1", "trails", "{d}"]}}, "response": []}], "description": "Use to list the trails"}, {"name": "Mission-enrollments", "item": [{"name": "List missions enrollments", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/mission-enrollments", "host": ["{{CORP_API}}"], "path": ["v1", "mission-enrollments"]}}, "response": []}, {"name": "Enroll in a mission", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"users\": [\r\n    \"string\"\r\n  ],\r\n  \"goal_date\": \"2019-08-24T14:15:22Z\",\r\n  \"required\": true,\r\n  \"missions\": [\r\n    \"string\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/mission-enrollments", "host": ["{{CORP_API}}"], "path": ["v1", "mission-enrollments"]}}, "response": []}, {"name": "List a mission enrollment", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{CORP_API}}/v1/mission-enrollments/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "mission-enrollments", "{id}"]}}, "response": []}], "description": "Use to enroll users and list enrollments"}, {"name": "Trail-enrollments", "item": [{"name": "List trails enrollments", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/learning-trail-enrollments", "host": ["{{CORP_API}}"], "path": ["v1", "learning-trail-enrollments"]}}, "response": []}, {"name": "Enroll in a traill", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"users\": [\r\n    \"497f6eca-6276-4993-bfeb-53cbbbba6f08\"\r\n  ],\r\n  \"goal_date\": \"2019-08-24T14:15:22Z\",\r\n  \"required\": true,\r\n  \"learning_trails\": [\r\n    \"497f6eca-6276-4993-bfeb-53cbbbba6f08\"\r\n  ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/learning-trail-enrollments", "host": ["{{CORP_API}}"], "path": ["v1", "learning-trail-enrollments"]}}, "response": []}, {"name": "List a trail enrollment", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/learning-trail-enrollments/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "learning-trail-enrollments", "{id}"]}}, "response": []}]}, {"name": "Groups", "item": [{"name": "List groups", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/groups", "host": ["{{CORP_API}}"], "path": ["v1", "groups"]}}, "response": []}, {"name": "Link users to group", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "POST", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "body": {"mode": "raw", "raw": "{\r\n  \"users\": [\r\n    \"497f6eca-6276-4993-bfeb-53cbbbba6f08\"\r\n  ],\r\n  \"enrollment_goal_date\": \"2019-08-24T14:15:22Z\",\r\n  \"enrollment_required_mission\": true,\r\n  \"regulatory_compliance_cycle_id\": \"db952ce8-0520-4d69-a218-a1390ed05ede\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{CORP_API}}/v1/groups/{id}/users", "host": ["{{CORP_API}}"], "path": ["v1", "groups", "{id}", "users"]}}, "response": []}], "description": "Use to manage the groups"}, {"name": "Pulse", "item": [{"name": "List pulses", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/pulses/", "host": ["{{CORP_API}}"], "path": ["v1", "pulses", ""]}}, "response": []}, {"name": "List a pulse", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{ACCESS_TOKEN}}", "type": "string"}]}, "method": "GET", "header": [{"key": "x-client", "value": "{{X_CLIENT}}"}], "url": {"raw": "{{CORP_API}}/v1/pulses/{id}", "host": ["{{CORP_API}}"], "path": ["v1", "pulses", "{id}"]}}, "response": []}], "description": "Use to list the pulses"}]}