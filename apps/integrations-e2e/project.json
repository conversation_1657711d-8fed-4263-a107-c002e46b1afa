{"name": "integrations-e2e", "$schema": "../../node_modules/nx/schemas/project-schema.json", "projectType": "application", "implicitDependencies": ["integration-gateway-alura"], "targets": {"e2e": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{e2eProjectRoot}"], "options": {"jestConfig": "apps/integrations-e2e/jest.config.ts", "passWithNoTests": true}}, "lint": {"executor": "@nx/eslint:lint"}}}