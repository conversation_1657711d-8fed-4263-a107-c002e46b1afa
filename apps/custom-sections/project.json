{"name": "custom-sections", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/custom-sections/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/custom-sections", "main": "apps/custom-sections/src/main.ts", "tsConfig": "apps/custom-sections/tsconfig.app.json", "assets": ["apps/custom-sections/src/assets"], "webpackConfig": "apps/custom-sections/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "custom-sections:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "custom-sections:build:development"}, "production": {"buildTarget": "custom-sections:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/custom-sections/jest.config.ts"}}}}