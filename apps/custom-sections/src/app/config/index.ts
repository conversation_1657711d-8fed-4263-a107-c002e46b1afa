import { ConfigModuleOptions } from '@nestjs/config';
import configSchema from './config.schema';
import appConfig from './modules/app.config';
import authConfig from './modules/auth.config';
import databaseConfig from './modules/database.config';

export { default as AppConfig } from './modules/app.config';
export { default as DatabaseConfig } from './modules/database.config';
export { default as AuthConfig } from './modules/auth.config';
export { default as ConfigSchema } from './config.schema';

export const configOptions: ConfigModuleOptions = {
  isGlobal: true,
  cache: true,
  validationOptions: {
    allowUnknown: true,
    abortEarly: true,
  },
  validationSchema: configSchema,
  load: [appConfig, databaseConfig, authConfig],
};
