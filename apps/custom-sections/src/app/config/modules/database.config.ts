import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { entitiesAndMigrations } from '../../db/entities-and-migrations';

export default registerAs<TypeOrmModuleOptions>('database', () => ({
  type: 'postgres',
  username: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  autoLoadEntities: true,
  synchronize: false,
  migrations: entitiesAndMigrations.migrations,
  logging: process.env.DB_DEBUG === 'true',
  logger: 'advanced-console',
}));
