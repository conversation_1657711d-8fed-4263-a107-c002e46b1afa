import Joi from 'joi';

export default Joi.object({
  NODE_ENV: Joi.string().valid('development', 'stage', 'production').required(),
  PORT: Joi.number().default(3000),

  // database
  DB_USER: Joi.string().default('postgres'),
  DB_PASS: Joi.string().default('postgres'),
  DB_NAME: Joi.string().valid('custom-sections_db', 'custom-sections_dev_db').required(),
  DB_HOST: Joi.string().default('localhost'),
  DB_PORT: Joi.number().default(5432),
  DB_DEBUG: Joi.boolean().default(false),
  DB_MIGRATIONS_RUN: Joi.boolean().default(false),

  // auth
  AUTH_URL: Joi.string().default('https://iam.keepsdev.com/auth/'),
  AUTH_REALM: Joi.string().valid('keeps-dev', 'keeps').required(),
  AUTH_CLIENT_ID: Joi.string().required(),
  AUTH_CLIENT_SECRET: Joi.string().required(),
  AUTH_REALM_PUBLIC_KEY: Joi.string().required(),
  AUTH_DEBUG: Joi.boolean().default(false),
});
