import { DataSourceOptions } from 'typeorm';

/***
 * @See https://spin.atomicobject.com/typeorm-webpack/
 *
 */
type EntitiesAndMigrationsOpts = Pick<DataSourceOptions, 'entities' | 'migrations'>;

const importAllFunctions = (requireContext: any) =>
  requireContext
    .keys()
    .sort()
    .map((filename: string) => {
      const required = requireContext(filename);
      return Object.keys(required).reduce((result, exportedKey) => {
        const exported = required[exportedKey];
        if (typeof exported === 'function') {
          return result.concat(exported);
        }
        return result;
      }, [] as any);
    })
    .flat();

const entitiesViaWebpack: NonNullable<EntitiesAndMigrationsOpts['entities']> = importAllFunctions(
  (require as any).context('../entities/', true, /\.entity.ts$/),
);
const migrationsViaWebpack: NonNullable<EntitiesAndMigrationsOpts['migrations']> = importAllFunctions(
  (require as any).context('../db/migrations/', true, /\.ts$/),
);

export const entitiesAndMigrations: EntitiesAndMigrationsOpts = {
  entities: entitiesViaWebpack,
  migrations: migrationsViaWebpack,
};
