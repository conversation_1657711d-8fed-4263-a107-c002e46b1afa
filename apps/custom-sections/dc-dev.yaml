version: '3.8'

services:
  api:
    image: custom-sections-mircroservice:latest
    container_name: custom-sections-microservice
    build:
      context: .
      dockerfile: .docker/dev/Dockerfile
    ports:
      - '3000:3000'
    env_file:
      - ./.env
    volumes:
      - type: bind
        source: ../../
        target: /usr/src/app
    command: ['./apps/custom-sections/.docker/dev/start.sh']
    depends_on:
      - db
    networks:
      - keeps-network

  db:
    image: postgres:latest
    container_name: keeps-postgres
    env_file:
      - ./.env
    ports:
      - '5432:5432'
    volumes:
      - type: volume
        source: pgdata
        target: /var/lib/postgresql/data
    networks:
      - keeps-network
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: custom-sections_dev_db

networks:
  keeps-network:
    driver: bridge

volumes:
  pgdata:
    driver: local
