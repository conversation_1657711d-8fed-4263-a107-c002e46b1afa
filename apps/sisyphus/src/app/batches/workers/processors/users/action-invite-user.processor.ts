import { Injectable } from '@nestjs/common';
import { ActionRecordProcessor } from '../../../types/action-record-processor.interface';
import { ActionRecord } from '../../../../entities/action-record.entity';
import { Batch } from '../../../../entities/batch.entity';
import { InvitationsRepository } from '../../../../@core/myaccount/interfaces/invitations.repository.abstract';

@Injectable()
export class ActionInviteUserProcessor implements ActionRecordProcessor {
  constructor(private readonly invitationsRepository: InvitationsRepository) {}

  async process(actionRecord: ActionRecord, batch: Batch) {
    this.invitationsRepository.resend(actionRecord.objectId, batch.workspaceId);
  }
}
