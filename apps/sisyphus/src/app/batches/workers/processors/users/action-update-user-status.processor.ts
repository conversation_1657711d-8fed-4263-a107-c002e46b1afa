import { Injectable } from '@nestjs/common';
import { ActionRecordProcessor } from '../../../types/action-record-processor.interface';
import { ActionRecord } from '../../../../entities/action-record.entity';
import { Batch } from '../../../../entities/batch.entity';
import { UsersRepository } from '../../../../@core/myaccount/interfaces/users.repository.interface';

@Injectable()
export class ActionUpdateUserStatusProcessor implements ActionRecordProcessor {
  constructor(private readonly usersClientService: UsersRepository) {}

  async process(actionRecord: ActionRecord, batch: Batch) {
    await this.usersClientService.updateUserStatus(
      actionRecord.objectId,
      batch.actionPayload.status,
      batch.authToken,
      batch.workspaceId,
    );
  }
}
