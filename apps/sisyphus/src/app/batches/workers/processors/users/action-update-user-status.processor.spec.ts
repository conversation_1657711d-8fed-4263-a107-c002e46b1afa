import { UsersRepository } from '../../../../@core/myaccount/interfaces/users.repository.interface';
import { ActionUpdateUserStatusProcessor } from './action-update-user-status.processor';
import { ActionRecord } from '../../../../entities/action-record.entity';
import { Batch } from '../../../../entities/batch.entity';
import { Chance } from 'chance';

describe('ActionUpdateUserStatusProcessor', () => {
  let processor: ActionUpdateUserStatusProcessor;
  let usersRepositoryMock: jest.Mocked<UsersRepository>;
  const chance = new Chance();
  const objectId = chance.guid();
  const workspaceId = chance.guid();
  const userStatus = chance.bool();

  beforeEach(async () => {
    usersRepositoryMock = {
      getUsers: jest.fn(),
      updateUserStatus: jest.fn(),
    };

    processor = new ActionUpdateUserStatusProcessor(usersRepositoryMock);
  });

  it('should call updateUserStatus with correct parameters', async () => {
    const mockActionRecord: ActionRecord = { objectId: objectId } as ActionRecord;
    const mockBatch: Batch = {
      workspaceId: workspaceId,
      actionPayload: { status: userStatus },
      authToken: chance.hash(),
    } as Batch;

    await processor.process(mockActionRecord, mockBatch);

    expect(usersRepositoryMock.updateUserStatus).toHaveBeenCalledTimes(1);
    expect(usersRepositoryMock.updateUserStatus).toHaveBeenCalledWith(
      objectId,
      userStatus,
      mockBatch.authToken,
      workspaceId,
    );
  });
});
