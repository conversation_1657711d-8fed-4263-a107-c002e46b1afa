import { ActionInviteUserProcessor } from './action-invite-user.processor';
import { ActionRecord } from '../../../../entities/action-record.entity';
import { Batch } from '../../../../entities/batch.entity';
import { Chance } from 'chance';
import { InvitationsRepository } from '../../../../@core/myaccount/interfaces/invitations.repository.abstract';

describe('ActionInviteUserProcessor', () => {
  let processor: ActionInviteUserProcessor;
  let invitationsRepositoryMock: jest.Mocked<InvitationsRepository>;
  const chance = new Chance();
  const objectId = chance.guid();
  const workspaceId = chance.guid();

  beforeEach(async () => {
    invitationsRepositoryMock = {
      resend: jest.fn(),
    };

    processor = new ActionInviteUserProcessor(invitationsRepositoryMock);
  });

  it('should call inviteUser with correct parameters', async () => {
    const mockActionRecord: ActionRecord = { objectId: objectId } as ActionRecord;
    const mockBatch: Batch = { workspaceId: workspaceId, authToken: chance.hash() } as Batch;

    await processor.process(mockActionRecord, mockBatch);

    expect(invitationsRepositoryMock.resend).toHaveBeenCalledTimes(1);
    expect(invitationsRepositoryMock.resend).toHaveBeenCalledWith(objectId, workspaceId);
  });
});
