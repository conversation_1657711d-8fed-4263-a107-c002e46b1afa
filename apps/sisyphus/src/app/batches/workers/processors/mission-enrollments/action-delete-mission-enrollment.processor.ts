import { Injectable } from '@nestjs/common';
import { ActionRecordProcessor } from '../../../types/action-record-processor.interface';
import { ActionRecord } from '../../../../entities/action-record.entity';
import { Batch } from '../../../../entities/batch.entity';
import { lastValueFrom } from 'rxjs';
import { KonquestMissionEnrollmentsService } from '@keeps-node-apis/konquest';

@Injectable()
export class ActionDeleteMissionEnrollmentProcessor implements ActionRecordProcessor {
  constructor(private readonly missionEnrollmentsService: KonquestMissionEnrollmentsService) {}

  async process(actionRecord: ActionRecord, batch: Batch) {
    await lastValueFrom(
      this.missionEnrollmentsService.deleteMissionEnrollment(actionRecord.objectId, {
        query: {},
        token: batch.authToken,
        workspace_id: batch.workspaceId,
      }),
    );
  }
}
