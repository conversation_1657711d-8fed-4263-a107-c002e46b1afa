import { Injectable } from '@nestjs/common';
import { ActionRecordProcessor } from '../../../types/action-record-processor.interface';
import { ActionRecord } from '../../../../entities/action-record.entity';
import { Batch } from '../../../../entities/batch.entity';
import { lastValueFrom } from 'rxjs';
import { KonquestMissionEnrollmentsService } from '@keeps-node-apis/konquest';

@Injectable()
export class ActionUpdateGoalDateMissionEnrollmentProcessor implements ActionRecordProcessor {
  constructor(private readonly missionEnrollmentsService: KonquestMissionEnrollmentsService) {}

  async process(actionRecord: ActionRecord, batch: Batch) {
    await lastValueFrom(
      this.missionEnrollmentsService.extendDeadlineMissionEnrollment(actionRecord.objectId, batch.actionPayload, {
        query: {},
        token: batch.authToken,
        workspace_id: batch.workspaceId,
      }),
    );
  }
}
