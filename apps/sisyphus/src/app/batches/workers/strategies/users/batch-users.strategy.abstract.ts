import { BatchStrategy } from '../batch-strategy.abstract';
import { Batch } from '../../../../entities/batch.entity';
import { UsersRepository } from '../../../../@core/myaccount/interfaces/users.repository.interface';
import { User } from '../../../../@core/myaccount/interfaces/user.interface';

export abstract class BatchUsersStrategy extends BatchStrategy<User> {
  // todo: Refactor Batch<PERSON>trategies to seperate the Filter (isObjectAvailable) and Retriever (retriveObjects)
  constructor(private readonly usersRepository: UsersRepository) {
    super();
  }

  async retrieveObjects(batch: Batch, queryFilters: Record<string, any>): Promise<User[]> {
    const formattedFilters = Object.entries(queryFilters).reduce((acc, [key, value]) => {
      value = this.formatFilterValues(value);
      key = key.replace('filter.', '');
      acc[key] = { values: [value] };
      return acc;
    }, {});

    return this.usersRepository.getUsers(formattedFilters, batch.workspaceId);
  }

  formatFilterValues(value: string | Array<string>): string {
    if (Array.isArray(value)) {
      return `$in:${value.join(',')}`;
    }
    return value;
  }

  abstract isObjectAvailable(actionObject: User): boolean;
}
