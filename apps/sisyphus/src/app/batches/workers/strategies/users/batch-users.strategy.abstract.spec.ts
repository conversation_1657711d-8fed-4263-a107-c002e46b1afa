import { UsersRepository } from '../../../../@core/myaccount/interfaces/users.repository.interface';
import { Batch } from '../../../../entities/batch.entity';
import { User } from '../../../../@core/myaccount/interfaces/user.interface';
import { BatchUsersStrategy } from './batch-users.strategy.abstract';

class TestBatchUsersStrategy extends BatchUsersStrategy {
  isObjectAvailable(user: User): boolean {
    return true;
  }
}

describe('BatchUsersStrategy', () => {
  let strategy: BatchUsersStrategy;
  let usersRepository: jest.Mocked<UsersRepository>;

  beforeEach(() => {
    usersRepository = {
      getUsers: jest.fn(),
    } as unknown as jest.Mocked<UsersRepository>;

    strategy = new TestBatchUsersStrategy(usersRepository);
  });

  describe('formatFilterValues', () => {
    it('should format array values into $in string', () => {
      const result = strategy.formatFilterValues(['one', 'two']);
      expect(result).toBe('$in:one,two');
    });

    it('should return the string if not an array', () => {
      const result = strategy.formatFilterValues('value');
      expect(result).toBe('value');
    });
  });

  describe('retrieveObjects', () => {
    it('should format filters and call usersRepository.getUsers', async () => {
      const batch: Batch = { workspaceId: 'workspace1' } as Batch;
      const filters = {
        'filter.status': 'active',
        'filter.roles': ['admin', 'user'],
      };

      const expectedFormattedFilters = {
        status: { values: ['active'] },
        roles: { values: ['$in:admin,user'] },
      };

      const mockUsers: User[] = [{ id: 'u1' }] as User[];
      usersRepository.getUsers.mockResolvedValue(mockUsers);

      const result = await strategy.retrieveObjects(batch, filters);

      expect(usersRepository.getUsers).toHaveBeenCalledWith(expectedFormattedFilters, batch.workspaceId);
      expect(result).toEqual(mockUsers);
    });
  });
});
