import { BatchInviteUsersStrategy } from '../users/batch-invite-users.strategy';
import { UsersRepository } from '../../../../@core/myaccount/interfaces/users.repository.interface';
import { Batch } from '../../../../entities/batch.entity';
import { Chance } from 'chance';
import { User } from '../../../../@core/myaccount/interfaces/user.interface';

describe('BatchInviteUsersStrategy', () => {
  let batchInviteUsersStrategy: BatchInviteUsersStrategy;
  let usersRepositoryMock: jest.Mocked<UsersRepository>;
  const chance = new Chance();
  const workspaceId = chance.guid();
  const queryFilters = { role: 'user' };
  const user: User = {
    id: chance.guid(),
    name: chance.name(),
    status: false,
    email: chance.email(),
  };

  beforeEach(() => {
    usersRepositoryMock = {
      getUsers: jest.fn(),
      updateUserStatus: jest.fn(),
    };

    batchInviteUsersStrategy = new BatchInviteUsersStrategy(usersRepositoryMock);
  });

  it('should return false if user status is false', () => {
    const inactiveUser: User = { ...user, status: false };

    expect(batchInviteUsersStrategy.isObjectAvailable(inactiveUser)).toBe(false);
  });

  it('should return true if user status is true', () => {
    const activeUser: User = { ...user, status: true };

    expect(batchInviteUsersStrategy.isObjectAvailable(activeUser)).toBe(true);
  });

  it('should call getUsers with correct parameters when retrieving objects', async () => {
    const mockBatch: Batch = { workspaceId } as Batch;
    const mockUsers: User[] = [user];

    usersRepositoryMock.getUsers.mockResolvedValue(mockUsers);

    const result = await batchInviteUsersStrategy.retrieveObjects(mockBatch, queryFilters);

    expect(usersRepositoryMock.getUsers).toHaveBeenCalledTimes(1);
    expect(usersRepositoryMock.getUsers).toHaveBeenCalledWith({ role: { values: ['user'] } }, workspaceId);
    expect(result).toEqual(mockUsers);
  });
});
