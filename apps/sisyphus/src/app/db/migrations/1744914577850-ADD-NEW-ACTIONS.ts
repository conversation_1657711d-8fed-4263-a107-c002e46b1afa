import { MigrationInterface, QueryRunner } from "typeorm";

export class ADDNEWACTIONS1744914577850 implements MigrationInterface {
    name = 'ADDNEWACTIONS1744914577850'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TYPE "public"."batch_actions" RENAME TO "batch_actions_old"`);
        await queryRunner.query(`CREATE TYPE "public"."batch_actions" AS ENUM('KONQUEST.FINISH_MSSION_ENROLLMENTS', 'KONQUEST.RESTART_MISSION_ENROLLMENTS', 'KONQUEST.RECREATE_MISSION_ENROLLMENTS', 'KONQUEST.DELETE_MISSION_ENROLLMENTS', 'KONQUEST.APPROVE_MISSION_ENROLLMENTS', 'KONQUEST.UPDATE_MISSION_ENROLLMENTS_GOAL_DATE', 'MYACCOUNT.INVITE_USERS', 'MYACCOUNT.UPDATE_USERS_STATUS')`);
        await queryRunner.query(`ALTER TABLE "batch" ALTER COLUMN "action_key" TYPE "public"."batch_actions" USING "action_key"::"text"::"public"."batch_actions"`);
        await queryRunner.query(`DROP TYPE "public"."batch_actions_old"`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."batch_actions_old" AS ENUM('KONQUEST.FINISH_MSSION_ENROLLMENTS', 'KONQUEST.RESTART_MISSION_ENROLLMENTS', 'KONQUEST.RECREATE_MISSION_ENROLLMENTS', 'KONQUEST.DELETE_MISSION_ENROLLMENTS', 'KONQUEST.APPROVE_MISSION_ENROLLMENTS', 'KONQUEST.UPDATE_MISSION_ENROLLMENTS_GOAL_DATE')`);
        await queryRunner.query(`ALTER TABLE "batch" ALTER COLUMN "action_key" TYPE "public"."batch_actions_old" USING "action_key"::"text"::"public"."batch_actions_old"`);
        await queryRunner.query(`DROP TYPE "public"."batch_actions"`);
        await queryRunner.query(`ALTER TYPE "public"."batch_actions_old" RENAME TO "batch_actions"`);
    }

}
