import { ClientGrpc } from '@nestjs/microservices';
import { Inject, Injectable } from '@nestjs/common';
import { MetadataFactory } from './metadata.factory';
import { firstValueFrom, Observable } from 'rxjs';
import { USER_PACKAGE } from '../../constants';
import { InvitationsRepository } from '../interfaces/invitations.repository.abstract';

interface InvitationServiceClient {
  resendInvitation(data: { userId: string }, metadata: any): Observable<{ status: string }>;
}

@Injectable()
export class InvitationsGRPCRepository implements InvitationsRepository {
  constructor(@Inject(USER_PACKAGE) private readonly client: ClientGrpc) {}

  private invitationService: InvitationServiceClient;

  onModuleInit() {
    this.invitationService = this.client.getService<InvitationServiceClient>('InvitationService');
  }

  async resend(userId: string, workspaceId: string) {
    const metadata = MetadataFactory.create(workspaceId);
    await firstValueFrom(this.invitationService.resendInvitation({ userId }, metadata));
  }
}
