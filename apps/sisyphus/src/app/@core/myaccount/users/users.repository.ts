import { Injectable, Inject } from '@nestjs/common';
import { ClientGrpc } from '@nestjs/microservices';
import { firstValueFrom, Observable } from 'rxjs';
import { USER_PACKAGE } from '../../constants';
import { UsersRepository } from '../interfaces/users.repository.interface';
import { UserListResponse, UserRequest } from './users-grpc.interfacte';
import { User } from '../interfaces/user.interface';
import { MetadataFactory } from './metadata.factory';
import { MyAccountService } from '@keeps-node-apis/@core';

interface UserService {
  Get(data: UserRequest, metadata: any): Observable<UserListResponse>;
}

@Injectable()
export class UsersGrpcRepository implements UsersRepository {
  private usersService: UserService;

  constructor(
    @Inject(USER_PACKAGE) private readonly client: ClientGrpc,
    private readonly myAccountService: MyAccountService,
  ) {}

  onModuleInit() {
    this.usersService = this.client.getService<UserService>('UserService');
  }

  async getUsers(filters: Record<string, any>, workspaceId: string): Promise<User[]> {
    const metadata = MetadataFactory.create(workspaceId);
    const response = await firstValueFrom(this.usersService.Get({ filters: filters }, metadata));
    return response?.items || [];
  }

  async updateUserStatus(
    userId: string,
    status: boolean,
    requestUserToken: string,
    workspaceId: string,
  ): Promise<void> {
    await this.myAccountService.updateUserStatus(
      userId,
      { status },
      { xClient: workspaceId, userToken: requestUserToken },
    );
  }
}
