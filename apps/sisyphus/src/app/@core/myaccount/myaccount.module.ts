import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MYACCOUNT_GRPC_SERVER_URL_CONFIG, USER_PACKAGE, USER_PROTO_PACKAGE } from '../constants';
import { UsersGrpcRepository } from './users/users.repository';
import { MyAccountService, RestModule } from '@keeps-node-apis/@core';
import { HttpModule } from '@nestjs/axios';
import { join } from 'path';
import { InvitationsRepository } from './interfaces/invitations.repository.abstract';
import { InvitationsGRPCRepository } from './users/invitations.repository';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: USER_PACKAGE,
        imports: [ConfigModule],
        inject: [ConfigService],
        useFactory: (configService: ConfigService) => ({
          transport: Transport.GRPC,
          options: {
            url: configService.get<string>(MYACCOUNT_GRPC_SERVER_URL_CONFIG),
            package: USER_PROTO_PACKAGE,
            protoPath: join(__dirname, './assets/protos/users.proto'),
            keepCase: false,
            longs: String,
            enums: String,
            defaults: true,
            oneofs: true,
          },
        }),
      },
    ]),
    HttpModule,
    RestModule,
  ],
  providers: [
    UsersGrpcRepository,
    MyAccountService,
    { provide: InvitationsRepository, useClass: InvitationsGRPCRepository },
  ],
  exports: [UsersGrpcRepository, MyAccountService, InvitationsRepository],
})
export class MyaccountModule {}
