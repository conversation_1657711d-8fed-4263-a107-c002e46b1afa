syntax = "proto3";

package users;

service UserService {
  rpc Get(GetUsersRequest) returns (UserListResponse);
}

service InvitationService {
  rpc ResendInvitation(ResendInvitationRequest) returns (ResendInvitationResponse);
}

message GetUsersRequest {
  int32 page = 1;
  int32 limit = 2;
  string search = 3;
  repeated string search_by = 4;
  repeated string select = 5;
  repeated string sort_by = 6;
  map<string, FilterValues> filters = 7;
}

message FilterValues {
  repeated string values = 1;
}

message UserListResponse {
  repeated User items = 1;
  int32 total = 2;
  int32 page = 3;
  int32 limit = 4;
}

message User {
  string id = 1;
  string email = 2;
  string name = 3;
  bool status = 4;
}

message ResendInvitationRequest {
  string userId = 1;
}

message ResendInvitationResponse {
  string status = 1;
}
