### Get User Authorization Token to SSO
POST http://localhost:3000/api/sso/authorization-token
Content-Type: application/json

{
	"client":"4adfa8f52b634156a94d143bee5130fa",
	"email": "<EMAIL>"
}

### Available Integrations
GET http://localhost:3000/api/integrations
Content-Type: application/json

{
	"token":"28f49c923329495b84e1ad2d3e48e38d"
}

### Validate Integration Token
POST http://localhost:3000/api/integrations/courses/validate
Content-Type: application/json

{
	"token":"c8996aa897594fae93f0998d3154ca66"
}

### Courses Catalog
POST http://localhost:3000/api/courses
Content-Type: application/json

{
	"token":"c8996aa897594fae93f0998d3154ca66"
}

### Courses Details
POST http://localhost:3000/api/courses/details
Content-Type: application/json

{
	"token":"5a5c234ef5734e18bfc5e3cf9edcfa2a",
	"startAt": "2024-01-01",
	"completedAt": "2024-02-01"
}

### Courses Completed
POST http://localhost:3000/api/courses/completed
Content-Type: application/json

{
	"token":"16a575253691414d8b11549b21c2d548",
	"email": "<EMAIL>"
}

### Courses Progress
POST http://localhost:3000/api/courses/progress
Content-Type: application/json

{
	"token":"f48ac309d3114a96b18865923fa2457c",
	"email": "<EMAIL>"
}

### Courses Engagement
POST http://localhost:3000/api/courses/engagement
Content-Type: application/json

{
	"token":"28f49c923329495b84e1ad2d3e48e38d"
}