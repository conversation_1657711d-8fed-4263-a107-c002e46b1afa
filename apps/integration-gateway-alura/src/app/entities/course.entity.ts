import { Column, DeleteDateColumn, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('courses')
export class Course {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column('text', { name: 'course_id' })
  courseId: string;

  @Column('text', { name: 'name' })
  name: string;

  @Column('uuid', { name: 'integration_id' })
  integrationId: string;

  @Column('text', { name: 'category' })
  category: string;

  @Column({ type: 'date', name: 'created_date', nullable: false })
  created_date: Date;

  @Column('enum', {
    name: 'status',
    enumName: 'enum_course_status',
    enum: Object.keys({ DISABLED: 'DISABLED', PUBLISHED: 'PUBLISHED' }),
    default: 'PUBLISHED',
    nullable: false,
  })
  status: string;

  @Column('text', { name: 'course_url' })
  courseUrl: string;

  @Column('text', { name: 'image_url' })
  imageUrl: string;

  @Column('text', { name: 'description' })
  description: string;

  @Column('integer', { name: 'workload' })
  workload: number;

  @DeleteDateColumn({ type: 'timestamptz', name: 'deleted_at', nullable: true })
  deletedAt: Date;
}
