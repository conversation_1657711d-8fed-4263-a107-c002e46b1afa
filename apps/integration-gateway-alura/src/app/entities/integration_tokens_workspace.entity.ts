import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { TokenWorkspaceStatusEnum } from '../common/models/integration-token-status.enum';
import { TokenTransformer } from '../common/utils/token-transformer';
import { Workspace } from './workspace.entity';

export enum AluraTokenName {
  SSO = 'sso',
  FINISHED_ENROLLMENTS = 'finished_enrollments',
  COURSES = 'courses',
  ENROLLMENT_PROGRESS = 'enrollment_progress',
}

@Entity('integration_token_workspace')
@Unique(['workspaceId', 'tokenName'])
@Index('idx_workspace_id', ['workspaceId'])
@Index('idx_token_name', ['tokenName'])
export class IntegrationTokensWorkspace {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', name: 'workspace_id' })
  workspaceId: string;

  @Column({
    type: 'enum',
    enum: AluraTokenName,
    name: 'token_name',
    nullable: false,
  })
  tokenName: AluraTokenName;

  @Column('timestamp', { name: 'last_checked_at' })
  lastCheckedAt: Date;

  @Column({
    type: 'enum',
    enum: TokenWorkspaceStatusEnum,
    default: TokenWorkspaceStatusEnum.ACTIVE,
    name: 'status',
  })
  status: TokenWorkspaceStatusEnum;

  @Column('text', { name: 'token', transformer: new TokenTransformer() })
  token: string;

  @ManyToOne(() => Workspace)
  @JoinColumn({ name: 'workspace_id' })
  workspace: string;
}
