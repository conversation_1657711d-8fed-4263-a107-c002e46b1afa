import { PageDto, PageOptionsDto, Sorting } from '@keeps-node-apis/@core';
import { MirroredCourseDto } from '../../dtos/mirrored-course.dto';
import { CreateMirroredCourseDto } from '../../dtos/mirrored-courses-create.dto';
import { UpdateMirroredCourseDto } from '../../dtos/mirrored-courses-update.dto';
import { MirroredCourses } from '../../entities/mirrored_courses.entity';
import { MirroredCourseFilterDto } from '../../dtos/mirrored-courses-filter.dto';

export abstract class MirroredCoursesRepository {
  abstract findAll(
    filters: MirroredCourseFilterDto,
    pageOptions: PageOptionsDto,
    sort: Sorting,
    workspaceId: string,
  ): Promise<PageDto<MirroredCourseDto>>;

  abstract findOne(id: string, workspaceId: string): Promise<MirroredCourses | undefined>;

  abstract findOneByMissionId(missionId: string, workspaceId: string): Promise<MirroredCourses | undefined>;

  abstract save(createCourseDto: CreateMirroredCourseDto): Promise<MirroredCourses>;

  abstract update(courseId: string, updateCourseDto: UpdateMirroredCourseDto): Promise<number>;

  abstract findBy(where: Partial<MirroredCourses>): Promise<MirroredCourses[]>;

  abstract getCategories(workspaceId: string): Promise<{ category: string }[]>;
}
