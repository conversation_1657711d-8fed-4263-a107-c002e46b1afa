import { Injectable } from '@nestjs/common';
import { DataSource, Repository } from 'typeorm';
import { IntegrationTokensWorkspace } from '../../entities/integration_tokens_workspace.entity';

@Injectable()
export class IntegrationTokenWorkspaceRepository extends Repository<IntegrationTokensWorkspace> {
  private dataSource: DataSource;
  constructor(dataSource: DataSource) {
    super(IntegrationTokensWorkspace, dataSource.createEntityManager());
    this.dataSource = dataSource;
  }

  findByWorkspaceAndIntegration(workspaceId?: string, integrationId?: string): Promise<IntegrationTokensWorkspace[]> {
    return this.dataSource
      .createQueryBuilder(IntegrationTokensWorkspace, 'itw')
      .leftJoin('integration_token', 'it', 'it.id = itw.integrationToken AND it.integrationId = :integrationId', {
        integrationId,
      })
      .where('itw.workspaceId = :workspaceId', { workspaceId })
      .getMany();
  }

  findTokenByWorkspaceId(workspaceId: string): Promise<IntegrationTokensWorkspace> {
    return this.dataSource
      .createQueryBuilder(IntegrationTokensWorkspace, 'itw')
      .leftJoin('integration_token', 'it', 'it.id = itw.integrationToken')
      .where('itw.workspaceId = :workspaceId', { workspaceId })
      .getOne();
  }
}
