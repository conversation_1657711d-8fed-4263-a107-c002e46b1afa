import { PageDto, PageOptionsDto, paginate, Sorting } from '@keeps-node-apis/@core';
import { Injectable } from '@nestjs/common';
import { plainToInstance } from 'class-transformer';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { MirroredCourseDto } from '../../dtos/mirrored-course.dto';
import { CreateMirroredCourseDto } from '../../dtos/mirrored-courses-create.dto';
import { UpdateMirroredCourseDto } from '../../dtos/mirrored-courses-update.dto';
import { MirroredCourses } from '../../entities/mirrored_courses.entity';
import { MirroredCoursesRepository } from './mirrored-courses-repository.interface';
import { MirroredCourseFilterDto } from '../../dtos/mirrored-courses-filter.dto';

@Injectable()
export default class TypeOrmMirroredCourseRepository implements MirroredCoursesRepository {
  private readonly repository: Repository<MirroredCourses>;

  constructor(private dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(MirroredCourses);
  }

  async findAll(
    filters: MirroredCourseFilterDto,
    pageOptions: PageOptionsDto,
    sort: Sorting,
    workspaceId: string,
  ): Promise<PageDto<MirroredCourseDto>> {
    const queryBuilder = this.dataSource
      .createQueryBuilder(MirroredCourses, 'mirrored_courses')
      .leftJoinAndSelect('mirrored_courses.course', 'course')
      .leftJoinAndSelect('mirrored_courses.mission', 'mission')
      .where('mirrored_courses.deletedAt IS NULL')
      .andWhere('mirrored_courses.workspace_id = :workspaceId', { workspaceId });

    if (filters.created_date_lte) {
      queryBuilder.andWhere('mirrored_courses.created_date <= :created_date_lte', {
        created_date_lte: filters.created_date_lte,
      });
    }

    if (filters.created_date_gte) {
      queryBuilder.andWhere('mirrored_courses.created_date >= :created_date_gte', {
        created_date_gte: filters.created_date_gte,
      });
    }

    if (filters.integrationId) {
      queryBuilder.andWhere('course.integrationId = :integrationId', {
        integrationId: filters.integrationId,
      });
    }

    if (filters.category?.length) {
      queryBuilder.andWhere('course.category IN (:...categories)', { categories: filters.category });
    }

    if (filters.status?.length) {
      queryBuilder.andWhere('course.status IN (:...courseStatus)', { courseStatus: filters.status });
    }

    if (filters.name) {
      queryBuilder.andWhere('course.name ILIKE :name', {
        name: `%${filters.name}%`,
      });
    }

    if (sort?.property) {
      this.addSortParams(sort, queryBuilder);
    }

    const paginatedResponse = await paginate<MirroredCourses>(queryBuilder, pageOptions, 'mirrored_courses', null);
    return plainToInstance(PageDto<MirroredCourseDto>, {
      ...paginatedResponse,
      items: this.mapToMirroredCourseDto(plainToInstance(MirroredCourses, paginatedResponse.items)),
    });
  }

  async save(createCourseDto: CreateMirroredCourseDto) {
    return this.repository.save(createCourseDto);
  }

  async findOne(id: string, workspaceId: string): Promise<MirroredCourses | undefined> {
    const queryBuilder = this.dataSource
      .createQueryBuilder(MirroredCourses, 'mirrored_courses')
      .leftJoinAndSelect('mirrored_courses.course', 'course')
      .leftJoinAndSelect('mirrored_courses.mission', 'mission')
      .where('mirrored_courses.deletedAt IS NULL')
      .andWhere({ id })
      .andWhere('mirrored_courses.workspace_id = :workspaceId', { workspaceId });
    return queryBuilder.getOne();
  }

  async findOneByMissionId(missionId: string, workspaceId: string): Promise<MirroredCourses | undefined> {
    const queryBuilder = this.dataSource
      .createQueryBuilder(MirroredCourses, 'mirrored_courses')
      .leftJoinAndSelect('mirrored_courses.course', 'course')
      .leftJoinAndSelect('mirrored_courses.mission', 'mission')
      .where('mirrored_courses.deletedAt IS NULL')
      .andWhere({ missionId })
      .andWhere('mirrored_courses.workspace_id = :workspaceId', { workspaceId });
    return queryBuilder.getOne();
  }

  async update(id: string, updateCourseDto: UpdateMirroredCourseDto): Promise<number> {
    const { affected } = await this.repository.update({ id }, updateCourseDto);
    return affected;
  }

  async findBy(where: Partial<MirroredCourses>): Promise<MirroredCourses[]> {
    return this.repository.findBy({ ...where, deletedAt: null });
  }

  private mapToMirroredCourseDto(courses: MirroredCourses[]): MirroredCourseDto[] {
    if (!courses?.length) {
      return [];
    }

    return courses.map((course) => MirroredCourseDto.fromMirroredCourse(course));
  }

  async getCategories(workspaceId: string): Promise<{ category: string }[]> {
    return await this.dataSource
      .createQueryBuilder(MirroredCourses, 'mirrored_courses')
      .select('DISTINCT course.category')
      .leftJoin('mirrored_courses.course', 'course')
      .where('mirrored_courses.workspace_id = :workspaceId', { workspaceId })
      .andWhere('mirrored_courses.deletedAt IS NULL')
      .getRawMany();
  }

  private addSortParams(sort: Sorting, queryBuilder: SelectQueryBuilder<MirroredCourses>) {
    const sortMapping: Record<string, string> = {
      createdAt: 'mirrored_courses.created_date',
      name: 'course.name',
      category: 'course.category',
    };

    const column = sortMapping[sort.property];
    if (column) {
      queryBuilder.orderBy(column, sort.direction);
    }
  }
}
