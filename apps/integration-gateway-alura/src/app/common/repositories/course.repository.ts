import { Injectable } from '@nestjs/common';
import { DataSource, In, Repository, UpdateResult } from 'typeorm';
import { CourseFilterDto } from '../../dtos/courses-filter.dto';
import { Course } from '../../entities/course.entity';
import { PageDto, PageOptionsDto, paginate } from '@keeps-node-apis/@core';
import { plainToInstance } from 'class-transformer';

@Injectable()
export default class CourseRepository extends Repository<Course> {
  private dataSouce: DataSource;

  constructor(dataSource: DataSource) {
    super(Course, dataSource.createEntityManager());
    this.dataSouce = dataSource;
  }

  async findAll(filters: CourseFilterDto, pageOptions: PageOptionsDto): Promise<PageDto<Course[]>> {
    const queryBuilder = this.dataSouce.createQueryBuilder(Course, 'course');

    if (filters.created_date_lte) {
      queryBuilder.andWhere('course.created_date <= :created_date_lte', {
        created_date_lte: filters.created_date_lte,
      });
    }

    if (filters.created_date_gte) {
      queryBuilder.andWhere('course.created_date >= :created_date_gte', {
        created_date_gte: filters.created_date_gte,
      });
    }

    if (filters.integrationId) {
      queryBuilder.andWhere('course.integrationId = :integrationId', {
        integrationId: filters.integrationId,
      });
    }

    console.log('categorias', filters.category);

    if (filters.category?.length) {
      queryBuilder.andWhere({ category: In(filters.category) });
    }

    if (filters.status?.length) {
      queryBuilder.andWhere({ status: In(filters.status) });
    }

    if (filters.name) {
      queryBuilder.andWhere('course.name ILIKE :name', {
        name: `%${filters.name}%`,
      });
    }

    queryBuilder.andWhere('course.deletedAt IS NULL');

    const paginatedResponse = await paginate<Course>(queryBuilder, pageOptions, 'course');
    return plainToInstance(PageDto<Course[]>, {
      ...paginatedResponse,
      items: plainToInstance(Course, paginatedResponse.items),
    });
  }

  async softDelete(entity: Course): Promise<UpdateResult> {
    return super.update({ id: entity.id }, { deletedAt: new Date() });
  }

  async getCategories(): Promise<{ category: string }[]> {
    return await this.dataSouce.createQueryBuilder(Course, 'course').select('DISTINCT course.category').getRawMany();
  }
}
