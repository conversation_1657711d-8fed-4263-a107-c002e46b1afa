import { convertISOToBrazilianFormat } from './date-utils';

describe('convertISOToBrazilianFormat', () => {
  it('should convert ISO date to Brazilian format', () => {
    const isoDate = '2023-06-01';
    const expectedDate = '01/06/2023';
    expect(convertISOToBrazilianFormat(isoDate)).toBe(expectedDate);
  });

  it('should throw an error for invalid ISO date strings', () => {
    const invalidIsoDate = 'invalid-date';
    expect(() => convertISOToBrazilianFormat(invalidIsoDate)).toThrowError();
  });
});
