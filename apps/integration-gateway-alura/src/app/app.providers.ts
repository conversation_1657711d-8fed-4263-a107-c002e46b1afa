import { Provider } from '@nestjs/common';
import { getRepositoryToken } from '@nestjs/typeorm';
import { IntegrationTokenWorkspaceRepository } from './common/repositories/integration-token-workspace.repository';
import WorkspaceRepository from './common/repositories/workspace.repository';
import { IntegrationTokensWorkspace } from './entities/integration_tokens_workspace.entity';
import { Workspace } from './entities/workspace.entity';
import { AluraIntegrationsService } from './services/alura-integrations.service';
import { SSOService } from './services/sso.service';

import { AdminGuard, KeepsAuthGuard } from '@keeps-node-apis/@core';
import { APP_GUARD } from '@nestjs/core';
import { DataSource } from 'typeorm';
import CourseRepository from './common/repositories/course.repository';
import { MirroredCoursesRepository } from './common/repositories/mirrored-courses-repository.interface';
import MissionRepository from './common/repositories/mission.repository';
import TypeOrmMirroredCourseRepository from './common/repositories/type-orm-mirrored-course.repository';
import { Course } from './entities/course.entity';
import { Mission } from './entities/mission.entity';
import ConnectionService from './notification/notification-connection.service';
import { NotificationService } from './notification/notification.service';
import { CoursesService } from './services/courses.service';
import { EnrollmentService } from './services/konquest-rest/enrollment.service';
import { MissionService } from './services/konquest-rest/mission.service';
import { MirrorCourseService } from './services/mirror-course.service';
import { MirroredCoursesService } from './services/mirrored-courses.service';
import { MyaccountUserService } from './services/myaccount-user.service';
import { SyncCoursesService } from './services/sync-courses.service';
import { SyncEnrollmentsService } from './services/sync-enrollments';

const INTEGRATION_TOKEN_WORKSPACE_REPOSITORY: Provider = {
  provide: getRepositoryToken(IntegrationTokensWorkspace),
  useClass: IntegrationTokenWorkspaceRepository,
};

const WORKSPACE_REPOSITORY: Provider = {
  provide: getRepositoryToken(Workspace),
  useClass: WorkspaceRepository,
};

const COURSE_REPOSITORY: Provider = {
  provide: getRepositoryToken(Course),
  useClass: CourseRepository,
};

const MIRRORED_COURSE_REPOSITORY: Provider = {
  provide: MirroredCoursesRepository,
  inject: [DataSource],
  useFactory: (dataSource: DataSource) => new TypeOrmMirroredCourseRepository(dataSource),
};

const MISSION_REPOSITORY: Provider = {
  provide: getRepositoryToken(Mission),
  useClass: MissionRepository,
};

const SERVICES = [
  AluraIntegrationsService,
  SSOService,
  CoursesService,
  MirroredCoursesService,
  MyaccountUserService,
  NotificationService,
  ConnectionService,
  SyncCoursesService,
  SyncEnrollmentsService,
  EnrollmentService,
  MissionService,
  MirrorCourseService,
];
const REPOSITORIES = [
  INTEGRATION_TOKEN_WORKSPACE_REPOSITORY,
  WORKSPACE_REPOSITORY,
  COURSE_REPOSITORY,
  MIRRORED_COURSE_REPOSITORY,
  MISSION_REPOSITORY,
];

const AUTH_GUARD: Provider = {
  provide: APP_GUARD,
  useClass: KeepsAuthGuard,
};

// TODO: Adicionar ADMIN GUARD, porém fora das rotas acessíveis por usuários
const ADMIN_GUARD: Provider = { provide: APP_GUARD, useClass: AdminGuard };

const PROVIDERS: Provider[] = [...REPOSITORIES, ...SERVICES, AUTH_GUARD];

export default PROVIDERS;
