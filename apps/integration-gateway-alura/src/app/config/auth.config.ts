import { registerAs } from '@nestjs/config';
import { KeycloakConnectConfig, TokenValidation } from 'nest-keycloak-connect';

export default registerAs('auth', () => {
  const config: KeycloakConnectConfig = {
    authServerUrl: process.env.AUTH_URL,
    realm: process.env.AUTH_REALM,
    resource: process.env.AUTH_CLIENT_ID,
    secret: process.env.AUTH_CLIENT_SECRET,
    bearerOnly: true,
    logLevels: ['log'],
    tokenValidation: TokenValidation.OFFLINE,
    realmPublicKey: process.env.AUTH_REALM_PUBLIC_KEY,
  };
  return config;
});
