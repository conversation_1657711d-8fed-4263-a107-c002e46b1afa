import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';
import { entitiesAndMigrations } from '../db/entities-and-migrations';

export default registerAs('database', (): TypeOrmModuleOptions => {
  return {
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: +process.env.DB_PORT || 5432,
    username: process.env.DB_USER,
    password: process.env.DB_PASS,
    database: process.env.DB_NAME,
    autoLoadEntities: true,
    synchronize: false,
    logging: process.env.DB_DEBUG === 'true',
    migrations: entitiesAndMigrations.migrations,
    migrationsRun: process.env.MIGRATIONS_RUN === 'true',
    extra: {
      max: 4,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
    },
  };
});
