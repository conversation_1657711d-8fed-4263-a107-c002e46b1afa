import { registerAs } from '@nestjs/config';

export interface GlobalAppConfig {
  userOwnerIntegrationId: string;
  groupName: string;
  providerId: string;
  port: number;
  nodenv: string;
  aluraApiUrl: string;
  aluraSsoUrl: string;
  aluraSingleSignOnUrl: string;
  tokenAluraGetAllCourses: string;
  tokenAluraGetFinishedEnrollments: string;
  tokenAluraGetProgressEnrollments: string;
  tokenAluraSSO: string;
}

export default registerAs('config', () => {
  const port = parseInt(process.env.PORT, 10) || 3000;
  const nodenv = process.env.NODE_ENV;
  const aluraApiUrl = process.env.ALURA_API_URL || 'https://cursos.alura.com.br/corp/api/v1';
  const aluraSsoUrl = (process.env.ALURA_SSO_URL = 'https://cursos.alura.com.br/getAuthorizationToken');
  const aluraSingleSignOnUrl = (process.env.ALURA_SINGLE_SIGN_ON_URL = 'https://cursos.alura.com.br/singleSignOn');

  // Required environment variables
  const tokenAluraGetAllCourses = process.env.TOKEN_ALURA_GET_ALL_COURSES;
  const tokenAluraGetFinishedEnrollments = process.env.TOKEN_ALURA_GET_FINISHED_ENROLLMENTS;
  const tokenAluraGetProgressEnrollments = process.env.TOKEN_ALURA_GET_PROGRESS_ENROLLMENTS;
  const tokenAluraSSO = process.env.TOKEN_ALURA_SSO;
  const userOwnerIntegrationId = process.env.USER_OWNER_ALURA_INTEGRATION_ID || '1b26d935-f87c-4e2a-94e9-e1d377cc5e16';
  const groupName = process.env.GROUP_NAME_ALURA || 'Alura Integration';
  const providerId = process.env.ALURA_PROVIDER_ID || 'b7927b8c-20dd-431a-a006-1bebab0c7d64';

  if (!tokenAluraGetAllCourses) {
    throw new Error('The environment variable TOKEN_ALURA_GET_ALL_COURSES is required.');
  }

  if (!tokenAluraGetFinishedEnrollments) {
    throw new Error('The environment variable TOKEN_ALURA_GET_FINISHED_ENROLLMENTS is required.');
  }

  if (!tokenAluraGetProgressEnrollments) {
    throw new Error('The environment variable TOKEN_ALURA_GET_PROGRESS_ENROLLMENTS is required.');
  }

  if (!tokenAluraSSO) {
    throw new Error('The environment variable TOKEN_ALURA_SSO is required.');
  }

  return {
    userOwnerIntegrationId,
    groupName,
    providerId,
    port,
    nodenv,
    aluraApiUrl,
    aluraSsoUrl,
    aluraSingleSignOnUrl,
    tokenAluraGetAllCourses,
    tokenAluraGetFinishedEnrollments,
    tokenAluraGetProgressEnrollments,
    tokenAluraSSO,
  };
});
