import { LANGUAGES } from '@keeps-node-apis/@core';
import { IsEmail, IsEnum, IsNotEmpty, IsObject, IsOptional, IsString, IsUUID } from 'class-validator';

export class CreateEmailMessageDto {
  @IsEmail()
  @IsOptional()
  userId: string;

  @IsString()
  @IsNotEmpty()
  subject: string;

  @IsUUID(4)
  workspaceId: string;

  @IsEnum(LANGUAGES)
  language: string;

  @IsOptional()
  @IsString()
  template: string;

  @IsOptional()
  @IsObject()
  templateData: any;

  receiverMail: string;
}
