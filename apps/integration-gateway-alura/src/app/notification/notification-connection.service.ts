import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import amqp, { AmqpConnectionManager, ChannelWrapper } from 'amqp-connection-manager';
import { Channel } from 'amqplib';
import { Message } from './dtos/message.dto';
export type MessageProcessor = (message: Message) => Promise<unknown>;

@Injectable()
export default class ConnectionService {
  private readonly logger = new Logger(ConnectionService.name);
  private uri: string;
  private connection: AmqpConnectionManager;
  private channelWrapper: ChannelWrapper;
  private queue: string;
  private deadLetterQueue: string;

  constructor(private configService: ConfigService) {
    this.uri = this.configService.get<string>('RABBITMQ_URI');
    this.queue = this.configService.get('RABBITMQ_BELL_NOTIFICATIONS_QUEUE') || 'bell';
    this.deadLetterQueue = this.queue + '-dead-letter';
    const workerQueueIsDurableConfig = configService.get('BELL_NOTIFICATIONS_QUEUE_IS_DURABLE');
    const queueIsDurable = workerQueueIsDurableConfig ? JSON.parse(workerQueueIsDurableConfig) : true;
    this.connectChannel(this.queue, this.deadLetterQueue, queueIsDurable);
  }

  private async waitForConnect() {
    try {
      await this.channelWrapper.waitForConnect();
      this.logger.log('listening for messages');
    } catch (error) {
      this.logger.error(`error to connect on channel: ${error}`);
    }
  }

  async connectChannel(
    queue: string,
    deadLetterQueue: string,
    options: { queueIsDurable: boolean } = { queueIsDurable: true },
  ): Promise<void> {
    this.connection = amqp.connect([this.uri]);
    this.queue = queue;
    this.deadLetterQueue = deadLetterQueue;
    this.connection.on('connectFailed', () => this.logger.error(`error to connect on broker`));
    this.channelWrapper = this.connection.createChannel({
      json: true,
      setup: (channel: Channel) => {
        return Promise.all([
          channel.assertQueue(queue, {
            durable: options.queueIsDurable,
            deadLetterExchange: '',
            deadLetterRoutingKey: deadLetterQueue,
          }),
          channel.assertQueue(deadLetterQueue, { durable: true }),
          channel.prefetch(1),
        ]);
      },
    });
    this.waitForConnect();
  }

  public async send(payload: Record<string, any>, taskName = ''): Promise<void> {
    await this.channelWrapper.waitForConnect();

    try {
      await this.channelWrapper.sendToQueue(this.queue, payload, {
        persistent: true,
        headers: { taskName },
      });
      this.logger.log(`Message sent to queue`);
    } catch (error) {
      this.logger.error(`Failed to send message: ${error.message}`);
    }
  }
}
