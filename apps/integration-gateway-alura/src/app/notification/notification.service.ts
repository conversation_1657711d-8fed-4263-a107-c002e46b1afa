import { Inject, Injectable, Logger } from '@nestjs/common';

import ConnectionService from './notification-connection.service';
import { Message } from './dtos/message.dto';
import { ConfigService } from '@nestjs/config';
import { CreateEmailMessageDto } from './dtos/email-payload.dto';
import { HTTP_CLIENT, HttpClient } from '@keeps-node-apis/@core';

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);

  constructor(
    private readonly connectionService: ConnectionService,
    private readonly configService: ConfigService,
    @Inject(HTTP_CLIENT) private http: HttpClient,
  ) {}

  public async createNotification(
    userIds: string[],
    workspaceId: string,
    typeKey: string,
    action: string,
    message: Message,
    additionalData?: { [key: string]: any },
  ): Promise<void> {
    if (!(message instanceof Message)) {
      throw new Error(`${message} is not a valid Message Type`);
    }

    const objectId = additionalData?.object;
    const url = additionalData?.url;

    for (const userId of userIds) {
      await this.connectionService.send({
        user_receiving_id: userId,
        type_key: typeKey,
        action: action,
        messages: message,
        object: objectId,
        url: url,
        workspace_id: workspaceId,
      });
    }
  }

  async sendEmailNotification(createEmailMessageDto: CreateEmailMessageDto) {
    const url = this.configService.get('NOTIFICATION_API_URL') + '/emails/messages';
    try {
      return await this.http.post(url, createEmailMessageDto);
    } catch (e) {
      throw new Error(`Failed to send email: ${e}`);
    }
  }
}
