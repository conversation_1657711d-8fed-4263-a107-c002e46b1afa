import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import { DataSource } from 'typeorm';
import { entitiesAndMigrations } from './entities-and-migrations';

config();

const configService = new ConfigService({});

const host = configService.get('DB_HOST') === 'db' ? 'localhost' : configService.get('DB_HOST');

export default new DataSource({
  type: 'postgres',
  host,
  port: +configService.get('DB_PORT'),
  username: configService.get('DB_USER'),
  password: configService.get('DB_PASS'),
  database: configService.get('DB_NAME'),
  entities: entitiesAndMigrations.entities,
  logging: true,
  migrations: entitiesAndMigrations.migrations,
});
