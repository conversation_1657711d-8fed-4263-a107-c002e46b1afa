import { MigrationInterface, QueryRunner } from "typeorm";

export class INITIAL1738858832816 implements MigrationInterface {
    name = 'INITIAL1738858832816'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TYPE "public"."enum_course_status" AS ENUM('DISABLED', 'PUBLISHED')`);
        await queryRunner.query(`CREATE TABLE "courses" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "course_id" text NOT NULL, "name" text NOT NULL, "integration_id" uuid NOT NULL, "category" text NOT NULL, "created_date" date NOT NULL, "status" "public"."enum_course_status" NOT NULL DEFAULT 'PUBLISHED', "course_url" text NOT NULL, "image_url" text NOT NULL, "description" text NOT NULL, "workload" integer NOT NULL, "deleted_at" TIMESTAMP WITH TIME ZONE, CONSTRAINT "PK_3f70a487cc718ad8eda4e6d58c9" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "workspace" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "alura_integration_active" boolean NOT NULL, CONSTRAINT "PK_ca86b6f9b3be5fe26d307d09b49" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TYPE "public"."integration_token_workspace_token_name_enum" AS ENUM('sso', 'finished_enrollments', 'courses', 'enrollment_progress')`);
        await queryRunner.query(`CREATE TYPE "public"."integration_token_workspace_status_enum" AS ENUM('ACTIVE', 'INVALID', 'CHANGED_SIGNATURE')`);
        await queryRunner.query(`CREATE TABLE "integration_token_workspace" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "workspace_id" uuid NOT NULL, "token_name" "public"."integration_token_workspace_token_name_enum" NOT NULL, "last_checked_at" TIMESTAMP NOT NULL, "status" "public"."integration_token_workspace_status_enum" NOT NULL DEFAULT 'ACTIVE', "token" text NOT NULL, CONSTRAINT "UQ_773a1806f6512c9161b1a3e6a56" UNIQUE ("workspace_id", "token_name"), CONSTRAINT "PK_23733cd0a204e751bb4661a7f4b" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "idx_token_name" ON "integration_token_workspace" ("token_name") `);
        await queryRunner.query(`CREATE INDEX "idx_workspace_id" ON "integration_token_workspace" ("workspace_id") `);
        await queryRunner.query(`CREATE TABLE "missions" ("id" uuid NOT NULL, "user_creator_id" uuid NOT NULL, "development_status" text NOT NULL, CONSTRAINT "PK_787aebb1ac5923c9904043c6309" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "mirrored_courses" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "course_id" uuid NOT NULL, "workspace_id" uuid NOT NULL, "mission_id" uuid NOT NULL, "created_date" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(), "is_active" boolean NOT NULL DEFAULT true, "deleted_at" TIMESTAMP WITH TIME ZONE, "deleted_info" character varying, CONSTRAINT "REL_6b0a3e28bf5b7bd24c1f5b6146" UNIQUE ("mission_id"), CONSTRAINT "PK_ce4ec9e9645cabbc0a0a3b6c156" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "integration_token_workspace" ADD CONSTRAINT "FK_4224d208a217684c7af264e49ca" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mirrored_courses" ADD CONSTRAINT "FK_fac52fbc27f324b2e4b4293e396" FOREIGN KEY ("course_id") REFERENCES "courses"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mirrored_courses" ADD CONSTRAINT "FK_51d3ddd50ffb90d8b0af09f3e12" FOREIGN KEY ("workspace_id") REFERENCES "workspace"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "mirrored_courses" ADD CONSTRAINT "FK_6b0a3e28bf5b7bd24c1f5b61460" FOREIGN KEY ("mission_id") REFERENCES "missions"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "mirrored_courses" DROP CONSTRAINT "FK_6b0a3e28bf5b7bd24c1f5b61460"`);
        await queryRunner.query(`ALTER TABLE "mirrored_courses" DROP CONSTRAINT "FK_51d3ddd50ffb90d8b0af09f3e12"`);
        await queryRunner.query(`ALTER TABLE "mirrored_courses" DROP CONSTRAINT "FK_fac52fbc27f324b2e4b4293e396"`);
        await queryRunner.query(`ALTER TABLE "integration_token_workspace" DROP CONSTRAINT "FK_4224d208a217684c7af264e49ca"`);
        await queryRunner.query(`DROP TABLE "mirrored_courses"`);
        await queryRunner.query(`DROP TABLE "missions"`);
        await queryRunner.query(`DROP INDEX "public"."idx_workspace_id"`);
        await queryRunner.query(`DROP INDEX "public"."idx_token_name"`);
        await queryRunner.query(`DROP TABLE "integration_token_workspace"`);
        await queryRunner.query(`DROP TYPE "public"."integration_token_workspace_status_enum"`);
        await queryRunner.query(`DROP TYPE "public"."integration_token_workspace_token_name_enum"`);
        await queryRunner.query(`DROP TABLE "workspace"`);
        await queryRunner.query(`DROP TABLE "courses"`);
        await queryRunner.query(`DROP TYPE "public"."enum_course_status"`);
    }

}
