import { Injectable } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { TokenWorkspaceStatusEnum } from '../common/models/integration-token-status.enum';
import { IntegrationTokenWorkspaceRepository } from '../common/repositories/integration-token-workspace.repository';
import { CreateCourseDto } from '../dtos/courses-create.dto';
import { CourseAluraExpectedDto } from '../dtos/expecteds/course-alura-expected.dto';
import { IntegrationTokensWorkspace } from '../entities/integration_tokens_workspace.entity';
import { AluraIntegrationsService } from './alura-integrations.service';
import { CoursesService } from './courses.service';

@Injectable()
export class SyncCoursesService {
  constructor(
    private aluraIntegrationService: AluraIntegrationsService,
    private coursesService: CoursesService,
    @InjectRepository(IntegrationTokensWorkspace)
    private integrationTokenWorkspaceRepository: IntegrationTokenWorkspaceRepository,
  ) {}

  async syncCourses() {
    const $tokenFetchCourses = await this.integrationTokenWorkspaceRepository.findOne({
      where: {
        status: TokenWorkspaceStatusEnum.ACTIVE,
      },
    });
    const tokenFetchCourses = $tokenFetchCourses.token;

    const status = { Desabilitado: 'DISABLED', Publicado: 'PUBLISHED' };
    const aluraCourses = (await this.aluraIntegrationService.fetchCourses({
      token: tokenFetchCourses,
    })) as CourseAluraExpectedDto[];

    for (const course of aluraCourses) {
      const createCourseDto: CreateCourseDto = {
        courseId: course.id.toString(),
        name: course.nome,
        integrationId: 'e7f28205-9921-41df-836b-d0d494cd23a9', // TODO: Pegar id do banco ou de variável de ambiente
        category: course.categoria,
        created_date: this.stringToDate(course.dataLancamento),
        status: status[course.estado],
        courseUrl: course.url,
        imageUrl: course.imagem,
        description: course.descricao,
        workload: course.cargaHoraria,
      };
      await this.coursesService.checkCourseChangeStatus(createCourseDto.courseId, createCourseDto.status);
      await this.coursesService.createOrUpdateCourse(createCourseDto);
    }
  }

  stringToDate(dateString: string): Date {
    const [datePart, timePart] = dateString.split(' ');
    const [day, month, year] = datePart.split('/').map(Number);
    const [hours, minutes, seconds] = timePart.split(':').map(Number);
    return new Date(year, month - 1, day, hours, minutes, seconds);
  }

  @Cron(CronExpression.EVERY_DAY_AT_3AM)
  async cronSyncCourses() {
    console.log('Syncing courses...');
    await this.syncCourses();
  }
}
