import { Inject, Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { ClassConstructor, plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { HTTP_CLIENT, HttpClient } from '@keeps-node-apis/@core';
import { convertISOToBrazilianFormat } from '../common/utils/date-utils';
import { TokenWorkspaceStatusEnum } from '../common/models/integration-token-status.enum';
import { IntegrationTokenWorkspaceRepository } from '../common/repositories/integration-token-workspace.repository';
import { GlobalAppConfig } from '../config/app.config';
import { CoursesCompletedFilterDTO } from '../dtos/courses-completed-filter.dto';
import { CoursesInputDTO } from '../dtos/courses-input.dto';
import { CoursesProgressFilterDTO } from '../dtos/courses-progress-filter.dto';
import { AuthTokenAluraExpectedDto } from '../dtos/expecteds/auth-token-alura-expected.dto';
import { CourseAluraExpectedDto } from '../dtos/expecteds/course-alura-expected.dto';
import { EnrollmentCompletedAluraExpectedDto } from '../dtos/expecteds/enrollment-completed-alura-expected.dto';
import { EnrollmentProgressAluraExpectedDto } from '../dtos/expecteds/enrollment-progress-alura-expected.dto';
import {
  IntegrationTokenCreateResponseDto,
  IntegrationTokenResponseItem,
} from '../dtos/integration-token-create-response.dto';
import { IntegrationTokenWorkspaceCreateDto } from '../dtos/integration-token-workspace.dto';
import { ListIntegrationTokenResponseDto } from '../dtos/list-integration-token-response-dto';
import { SSOInputDTO } from '../dtos/sso-input.dto';
import { ValidateTokenDTO } from '../dtos/validate-token.dto';
import { AluraTokenName, IntegrationTokensWorkspace } from '../entities/integration_tokens_workspace.entity';
import { CreateEmailMessageDto } from '../notification/dtos/email-payload.dto';
import { Message } from '../notification/dtos/message.dto';
import { NotificationService } from '../notification/notification.service';
import { MyaccountUserService } from './myaccount-user.service';
import { SSOService } from './sso.service';

export type IntegrationTokenType = keyof IntegrationTokenWorkspaceCreateDto;

const ALLOWED_INTEGRATION_TOKENS: IntegrationTokenType[] = [
  'sso',
  'courses',
  'enrollment_progress',
  'finished_enrollments',
];

type SignatureCheckItem<T> = {
  name: IntegrationTokenType;
  args: { token: string; email?: string };
  dto: ClassConstructor<T>;
};

type SignatureCheckItemDtos =
  | CourseAluraExpectedDto
  | EnrollmentCompletedAluraExpectedDto
  | EnrollmentProgressAluraExpectedDto
  | AuthTokenAluraExpectedDto;

@Injectable()
export class AluraIntegrationsService {
  private readonly logger: Logger;
  private config: GlobalAppConfig;

  constructor(
    @Inject(HTTP_CLIENT) private httpClient: HttpClient,
    private ssoService: SSOService,
    private myaccountUserService: MyaccountUserService,
    private notificationService: NotificationService,
    @InjectRepository(IntegrationTokensWorkspace)
    private integrationTokensWorkspaceRepository: IntegrationTokenWorkspaceRepository,
    configService: ConfigService,
  ) {
    this.logger = new Logger(AluraIntegrationsService.name);
    this.config = configService.get<GlobalAppConfig>('config');
  }

  getIntegrations(): IntegrationTokenType[] {
    return ALLOWED_INTEGRATION_TOKENS;
  }

  async validateIntegrationToken(
    integrationTokenType: IntegrationTokenType,
    input: ValidateTokenDTO,
  ): Promise<boolean> {
    const { token } = input;
    if (!token) {
      throw new Error('Token is Required');
    }

    const validationFunction = this.getValidationFnByTokenType(integrationTokenType);

    try {
      await validationFunction(input);
      return true;
    } catch (error) {
      this.logger.error(error);
      return error?.response?.error === 'User not found';
    }
  }

  fetchCourses = (input: CoursesInputDTO) => {
    const { token } = input;
    if (!token) {
      throw new Error('Token is Required');
    }
    return this.httpClient.get(`${this.config.aluraApiUrl}/cursos`, { params: { token } });
  };

  fetchCompletedEnrollments = (input: CoursesCompletedFilterDTO) => {
    const { token, startAt, completedAt, email, enrollment } = input;
    if (!token) {
      throw new Error('Token is Required');
    }
    return this.httpClient.get<EnrollmentCompletedAluraExpectedDto[]>(`${this.config.aluraApiUrl}/conclusoes`, {
      params: {
        token,
        ...(startAt && { dataDeInicio: convertISOToBrazilianFormat(startAt) }),
        ...(completedAt && { dataDeFim: convertISOToBrazilianFormat(completedAt) }),
        ...(email && { email }),
        ...(enrollment && { matricula: enrollment }),
      },
    });
  };

  fetchEnrollmentProgress = (input: CoursesProgressFilterDTO) => {
    const { token, startAt, completedAt, email } = input;
    if (!token) {
      throw new Error('Token is Required');
    }
    return this.httpClient.get<EnrollmentProgressAluraExpectedDto[]>(`${this.config.aluraApiUrl}/progresso`, {
      params: {
        token,
        ...(startAt && { dataDeInicio: convertISOToBrazilianFormat(startAt) }),
        ...(completedAt && { dataDeFim: convertISOToBrazilianFormat(completedAt) }),
        ...(email && { email }),
      },
    });
  };

  async findAllWorkspaceTokensByWorkspace(workspaceId: string): Promise<ListIntegrationTokenResponseDto> {
    const tokens = await this.integrationTokensWorkspaceRepository.findByWorkspaceAndIntegration(workspaceId);
    const result: ListIntegrationTokenResponseDto = {
      courses: '',
      enrollment_progress: '',
      finished_enrollments: '',
      sso: '',
    };
    tokens?.forEach((workspaceToken) => {
      result[workspaceToken.tokenName] = workspaceToken.token;
    });
    return result;
  }

  async createWorkspaceToken(workspaceId: string, tokensCreateDto: IntegrationTokenWorkspaceCreateDto) {
    const response = new IntegrationTokenCreateResponseDto();
    const tokenTypes = Object.keys(tokensCreateDto) as IntegrationTokenType[];
    for (const tokenName of tokenTypes) {
      const tokenValue = tokensCreateDto[tokenName];
      const isValid = await this.validateIntegrationToken(tokenName, { token: tokenValue });
      const tokenResult: IntegrationTokenResponseItem = {
        token_name: tokenName,
        value: tokenValue,
        isValid,
      };
      if (!isValid) {
        response.errors.push(tokenResult);
        continue;
      }
      await this.upsertWorkspaceToken(tokenValue, workspaceId, tokenName);
      response.success.push(tokenResult);
    }
    return response;
  }

  private async upsertWorkspaceToken(tokenValue: string, workspaceId: string, tokenName: string) {
    await this.integrationTokensWorkspaceRepository.upsert(
      {
        token: tokenValue,
        tokenName: tokenName as AluraTokenName,
        workspaceId,
        lastCheckedAt: new Date(),
        status: TokenWorkspaceStatusEnum.ACTIVE,
      },
      ['workspaceId', 'tokenName'],
    );
  }

  @Cron(CronExpression.EVERY_DAY_AT_11PM)
  async checkSignatureTokens() {
    const coursesSignatureCheck: SignatureCheckItem<CourseAluraExpectedDto> = {
      name: 'courses',
      args: { token: this.config.tokenAluraGetAllCourses },
      dto: CourseAluraExpectedDto,
    };
    const finishedEnrollmentsSignatureCheck: SignatureCheckItem<EnrollmentCompletedAluraExpectedDto> = {
      name: 'finished_enrollments',
      args: { token: this.config.tokenAluraGetFinishedEnrollments },
      dto: EnrollmentCompletedAluraExpectedDto,
    };
    const enrollmentProgressSignatureCheck: SignatureCheckItem<EnrollmentProgressAluraExpectedDto> = {
      name: 'enrollment_progress',
      args: { token: this.config.tokenAluraGetProgressEnrollments },
      dto: EnrollmentProgressAluraExpectedDto,
    };
    const ssoSignatureCheck: SignatureCheckItem<AuthTokenAluraExpectedDto> = {
      name: 'sso',
      args: { token: this.config.tokenAluraSSO },
      dto: AuthTokenAluraExpectedDto,
    };
    const tokensMap = new Map<IntegrationTokenType, SignatureCheckItem<SignatureCheckItemDtos>>([
      ['courses', coursesSignatureCheck],
      ['finished_enrollments', finishedEnrollmentsSignatureCheck],
      ['enrollment_progress', enrollmentProgressSignatureCheck],
      ['sso', ssoSignatureCheck],
    ]);
    for (const [integrationTokenType, signatureCheckItem] of tokensMap.entries()) {
      await this.validateSignatureToken(integrationTokenType, signatureCheckItem);
      await this.sleep(5000);
    }
  }

  private async validateSignatureToken(
    tokenType: IntegrationTokenType,
    signatureCheckItem: SignatureCheckItem<SignatureCheckItemDtos>,
  ) {
    const validationFunction = this.getValidationFnByTokenType(tokenType);
    const validationFunctionArgs = signatureCheckItem.args;
    const responseValidationDto = signatureCheckItem.dto;
    const result = await validationFunction(validationFunctionArgs);
    const resultISArray = Array.isArray(result);
    if (!result) {
      return;
    }
    if (resultISArray && result.length === 0) {
      return;
    }
    const plainDto = resultISArray ? (result as unknown[])[0] : result;
    const responseDtoInstance = plainToInstance(responseValidationDto, plainDto);
    const validationErrors = await validate(responseDtoInstance, {
      skipMissingProperties: false,
      forbidNonWhitelisted: true,
    });
    if (validationErrors.length > 0) {
      await this.disableAllTokensWorkspaceByTokenId();
    }
  }

  private async disableAllTokensWorkspaceByTokenId() {
    const tokens = await this.integrationTokensWorkspaceRepository.find();

    for (const token of tokens) {
      token.status = TokenWorkspaceStatusEnum.CHANGED_SIGNATURE;
      await this.integrationTokensWorkspaceRepository.save(token);
      await this.sendIntegrationFailNotificationToAdmins(token.workspaceId);
    }
  }

  @Cron(CronExpression.EVERY_DAY_AT_10AM)
  async checkAllTokens() {
    const tokens = await this.integrationTokensWorkspaceRepository.find({
      where: { status: TokenWorkspaceStatusEnum.ACTIVE },
    });

    for (const token of tokens) {
      const isValid = await this.validateIntegrationToken(token.tokenName, { token: token.token });
      if (!isValid) {
        await this.integrationTokensWorkspaceRepository.update(token.id, {
          status: TokenWorkspaceStatusEnum.INVALID,
        });
        await this.sendIntegrationFailNotificationToAdmins(token.workspaceId);
      }
      await this.sleep(5000);
    }
  }

  sleep(ms: number) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  async sendIntegrationFailNotificationToAdmins(workspaceId: string) {
    const admins = await this.myaccountUserService.getWorkspaceAdmins(workspaceId);
    const user_ids = admins.map((user: { id: any }) => user.id);
    await this.notificationService.createNotification(
      user_ids,
      workspaceId,
      'INTEGRATION_ALURA_FAIL',
      'NOTHING',
      new Message(
        'Falha na integração alura',
        {},
        'Atenção! A integração com a Alura apresenta inconsistência, entre em contato com nosso suporte.',
      ),
    );

    const payloadEmail = {
      subject: 'Erro na integração alura',
      workspaceId: workspaceId,
      language: 'pt-BR',
      template: 'konquest_integration_token_error',
      templateData: {
        integration_name: 'Alura',
        konquest_web_url: 'https://konquest.keepsdev.com',
        workspace_name: 'Keeps',
        workspace_icon:
          'https://media-stage.keepsdev.com/myaccount/workspace-icon/1080facf-7a5b-428b-bcd7-9859b6f3fb2d.png',
      },
    } as CreateEmailMessageDto;

    for (const user_id of user_ids) {
      payloadEmail.userId = user_id;
      await this.notificationService.sendEmailNotification(payloadEmail);
    }
  }

  private getValidationFnByTokenType(tokenType: IntegrationTokenType) {
    if (!ALLOWED_INTEGRATION_TOKENS.includes(tokenType)) {
      throw new Error(`Invalid token type. Allowed types: ${ALLOWED_INTEGRATION_TOKENS}`);
    }
    const validateFunctionsMap: Map<IntegrationTokenType, (...args: any) => Promise<unknown>> = new Map([
      ['courses', this.fetchCourses],
      ['finished_enrollments', this.fetchCompletedEnrollments],
      ['enrollment_progress', this.fetchEnrollmentProgress],
      ['sso', this.ssoCheckFunction],
    ]);
    return validateFunctionsMap.get(tokenType) as (...args: any) => Promise<SignatureCheckItemDtos>;
  }

  private ssoCheckFunction = (input: SSOInputDTO) => this.ssoService.getUserAuthorizationToken(input);
}
