// mission.service.spec.ts

import { HTTP_CLIENT, HttpClient, RestClient } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import { of } from 'rxjs';
import { MissionCreateDTO } from '../../dtos/mission-create.dto';
import { MissionService } from './mission.service';

describe('MissionService', () => {
  let service: MissionService;
  let restClientMock: Partial<RestClient>;
  let mockHttpClient: Partial<HttpClient>;

  beforeEach(async () => {
    restClientMock = {
      post: jest.fn().mockReturnValue(of({ data: {} })),
      delete: jest.fn().mockReturnValue(of({ data: {} })),
      patch: jest.fn().mockReturnValue(of({ data: {} })),
    };

    mockHttpClient = {
      post: jest.fn(),
      get: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        MissionService,
        {
          provide: RestClient,
          useValue: restClientMock,
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('mock_config_value'),
          },
        },
        {
          provide: HTTP_CLIENT,
          useValue: mockHttpClient,
        },
      ],
    }).compile();

    service = moduleRef.get<MissionService>(MissionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createMission', () => {
    it('should call RestClient.post with correct parameters', async () => {
      const missionCreateDTO = { name: 'Test Mission' } as MissionCreateDTO;
      const workspaceId = 'testWorkspaceId';

      await service.createMission(missionCreateDTO, workspaceId);

      expect(mockHttpClient.post).toHaveBeenCalledWith(
        'mock_config_value/missions/external/mirror',
        missionCreateDTO,
        expect.objectContaining({
          headers: { 'X-Client': workspaceId, 'Content-Type': 'application/json', Authorization: 'mock_config_value' },
        }),
      );
    });
  });

  describe('deleteMission', () => {
    it('should call RestClient.delete with correct parameters', async () => {
      const missionId = '1';
      const workspaceId = 'testWorkspaceId';
      const token = 'testToken';

      await service.deleteMission(missionId, workspaceId, token);

      expect(restClientMock.delete).toHaveBeenCalledWith(
        expect.stringContaining(`/missions/${missionId}`),
        expect.objectContaining({
          workspace_id: workspaceId,
          token: token,
        }),
        expect.any(Object),
      );
    });
  });

  describe('updateMission', () => {
    it('should call RestClient.patch with correct parameters', async () => {
      const missionId = '1';
      const data = { name: 'Updated Name' };
      const workspaceId = 'testWorkspaceId';

      await service.updateMission(missionId, data, workspaceId);

      expect(restClientMock.patch).toHaveBeenCalledWith(
        expect.stringContaining(`/missions/${missionId}`),
        data,
        expect.objectContaining({
          workspace_id: workspaceId,
          token: expect.any(String),
        }),
      );
    });
  });
});
