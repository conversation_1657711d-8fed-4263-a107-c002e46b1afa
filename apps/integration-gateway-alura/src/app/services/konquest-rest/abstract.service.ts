import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HTTP_CLIENT, HttpClient, RestClient } from '@keeps-node-apis/@core';
import { AxiosRequestConfig } from 'axios';

@Injectable()
export abstract class BaseService {
  constructor(
    protected rest: RestClient,
    protected configService: ConfigService,
    @Inject(HTTP_CLIENT) protected httpClient: HttpClient,
  ) {}

  getConfig(): AxiosRequestConfig {
    return {
      headers: {
        Accept: 'application/json',
      },
    } as AxiosRequestConfig;
  }

  getBaseUrl(): string {
    return this.configService.get('KONQUEST_URL');
  }
  getToken(): string {
    return this.configService.get('KEEPS_SECRET_TOKEN_INTEGRATION');
  }
}
