import { RestParams } from '@keeps-node-apis/@core';
import { Injectable } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import { MissionCreateDTO } from '../../dtos/mission-create.dto';
import { BaseService } from './abstract.service';

@Injectable()
export class MissionService extends BaseService {
  async createMission(missionCreateDTO: MissionCreateDTO, workspace_id: string): Promise<any> {
    const url = this.getBaseUrl() + '/missions/external/mirror';

    return await this.httpClient.post(url, missionCreateDTO, {
      headers: { 'X-Client': workspace_id, Authorization: this.getToken(), 'Content-Type': 'application/json' },
    });
  }

  async deleteMission(missionId: string, workspace_id: string, token: string): Promise<any> {
    const request = this.rest
      .delete(
        this.getBaseUrl() + '/missions/' + missionId,
        { workspace_id: workspace_id, token: token } as RestParams,
        this.getConfig(),
      )
      .pipe(
        catchError((error) => {
          throw new Error(error);
        }),
      );

    const response = await firstValueFrom(request);
    return response;
  }

  async updateMission(missionId: string, data: any, workspace_id: string): Promise<any> {
    const request = this.rest
      .patch(this.getBaseUrl() + '/missions/' + missionId, data, {
        workspace_id: workspace_id,
        token: this.getToken(),
      } as RestParams)
      .pipe(
        catchError((error) => {
          throw new Error(error);
        }),
      );

    const response = await firstValueFrom(request);
    return response;
  }
}
