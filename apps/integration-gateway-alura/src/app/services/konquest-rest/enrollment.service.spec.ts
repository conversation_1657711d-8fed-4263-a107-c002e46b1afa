// enrollment.service.spec.ts

import { HTTP_CLIENT, RestClient } from '@keeps-node-apis/@core';
import { ConfigService } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import { of } from 'rxjs';
import { EnrollmentProviderDTO } from '../../dtos/enrollment-provider.dto';
import { EnrollmentService } from './enrollment.service';

describe('EnrollmentService', () => {
  let service: EnrollmentService;
  let restClientSpy: jest.SpyInstance;

  beforeEach(async () => {
    const mockHttpClient = {
      post: jest.fn(),
      get: jest.fn(),
    };

    const moduleRef = await Test.createTestingModule({
      providers: [
        {
          provide: RestClient,
          useFactory: () => ({
            post: jest.fn().mockReturnValue(of({ data: {} })),
          }),
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue('test'),
          },
        },
        EnrollmentService,
        {
          provide: HTTP_CLIENT,
          useValue: mockHttpClient,
        },
      ],
    }).compile();

    service = moduleRef.get<EnrollmentService>(EnrollmentService);
    restClientSpy = jest.spyOn(moduleRef.get(RestClient), 'post');
  });

  afterEach(() => {
    restClientSpy.mockRestore();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('syncEnrollment', () => {
    it('should call RestClient.post with correct parameters', async () => {
      const mockData = [{ email: 'email' }] as EnrollmentProviderDTO[];
      const workspaceId = 'testWorkspaceId';

      await service.syncEnrollment(mockData, workspaceId);

      expect(restClientSpy).toHaveBeenCalledWith(
        expect.stringContaining('/mission-enrollments/sync-enrollments-provider'),
        mockData,
        expect.objectContaining({
          workspace_id: workspaceId,
          token: 'test',
        }),
        expect.any(Object),
      );
    });
  });
});
