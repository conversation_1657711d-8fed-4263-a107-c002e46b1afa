import { RestParams } from '@keeps-node-apis/@core';
import { Injectable } from '@nestjs/common';
import { catchError, firstValueFrom } from 'rxjs';
import { EnrollmentProviderDTO } from '../../dtos/enrollment-provider.dto';
import { BaseService } from './abstract.service';

@Injectable()
export class EnrollmentService extends BaseService {
  async syncEnrollment(enrollmentProviderDTO: EnrollmentProviderDTO[], workspace_id: string): Promise<any> {
    const request = this.rest
      .post(
        this.getBaseUrl() + '/mission-enrollments/sync-enrollments-provider',
        enrollmentProviderDTO,
        { workspace_id: workspace_id, token: this.getToken() } as RestParams,
        this.getConfig(),
      )
      .pipe(
        catchError((error) => {
          throw new Error(error);
        }),
      );

    const response = await firstValueFrom(request);
    return response;
  }
}
