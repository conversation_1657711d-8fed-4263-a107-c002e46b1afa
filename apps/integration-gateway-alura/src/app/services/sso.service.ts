import { Inject, Injectable } from '@nestjs/common';

import { HTTP_CLIENT, HttpClient } from '@keeps-node-apis/@core';
import { SSOInputDTO } from '../dtos/sso-input.dto';
import { ConfigService } from '@nestjs/config';
import { GlobalAppConfig } from '../config/app.config';

@Injectable()
export class SSOService {
  private config: GlobalAppConfig;

  constructor(
    @Inject(HTTP_CLIENT) private httpClient: HttpClient,
    configService: ConfigService,
  ) {
    this.config = configService.get<GlobalAppConfig>('config');
  }

  getUserAuthorizationToken(input: SSOInputDTO) {
    // TODO: Para validar o token  devera ser tratada a reposta do servidor da alura, caso seja 'Client key not found' é pq está válido, e caso seja 'Client key not found' considera inválido
    const { token, email } = input;
    const defaultEmail = '<EMAIL>';
    if (!token) {
      throw new Error('Client is Required');
    }
    return this.httpClient.post<{ authorization: string }>(this.config.aluraSsoUrl, {
      client: token,
      userkey: email || defaultEmail,
    });
  }
}
