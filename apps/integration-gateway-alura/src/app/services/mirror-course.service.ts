import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectRepository } from '@nestjs/typeorm';
import MissionRepository from '../common/repositories/mission.repository';
import { MissionCreateDTO } from '../dtos/mission-create.dto';
import { MirroredCourses } from '../entities/mirrored_courses.entity';
import { Mission } from '../entities/mission.entity';
import { CoursesService } from './courses.service';
import { MissionService } from './konquest-rest/mission.service';
import { MirroredCoursesService } from './mirrored-courses.service';
import { GlobalAppConfig } from '../config/app.config';

@Injectable()
export class MirrorCourseService {
  private config: GlobalAppConfig;

  constructor(
    configService: ConfigService,
    private missionService: MissionService,
    private coursesService: CoursesService,
    private mirroredCoursesService: MirroredCoursesService,
    @InjectRepository(Mission)
    private missionRepository: MissionRepository,
  ) {
    this.config = configService.get('config');
  }

  async mirrorCourse(courseId: string, workspaceId: string): Promise<MirroredCourses> {
    const mirroredCourses = await this.mirroredCoursesService.getByCourseAndWorkspace(courseId, workspaceId);
    if (mirroredCourses.length > 0) {
      return mirroredCourses[0];
    }

    const course = await this.coursesService.getOne(courseId);
    const missionCreateDTO: MissionCreateDTO = {
      name: course.name,
      mission_category_name: course.category,
      language: 'pt-BR',
      description: course.description,
      user_creator_id: this.config.userOwnerIntegrationId,
      group_name: this.config.groupName,
      image_url: course.imageUrl,
      duration_time: course.workload * 60,
      course_url: course.courseUrl,
      provider_id: this.config.providerId,
    };

    const mission = await this.missionService.createMission(missionCreateDTO, workspaceId);

    await this.missionRepository.save({
      id: mission.id,
      userCreatorId: this.config.userOwnerIntegrationId,
      developmentStatus: mission.development_status,
    });

    return await this.mirroredCoursesService.create({
      courseId: courseId,
      missionId: mission.id,
      workspaceId: workspaceId,
    });
  }

  async mirrorCourseBatch(
    courseIds: string[],
    workspaceId: string,
  ): Promise<{ errors: Array<{ [courseId: string]: string }> }> {
    const errors: Array<{ [courseId: string]: string }> = [];

    for (const courseId of courseIds) {
      try {
        await this.mirrorCourse(courseId, workspaceId);
      } catch (error) {
        errors.push({ [courseId]: error.message });
      }
    }

    return { errors };
  }
}
