import { SyncEnrollmentsService } from './sync-enrollments';
import { EnrollmentService } from './konquest-rest/enrollment.service';
import { AluraIntegrationsService } from './alura-integrations.service';
import { MirroredCoursesRepository } from '../common/repositories/mirrored-courses-repository.interface';
import { IntegrationTokenWorkspaceRepository } from '../common/repositories/integration-token-workspace.repository';
import { Chance } from 'chance';
import { IntegrationTokensWorkspace } from '../entities/integration_tokens_workspace.entity';
import { TokenWorkspaceStatusEnum } from '../common/models/integration-token-status.enum';
import { EnrollmentProgressAluraExpectedDto } from '../dtos/expecteds/enrollment-progress-alura-expected.dto';
import { MirroredCourses } from '../entities/mirrored_courses.entity';
import { EnrollmentCompletedAluraExpectedDto } from '../dtos/expecteds/enrollment-completed-alura-expected.dto';
import { subMonths } from 'date-fns';

describe('SyncEnrollmentsService', () => {
  let service: SyncEnrollmentsService;
  let enrollmentsService: jest.Mocked<EnrollmentService>;
  let aluraIntegrationService: jest.Mocked<AluraIntegrationsService>;
  let mirroredCourseRepository: jest.Mocked<MirroredCoursesRepository>;
  let integrationTokensWorkspaceRepository: jest.Mocked<IntegrationTokenWorkspaceRepository>;
  const chance = new Chance();

  beforeEach(() => {
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  beforeEach(() => {
    enrollmentsService = { syncEnrollment: jest.fn() } as unknown as jest.Mocked<EnrollmentService>;
    aluraIntegrationService = {
      fetchEnrollmentProgress: jest.fn(),
      fetchCompletedEnrollments: jest.fn(),
    } as unknown as jest.Mocked<AluraIntegrationsService>;
    mirroredCourseRepository = { findBy: jest.fn() } as unknown as jest.Mocked<MirroredCoursesRepository>;
    integrationTokensWorkspaceRepository = {
      findBy: jest.fn(),
      findOneBy: jest.fn(),
    } as unknown as jest.Mocked<IntegrationTokenWorkspaceRepository>;

    service = new SyncEnrollmentsService(
      enrollmentsService,
      aluraIntegrationService,
      mirroredCourseRepository,
      integrationTokensWorkspaceRepository,
    );
  });

  describe('cronSyncProgress', () => {
    it('should synchronize the enrollments in progress for all the available workspaces', async () => {
      const workspaceId = chance.guid();
      const token = chance.guid();
      const missionId = chance.guid();
      const enrollmentEmail = chance.email();
      integrationTokensWorkspaceRepository.findBy.mockResolvedValueOnce([
        {
          workspaceId,
          token,
        },
      ] as IntegrationTokensWorkspace[]);
      aluraIntegrationService.fetchEnrollmentProgress.mockResolvedValueOnce([
        { idCurso: 123, email: enrollmentEmail, percentualProgresso: 10 },
      ] as EnrollmentProgressAluraExpectedDto[]);
      mirroredCourseRepository.findBy.mockResolvedValueOnce([{ missionId, courseId: '123' }] as MirroredCourses[]);
      const expectedEnrollmentToSync = {
        email: enrollmentEmail,
        mission_id: missionId,
        workspace_id: workspaceId,
        progress: 10,
      };

      await service.cronSyncProgress();

      expect(integrationTokensWorkspaceRepository.findBy).toHaveBeenCalledWith({
        status: TokenWorkspaceStatusEnum.ACTIVE,
      });
      expect(aluraIntegrationService.fetchEnrollmentProgress).toHaveBeenCalledWith({ token, startAt: null });
      expect(mirroredCourseRepository.findBy).toHaveBeenCalledWith({ workspaceId });
      expect(enrollmentsService.syncEnrollment).toHaveBeenCalledWith([expectedEnrollmentToSync], workspaceId);
    });
  });

  describe('cronSyncFinish', () => {
    it('should synchronize the finished enrollments for all the available workspaces', async () => {
      const workspaceId = chance.guid();
      const token = chance.guid();
      const missionId = chance.guid();
      const enrollmentEmail = chance.email();
      const certificateUrl = chance.url();
      integrationTokensWorkspaceRepository.findBy.mockResolvedValueOnce([
        {
          workspaceId,
          token,
        },
      ] as IntegrationTokensWorkspace[]);
      aluraIntegrationService.fetchCompletedEnrollments.mockResolvedValueOnce([
        { idCurso: 123, email: enrollmentEmail, percentualConclusao: 10, urlCertificado: certificateUrl },
      ] as EnrollmentCompletedAluraExpectedDto[]);
      mirroredCourseRepository.findBy.mockResolvedValueOnce([{ missionId, courseId: '123' }] as MirroredCourses[]);
      const expectedEnrollmentToSync = {
        email: enrollmentEmail,
        mission_id: missionId,
        workspace_id: workspaceId,
        progress: 10,
        certificate_url: certificateUrl,
      };

      await service.cronSyncFinish();

      expect(integrationTokensWorkspaceRepository.findBy).toHaveBeenCalledWith({
        status: TokenWorkspaceStatusEnum.ACTIVE,
      });
      expect(aluraIntegrationService.fetchCompletedEnrollments).toHaveBeenCalledWith({ token, startAt: null });
      expect(mirroredCourseRepository.findBy).toHaveBeenCalledWith({ workspaceId });
      expect(enrollmentsService.syncEnrollment).toHaveBeenCalledWith([expectedEnrollmentToSync], workspaceId);
    });
  });

  describe('forceEnrollmentsSyncForWorkspace', () => {
    it('should force the synchronization of both in progress and finished enrollments for the provided workspace for the last 3 months', async () => {
      const workspaceId = chance.guid();
      const integrationTokenValue = chance.guid();
      const workspaceIntegrationToken = { token: integrationTokenValue, workspaceId } as IntegrationTokensWorkspace;
      integrationTokensWorkspaceRepository.findOneBy.mockResolvedValue(workspaceIntegrationToken);
      const mockEnrollment = { idCurso: 123 };
      aluraIntegrationService.fetchEnrollmentProgress.mockResolvedValueOnce([
        mockEnrollment,
      ] as EnrollmentProgressAluraExpectedDto[]);
      aluraIntegrationService.fetchCompletedEnrollments.mockResolvedValueOnce([
        mockEnrollment,
      ] as EnrollmentCompletedAluraExpectedDto[]);
      mirroredCourseRepository.findBy.mockResolvedValue([{ missionId: '123', courseId: '123' }] as MirroredCourses[]);
      const threeMonthsAgo = subMonths(new Date(), 3).toISOString();

      await service.forceEnrollmentsSyncForWorkspace(workspaceId);

      expect(integrationTokensWorkspaceRepository.findOneBy).toHaveBeenCalledWith({
        status: TokenWorkspaceStatusEnum.ACTIVE,
        workspaceId,
      });
      expect(aluraIntegrationService.fetchEnrollmentProgress).toHaveBeenCalledWith({
        token: integrationTokenValue,
        startAt: threeMonthsAgo,
      });
      expect(aluraIntegrationService.fetchCompletedEnrollments).toHaveBeenCalledWith({
        token: integrationTokenValue,
        startAt: threeMonthsAgo,
      });
    });
  });
});
