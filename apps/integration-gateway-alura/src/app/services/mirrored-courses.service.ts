import { PageDto, PageOptionsDto, Sorting } from '@keeps-node-apis/@core';
import { Injectable, NotFoundException } from '@nestjs/common';
import { MirroredCoursesRepository } from '../common/repositories/mirrored-courses-repository.interface';
import { BatchDeleteDto } from '../dtos/batch-delete.dto';
import { MirroredCourseDto } from '../dtos/mirrored-course.dto';
import { CreateMirroredCourseDto } from '../dtos/mirrored-courses-create.dto';
import { MirroredCourseFilterDto } from '../dtos/mirrored-courses-filter.dto';
import { UpdateActiveStatusBatchDto } from '../dtos/update-active-status-batch.dto';
import { UpdateActiveStatusDto } from '../dtos/update-active-status.dto';
import { MirroredCourses } from '../entities/mirrored_courses.entity';
import { MissionService } from './konquest-rest/mission.service';

@Injectable()
export class MirroredCoursesService {
  constructor(
    private coursesRepository: MirroredCoursesRepository,
    private missionService: MissionService,
  ) {}

  async create(createCourseDto: CreateMirroredCourseDto): Promise<MirroredCourses> {
    return this.coursesRepository.save(createCourseDto);
  }

  getAll(
    filters: MirroredCourseFilterDto,
    pageOptions: PageOptionsDto,
    sort: Sorting,
    workspaceId: string,
  ): Promise<PageDto<MirroredCourseDto>> {
    return this.coursesRepository.findAll(filters, pageOptions, sort, workspaceId);
  }

  async getOne(id: string, workspaceId: string): Promise<MirroredCourses | undefined> {
    const mirroredCourse = await this.coursesRepository.findOne(id, workspaceId);
    if (!mirroredCourse) {
      throw new NotFoundException();
    }
    return {
      ...mirroredCourse,
      status: this.getCourseStatus(mirroredCourse),
    };
  }

  async getByCourseAndWorkspace(courseId: string, workspaceId: string) {
    return this.coursesRepository.findBy({ courseId, workspaceId, deletedAt: null });
  }

  async remove(id: string, workspaceId: string, token: string): Promise<MirroredCourses> {
    const decodedJwt = JSON.parse(atob(token.split('.')[1]));
    const mirroredCourse = await this.getOne(id, workspaceId);
    await this.missionService.deleteMission(mirroredCourse.missionId, workspaceId, token);
    const deletedInfo = `{ user_name: ${decodedJwt.name}, user_id: ${decodedJwt.sub} }`;
    const deletedAt = new Date();
    await this.coursesRepository.update(id, { deletedAt, deletedInfo });
    return { ...mirroredCourse, deletedAt, deletedInfo };
  }

  async removeByMissionId(missionId: string, workspaceId: string, token: string): Promise<MirroredCourses> {
    const mirroredCourse = await this.coursesRepository.findOneByMissionId(missionId, workspaceId);
    return await this.remove(mirroredCourse.id, workspaceId, token);
  }

  async removeBatch(batchDeleteDto: BatchDeleteDto, workspaceId: string, token: string) {
    await Promise.all(
      batchDeleteDto.ids.map(async (id) => {
        await this.remove(id, workspaceId, token);
      }),
    );
  }

  async updateActiveStatus(
    updateActiveStatusDto: UpdateActiveStatusDto,
    workspaceId: string,
  ): Promise<MirroredCourseDto> {
    const { courseId, isActive } = updateActiveStatusDto;
    const mirroredCourse = await this.getOne(courseId, workspaceId);
    await this.coursesRepository.update(courseId, { isActive });
    return MirroredCourseDto.fromMirroredCourse({ ...mirroredCourse, isActive });
  }

  async batchUpdateActiveStatus(updateActiveStatusBatchDto: UpdateActiveStatusBatchDto, workspaceId: string) {
    const { courseIds, isActive } = updateActiveStatusBatchDto;
    const operations = courseIds.map(
      async (id) =>
        await this.updateActiveStatus(
          {
            courseId: id,
            isActive,
          },
          workspaceId,
        ),
    );

    return await Promise.all(operations);
  }

  private getCourseStatus(mirroredCourse: MirroredCourses): string {
    return mirroredCourse.course.status !== 'PUBLISHED'
      ? mirroredCourse.course.status
      : mirroredCourse.mission.developmentStatus;
  }

  async getAllMirroredCoursesByCourseId(courseId: string): Promise<MirroredCourses[]> {
    return this.coursesRepository.findBy({ courseId });
  }

  async getCategories(workspaceId: string) {
    return this.coursesRepository
      .getCategories(workspaceId)
      .then((categories) => categories.map((category) => category.category));
  }

  getMirroredCourseByMissionId(missionId: string, workspaceId: string) {
    return this.coursesRepository.findOneByMissionId(missionId, workspaceId);
  }
}
