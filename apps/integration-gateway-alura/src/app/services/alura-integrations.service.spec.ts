import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { of } from 'rxjs';
import { TokenWorkspaceStatusEnum } from '../common/models/integration-token-status.enum';
import { CoursesCompletedFilterDTO } from '../dtos/courses-completed-filter.dto';
import { CoursesInputDTO } from '../dtos/courses-input.dto';
import { CoursesProgressFilterDTO } from '../dtos/courses-progress-filter.dto';
import { ValidateTokenDTO } from '../dtos/validate-token.dto';
import { AluraTokenName, IntegrationTokensWorkspace } from '../entities/integration_tokens_workspace.entity';
import { Workspace } from '../entities/workspace.entity';
import { NotificationService } from '../notification/notification.service';
import { AluraIntegrationsService, IntegrationTokenType } from './alura-integrations.service';
import { MyaccountUserService } from './myaccount-user.service';
import { SSOService } from './sso.service';
import { IntegrationTokenWorkspaceRepository } from '../common/repositories/integration-token-workspace.repository';
import { UpdateResult } from 'typeorm';
import { ListIntegrationTokenResponseDto } from '../dtos/list-integration-token-response-dto';
import { HTTP_CLIENT } from '@keeps-node-apis/@core';

jest.mock('../common/utils/date-utils.ts', () => ({
  convertISOToBrazilianFormat: jest.fn(),
}));

describe('AluraIntegrationsService', () => {
  let service: AluraIntegrationsService;
  let httpClientMock: any;
  let myaccountUserServiceMock: any;
  let notificationServiceMock: any;
  let mockIntegrationTokensWorkspaceRepository: jest.Mocked<IntegrationTokenWorkspaceRepository>;

  beforeEach(async () => {
    mockIntegrationTokensWorkspaceRepository = {
      find: jest.fn().mockResolvedValue([]),
      findByWorkspaceAndIntegration: jest.fn().mockResolvedValue([]),
      update: jest.fn().mockResolvedValue({}),
      save: jest.fn().mockResolvedValue({}),
    } as unknown as jest.Mocked<IntegrationTokenWorkspaceRepository>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AluraIntegrationsService,
        {
          provide: HTTP_CLIENT,
          useValue: {
            get: jest.fn().mockResolvedValue({}),
            post: jest.fn().mockResolvedValue({}),
          },
        },
        {
          provide: SSOService,
          useValue: {
            getUserAuthorizationToken: jest.fn(() => of(Promise.resolve('alura_sso'))),
          },
        },
        {
          provide: MyaccountUserService,
          useValue: {
            getWorkspaceAdmins: jest.fn(() => Promise.resolve([{ id: 1 }, { id: 2 }])),
          },
        },
        {
          provide: NotificationService,
          useValue: {
            createNotification: jest.fn(() => Promise.resolve(true)),
            sendEmailNotification: jest.fn(() => Promise.resolve(true)),
            sendIntegrationFailNotificationToAdmins: jest.fn(() => Promise.resolve(true)),
          },
        },
        {
          provide: getRepositoryToken(IntegrationTokensWorkspace),
          useValue: mockIntegrationTokensWorkspaceRepository,
        },
        {
          provide: getRepositoryToken(Workspace),
          useValue: {},
        },
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue({
              tokenAluraGetAllCourses: '2222',
              tokenAluraGetFinishedEnrollments: '3333',
              tokenAluraGetProgressEnrollments: '4444',
              tokenAluraSSO: '1111',
              aluraApiUrl: 'alura-api-endpoint',
            }),
          },
        },
      ],
    }).compile();

    service = module.get<AluraIntegrationsService>(AluraIntegrationsService);
    httpClientMock = module.get(HTTP_CLIENT);
    myaccountUserServiceMock = module.get(MyaccountUserService);
    notificationServiceMock = module.get(NotificationService);
    mockIntegrationTokensWorkspaceRepository = module.get(getRepositoryToken(IntegrationTokensWorkspace));
  });

  describe('getIntegrations', () => {
    it('should return a list of integrations', () => {
      const integrations = service.getIntegrations();
      expect(integrations).toEqual(['sso', 'courses', 'enrollment_progress', 'finished_enrollments']);
    });

    describe('validateIntegrationToken', () => {
      it('should return true if the token is valid', async () => {
        const tokenType: IntegrationTokenType = 'courses';
        const input: ValidateTokenDTO = { token: 'valid-token' };

        httpClientMock.get.mockResolvedValue({});

        const result = await service.validateIntegrationToken(tokenType, input);
        expect(result).toBe(true);
      });

      it('should return false if the token is invalid', async () => {
        const tokenType: IntegrationTokenType = 'courses';
        const input: ValidateTokenDTO = { token: 'invalid-token' };

        httpClientMock.get.mockRejectedValueOnce('Invalid token');

        const result = await service.validateIntegrationToken(tokenType, input);
        expect(result).toBe(false);
      });

      it('should throw an error if no token is provided', async () => {
        const tokenType: IntegrationTokenType = 'courses';
        const input: ValidateTokenDTO = { token: '' };

        await expect(service.validateIntegrationToken(tokenType, input)).rejects.toThrow('Token is Required');
      });
    });

    describe('fetchCourses', () => {
      it('should fetch courses successfully', async () => {
        const token = 'test-token';
        const mockResponse = [];

        httpClientMock.get.mockResolvedValueOnce(mockResponse);

        const result = await service.fetchCourses({ token });

        expect(httpClientMock.get).toHaveBeenCalledWith(`alura-api-endpoint/cursos`, { params: { token } });
        expect(result).toEqual(mockResponse);
      });

      it('should throw an error if no token is provided', async () => {
        const input: CoursesInputDTO = {} as any;

        try {
          await service.fetchCourses(input);
        } catch (error) {
          expect(error.message).toEqual('Token is Required');
        }
      });
    });

    describe('completed', () => {
      it('should fetch completed successfully', async () => {
        const token = 'test-token';
        const mockResponse = [];

        httpClientMock.get.mockResolvedValueOnce(mockResponse);

        const result = await service.fetchCompletedEnrollments({ token } as any);

        expect(httpClientMock.get).toHaveBeenCalledWith('alura-api-endpoint/conclusoes', { params: { token } });
        expect(result).toEqual(mockResponse);
      });

      it('should throw an error if no token is provided', async () => {
        const input: CoursesCompletedFilterDTO = {} as any;

        try {
          await service.fetchCompletedEnrollments(input);
        } catch (error) {
          expect(error.message).toEqual('Token is Required');
        }
      });
    });
    describe('progress', () => {
      it('should fetch progress successfully', async () => {
        const token = 'test-token';
        const mockResponse = [];

        httpClientMock.get.mockResolvedValueOnce(mockResponse);

        const result = await service.fetchEnrollmentProgress({ token } as any);

        expect(httpClientMock.get).toHaveBeenCalledWith('alura-api-endpoint/progresso', { params: { token } });
        expect(result).toEqual(mockResponse);
      });

      it('should throw an error if no token is provided', async () => {
        const input: CoursesProgressFilterDTO = {} as any;

        try {
          await service.fetchEnrollmentProgress(input);
        } catch (error) {
          expect(error.message).toEqual('Token is Required');
        }
      });
    });

    describe('checkSignatureTokens', () => {
      beforeEach(() => {
        service.sleep = jest.fn().mockImplementation();
      });

      it('should check signature tokens daily and handle errors correctly', async () => {
        httpClientMock.get.mockImplementationOnce(() => of({ data: [] }));

        await service.checkSignatureTokens();

        expect(httpClientMock.get).toHaveBeenCalledTimes(3);
      });

      it('should not validate the token if the api response is undefined', async () => {
        httpClientMock.get.mockImplementationOnce(() => of({ data: undefined }));

        await service.checkSignatureTokens();

        expect(mockIntegrationTokensWorkspaceRepository.save).not.toHaveBeenCalled();
      });

      it('should not validate the token if the api response is an empty array', async () => {
        httpClientMock.get.mockImplementationOnce(() => of({ data: [] }));

        await service.checkSignatureTokens();

        expect(mockIntegrationTokensWorkspaceRepository.save).not.toHaveBeenCalled();
      });

      it('should disable the token if the response is invalid', async () => {
        httpClientMock.get.mockImplementation(() => of({ error: '401 not authorized' }));
        const mockToken = {
          status: TokenWorkspaceStatusEnum.ACTIVE,
          workspaceId: 'mock_workspace_id',
        } as IntegrationTokensWorkspace;
        const expectedSavePayload = {
          status: TokenWorkspaceStatusEnum.CHANGED_SIGNATURE,
          workspaceId: 'mock_workspace_id',
        };
        mockIntegrationTokensWorkspaceRepository.find.mockResolvedValueOnce([mockToken]);

        await service.checkSignatureTokens();

        expect(mockIntegrationTokensWorkspaceRepository.save).toHaveBeenCalledWith(expectedSavePayload);
      });
    });

    it('should find all workspace tokens by workspaceId and integrationId', async () => {
      const workspaceId = 'someWorkspaceId';
      const expectedResult: ListIntegrationTokenResponseDto = {
        courses: '',
        enrollment_progress: '',
        finished_enrollments: '',
        sso: '',
      };
      const result = await service.findAllWorkspaceTokensByWorkspace(workspaceId);
      expect(result).toEqual(expectedResult);
    });
  });

  describe('sendIntegrationFailNotificationToAdmins', () => {
    it('should call the necessary services with correct parameters', async () => {
      const workspaceId = 'someWorkspaceId';
      await service.sendIntegrationFailNotificationToAdmins(workspaceId);

      expect(myaccountUserServiceMock.getWorkspaceAdmins).toHaveBeenCalledWith(workspaceId);
      expect(notificationServiceMock.createNotification).toHaveBeenCalled();
      expect(notificationServiceMock.sendEmailNotification).toHaveBeenCalled();
    });
  });

  describe('checkAllTokens', () => {
    it('should update token status to INVALID and notify admins if token is not valid', async () => {
      // Preparação dos mocks
      const token = 'token123';
      const workspaceId = 'workspaceId123';

      mockIntegrationTokensWorkspaceRepository.find.mockResolvedValue([
        {
          id: '1',
          workspaceId,
          status: TokenWorkspaceStatusEnum.ACTIVE,
          token: token,
          workspace: '',
          lastCheckedAt: undefined,
          tokenName: AluraTokenName.SSO,
        },
      ]);
      service.validateIntegrationToken = jest.fn(() => Promise.resolve(false));
      mockIntegrationTokensWorkspaceRepository.update.mockResolvedValue({} as UpdateResult);
      service.sendIntegrationFailNotificationToAdmins = jest.fn(() => Promise.resolve());
      service.sleep = jest.fn();

      await service.checkAllTokens();

      expect(mockIntegrationTokensWorkspaceRepository.find).toHaveBeenCalledWith({
        where: { status: TokenWorkspaceStatusEnum.ACTIVE },
      });
      expect(mockIntegrationTokensWorkspaceRepository.update).toHaveBeenCalledWith('1', {
        status: TokenWorkspaceStatusEnum.INVALID,
      });
      expect(service.sendIntegrationFailNotificationToAdmins).toHaveBeenCalledWith(workspaceId);
    });

    it('should not update token status or notify admins if token is valid', async () => {
      const token = 'token123';
      const workspaceId = 'workspaceId123';

      httpClientMock.get.mockImplementationOnce(() => of({}));
      mockIntegrationTokensWorkspaceRepository.find.mockResolvedValue([
        {
          id: '1',
          workspaceId,
          status: TokenWorkspaceStatusEnum.ACTIVE,
          token: token,
          workspace: '',
          lastCheckedAt: undefined,
          tokenName: AluraTokenName.COURSES,
        },
      ]);
      mockIntegrationTokensWorkspaceRepository.update.mockResolvedValue({} as UpdateResult);
      service.sleep = jest.fn();
      service.sendIntegrationFailNotificationToAdmins = jest.fn(() => Promise.resolve());

      await service.checkAllTokens();

      expect(httpClientMock.get).toHaveBeenCalledWith('alura-api-endpoint/cursos', { params: { token } });
      expect(mockIntegrationTokensWorkspaceRepository.find).toHaveBeenCalledWith({
        where: { status: TokenWorkspaceStatusEnum.ACTIVE },
      });
      expect(mockIntegrationTokensWorkspaceRepository.update).not.toHaveBeenCalled();
      expect(service.sendIntegrationFailNotificationToAdmins).not.toHaveBeenCalled();
    });
  });
});
