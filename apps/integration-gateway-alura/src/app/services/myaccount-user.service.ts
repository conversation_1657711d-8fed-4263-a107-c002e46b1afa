import { Inject, Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { HTTP_CLIENT, HttpClient, User } from '@keeps-node-apis/@core';

@Injectable()
export class MyaccountUserService {
  private apiUrl: string;
  private token: string;

  constructor(
    private configService: ConfigService,
    @Inject(HTTP_CLIENT) private http: HttpClient,
  ) {
    this.apiUrl = this.configService.get('OLD_MYACCOUNT_API_URL');
    this.token = this.configService.get('MYACCOUNT_SUPER_TOKEN');
  }

  async getWorkspaceAdmins(workspaceId: string) {
    const ROLES_ADMIN: string[] = ['297a88de-c34b-4661-be8a-7090fa9a89e5', 'c2a0da89-311d-4e4f-bf7b-c49d7c15f2b6'];
    let formattedQueryParams = '?';
    for (const role of ROLES_ADMIN) {
      formattedQueryParams += `role_id=${role}&`;
    }
    formattedQueryParams += `user__status=true`;

    try {
      return await this.http.get<User[]>(`${this.apiUrl}/users-roles?${formattedQueryParams}`, {
        headers: {
          Authorization: `${this.token}`,
          'x-client': workspaceId,
        },
      });
    } catch (e) {
      throw new Error(`Unable to retrieve users roles from myaccount`);
    }
  }
}
