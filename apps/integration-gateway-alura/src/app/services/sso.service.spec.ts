import { SSOInputDTO } from '../dtos/sso-input.dto';
import { SSOService } from './sso.service';
import { ConfigService } from '@nestjs/config';
import { HttpClient } from '@keeps-node-apis/@core';
import { Chance } from 'chance';

describe('SSOService', () => {
  let service: SSOService;
  let httpClientMock: jest.Mocked<HttpClient>;
  let configServiceMock: jest.Mocked<ConfigService>;
  const chance = new Chance();
  const aluraSsoUrl = chance.url();

  beforeEach(async () => {
    httpClientMock = {
      post: jest.fn(),
      get: jest.fn(),
    } as unknown as jest.Mocked<HttpClient>;

    configServiceMock = {
      get: jest.fn().mockReturnValue({ aluraSsoUrl }),
    } as unknown as jest.Mocked<ConfigService>;

    service = new SSOService(httpClientMock, configServiceMock);
  });

  describe('getUserAuthorizationToken', () => {
    it('should throw an error if client is not provided', () => {
      const input: SSOInputDTO = { token: '', email: '<EMAIL>' };
      expect(() => service.getUserAuthorizationToken(input)).toThrow('Client is Required');
    });

    it('should call rest.post with correct parameters', () => {
      const input: SSOInputDTO = { token: chance.guid(), email: chance.email() };

      service.getUserAuthorizationToken(input);

      expect(httpClientMock.post).toHaveBeenCalledWith(aluraSsoUrl, { client: input.token, userkey: input.email });
    });
  });
});
