import { CreateMirroredCourseDto } from '../dtos/mirrored-courses-create.dto';
import { MirroredCourseFilterDto } from '../dtos/mirrored-courses-filter.dto';

import { PageDto, PageOptionsDto, Sorting } from '@keeps-node-apis/@core';
import { NotFoundException } from '@nestjs/common';
import { Chance } from 'chance';
import { MirroredCoursesRepository } from '../common/repositories/mirrored-courses-repository.interface';
import { MirroredCourseDto } from '../dtos/mirrored-course.dto';
import { MirroredCourses } from '../entities/mirrored_courses.entity';
import { MissionService } from './konquest-rest/mission.service';
import { MirroredCoursesService } from './mirrored-courses.service';

describe('MirroredCoursesService', () => {
  let service: MirroredCoursesService;
  let repositoryMock: jest.Mocked<MirroredCoursesRepository>;
  let missionServiceMock: jest.Mocked<MissionService>;
  const chance = new Chance();
  const token =
    'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJSYzRyNmZMREJYWU1NcE00dS10OWhPOC1ob3hxNGhrdlVtcXo1WW9hQURzIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qDJFMAyaWOTZ2BKGvmp-eLyeFNooDRafl-k5sYhKlJwEhG77jewNgnz-C0bLmGeiOIEKGAh4WSv0z5pUsLee8dl-EJYx9DWA1bEqJDXXQmwF_wHvuqY6KPcXE4VzlY7pGEgl0RCTlUdP82uj4C_VqXNf6o4i4cP7puCu2xmi_0aQK9-ACIkdfKnUt1KEF90jtMPMoSvYr_KiST6_GW9K3xVhm71f1S8k9snPUlnOo07m4fqhW4u9xLPdijIsE9pylwSiYYEiTp0rCqP5-p7IIpsTu2IHXziGR6KrZDuZQ_WKdsX01v4OIImYt0-Llu5R2ijOir2PuZbO6QKkTdR8xg';

  beforeEach(async () => {
    repositoryMock = {
      save: jest.fn(),
      findAll: jest.fn(),
      findOne: jest.fn(),
      update: jest.fn(),
      getAllMirroredCoursesByCourseId: jest.fn(),
      findOneByMissionId: jest.fn(),
      getCategories: jest.fn(),
    } as unknown as jest.Mocked<MirroredCoursesRepository>;

    missionServiceMock = { deleteMission: jest.fn().mockResolvedValue({}) } as unknown as jest.Mocked<MissionService>;
    service = new MirroredCoursesService(repositoryMock, missionServiceMock);
  });

  describe('create', () => {
    it('should create a new mirrored course', async () => {
      const courseId = chance.guid();
      const missionId = chance.guid();
      const workspaceId = chance.guid();
      const createCourseDto: CreateMirroredCourseDto = { courseId, missionId, workspaceId };

      await service.create(createCourseDto);

      expect(repositoryMock.save).toHaveBeenCalledWith(createCourseDto);
    });
  });

  describe('getAll', () => {
    it('should return all mirrored courses in a paginated response', async () => {
      const filters = new MirroredCourseFilterDto();
      const pageOptions = new PageOptionsDto();
      const sortParams: Sorting = { property: 'name', direction: 'ASC' };
      const workspaceId = chance.guid();
      const mockResponse: PageDto<MirroredCourseDto> = {
        hasNextPage: false,
        hasPreviousPage: false,
        page: 1,
        total: 0,
        items: [],
      };
      repositoryMock.findAll.mockResolvedValue(mockResponse);

      const result = await service.getAll(filters, pageOptions, sortParams, workspaceId);

      expect(repositoryMock.findAll).toHaveBeenCalledWith(filters, pageOptions, sortParams, workspaceId);
      expect(result).toMatchObject(mockResponse);
    });
  });

  describe('getOne', () => {
    it('should return a mirrored course by id with the mission development status when the course status is PUBLISHED', async () => {
      const courseId = chance.guid();
      const workspaceId = chance.guid();
      const mockCourse = {
        id: courseId,
        course: { status: 'PUBLISHED' },
        mission: { developmentStatus: 'mock_development_status' },
      } as MirroredCourses;
      repositoryMock.findOne.mockResolvedValue(mockCourse);

      const result = await service.getOne(courseId, workspaceId);

      expect(repositoryMock.findOne).toHaveBeenCalledWith(courseId, workspaceId);
      expect(result.status).toEqual('mock_development_status');
    });

    it('should return a mirrored with the course status when it is different from PUBLISHED', async () => {
      const courseId = chance.guid();
      const workspaceId = chance.guid();
      const mockCourse = {
        id: courseId,
        course: { status: 'DISABLED' },
        mission: { developmentStatus: 'mock_development_status' },
      } as MirroredCourses;
      repositoryMock.findOne.mockResolvedValue(mockCourse);

      const result = await service.getOne(courseId, workspaceId);

      expect(repositoryMock.findOne).toHaveBeenCalledWith(courseId, workspaceId);
      expect(result.status).toEqual('DISABLED');
    });

    it('should throw an NotFoundException if the course does not exists', async () => {
      repositoryMock.findOne.mockResolvedValueOnce(undefined);
      const mockId = chance.guid();
      await expect(service.getOne(mockId, mockId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('remove', () => {
    it('should remove a mirrored course', async () => {
      const courseId = chance.guid();
      const missionId = chance.guid();
      const workspaceId = chance.guid();

      const decodedUserInfo = '{ user_name: Super Admin, user_id: bbf47825-8dfb-49bc-8ad8-f8adc775f95f }';
      const expectedDeletedCourse = {
        id: courseId,
        course: { status: 'DISABLED' },
        status: 'DISABLED',
        missionId,
        deletedInfo: decodedUserInfo,
      } as MirroredCourses;
      repositoryMock.findOne.mockResolvedValueOnce(expectedDeletedCourse);

      const result = await service.remove(courseId, workspaceId, token);

      expect(result).toMatchObject(
        expect.objectContaining({
          ...expectedDeletedCourse,
          deletedAt: expect.any(Date),
        }),
      );
      expect(repositoryMock.update).toHaveBeenCalledWith(
        courseId,
        expect.objectContaining({
          deletedInfo: decodedUserInfo,
          deletedAt: expect.any(Date),
        }),
      );
      expect(missionServiceMock.deleteMission).toHaveBeenCalledWith(missionId, workspaceId, token);
    });

    it('should throw an NotFoundException the course does not exist', async () => {
      repositoryMock.findOne.mockResolvedValueOnce(undefined);
      const mockId = chance.guid();
      await expect(service.remove(mockId, mockId, token)).rejects.toThrow(NotFoundException);
    });

    describe('updateActiveStatus', () => {
      it('should update the active status of an mirrored course', async () => {
        const courseId = chance.guid();
        const mirroredCourseId = chance.guid();
        const workspaceId = chance.guid();
        const expectedResult = {
          id: mirroredCourseId,
          courseId: courseId,
          createdAt: '2024-01-02',
          isActive: false,
          status: 'DISABLED',
        } as MirroredCourseDto;
        const mockCourse = {
          id: mirroredCourseId,
          courseId: courseId,
          course: { status: 'DISABLED' },
          created_date: new Date('2024-01-02T00:00:00'),
          mission: { developmentStatus: 'mock_development_status' },
        } as MirroredCourses;
        repositoryMock.findOne.mockResolvedValue(mockCourse);

        const result = await service.updateActiveStatus({ courseId, isActive: false }, workspaceId);

        expect(repositoryMock.update).toHaveBeenCalledWith(courseId, { isActive: false });
        expect(result).toMatchObject(expectedResult);
      });
    });

    describe('batchUpdateActiveStatus', () => {
      it('should update the active status of multiple mirrored courses', async () => {
        const mirroredCourseIds = [chance.guid(), chance.guid()];
        const courseIds = [chance.guid(), chance.guid()];
        const workspaceId = chance.guid();
        const expectedResult = [
          {
            id: mirroredCourseIds.at(0),
            courseId: courseIds.at(0),
            createdAt: '2024-01-02',
            isActive: false,
            status: 'DISABLED',
          },
          {
            id: mirroredCourseIds.at(1),
            courseId: courseIds.at(1),
            createdAt: '2024-01-02',
            isActive: false,
            status: 'DISABLED',
          },
        ] as MirroredCourseDto[];
        const mockCourses = [
          {
            id: mirroredCourseIds.at(0),
            courseId: courseIds.at(0),
            course: { status: 'DISABLED' },
            created_date: new Date('2024-01-02T00:00:00'),
            mission: { developmentStatus: 'mock_development_status' },
          },
          {
            id: mirroredCourseIds.at(1),
            courseId: courseIds.at(1),
            course: { status: 'DISABLED' },
            created_date: new Date('2024-01-02T00:00:00'),
            mission: { developmentStatus: 'mock_development_status' },
          },
        ] as MirroredCourses[];
        repositoryMock.findOne.mockImplementation((id) =>
          Promise.resolve(mockCourses.find((course) => course.id === id)),
        );

        const result = await service.batchUpdateActiveStatus(
          { courseIds: mirroredCourseIds, isActive: false },
          workspaceId,
        );
        expect(result).toMatchObject(expectedResult);
      });
    });
  });

  describe('removeByMissionId', () => {
    it('should remove a mirrored course using the associated missionID', async () => {
      const courseId = chance.guid();
      const missionId = chance.guid();
      const workspaceId = chance.guid();
      const removeSpy = jest
        .spyOn(service, 'remove')
        .mockImplementationOnce(() => Promise.resolve({} as MirroredCourses));
      const expectedMirroredCourse = {
        id: courseId,
        course: { status: 'DISABLED' },
        status: 'DISABLED',
        missionId,
      } as MirroredCourses;
      repositoryMock.findOneByMissionId.mockResolvedValueOnce(expectedMirroredCourse);

      await service.removeByMissionId(missionId, workspaceId, token);

      expect(removeSpy).toHaveBeenCalledWith(courseId, workspaceId, token);
    });
  });

  describe('getCategories', () => {
    it('should return categories', async () => {
      const mockCategories = [{ category: 'some-category' }];

      repositoryMock.getCategories.mockResolvedValue(mockCategories);

      const result = await service.getCategories('workspace');

      expect(result).toEqual(mockCategories.map((category) => category.category));
      expect(repositoryMock.getCategories).toHaveBeenCalledWith('workspace');
    });
  });
});
