import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { TokenWorkspaceStatusEnum } from '../common/models/integration-token-status.enum';
import { IntegrationTokenWorkspaceRepository } from '../common/repositories/integration-token-workspace.repository';
import { MirroredCoursesRepository } from '../common/repositories/mirrored-courses-repository.interface';
import { CoursesProgressFilterDTO } from '../dtos/courses-progress-filter.dto';
import { EnrollmentProviderDTO } from '../dtos/enrollment-provider.dto';
import { IntegrationTokensWorkspace } from '../entities/integration_tokens_workspace.entity';
import { AluraIntegrationsService } from './alura-integrations.service';
import { EnrollmentService } from './konquest-rest/enrollment.service';
import { subMonths } from 'date-fns';

@Injectable()
export class SyncEnrollmentsService {
  private logger = new Logger(SyncEnrollmentsService.name);

  constructor(
    private enrollmentService: EnrollmentService,
    private aluraIntegrationService: AluraIntegrationsService,
    private mirroredCourseRepository: MirroredCoursesRepository,
    @InjectRepository(IntegrationTokensWorkspace)
    private integrationTokensWorkspaceRepository: IntegrationTokenWorkspaceRepository,
  ) {}

  @Cron(CronExpression.EVERY_4_HOURS)
  async cronSyncProgress() {
    this.logger.debug('Syncing enrollments in progress...');
    await this.syncEnrollments();
    this.logger.debug('Finish syncing enrollments in progress');
  }

  @Cron(CronExpression.EVERY_HOUR)
  async cronSyncFinish() {
    this.logger.debug('Syncing enrollments finished...');
    await this.syncFinishEnrollments();
    this.logger.debug('Finish syncing enrollments finished');
  }

  async forceEnrollmentsSyncForWorkspace(workspaceId: string) {
    const integrationToken = await this.integrationTokensWorkspaceRepository.findOneBy({
      workspaceId,
      status: TokenWorkspaceStatusEnum.ACTIVE,
    });
    const threeMonthsAgo = subMonths(new Date(), 3).toISOString();
    await this.syncEnrollmentsInProgressForIntegrationToken(integrationToken, threeMonthsAgo);
    await this.syncFinishedEnrollmentsForIntegrationToken(integrationToken, threeMonthsAgo);
  }

  private async syncEnrollments() {
    const integrationTokens = await this.integrationTokensWorkspaceRepository.findBy({
      status: TokenWorkspaceStatusEnum.ACTIVE,
    });

    for (const token of integrationTokens) {
      this.logger.debug(`Sync enrollments in progress for workspace ID: ${token.workspaceId}`);
      await this.syncEnrollmentsInProgressForIntegrationToken(token);
    }
  }

  private async syncFinishEnrollments() {
    const integrationTokens = await this.integrationTokensWorkspaceRepository.findBy({
      status: TokenWorkspaceStatusEnum.ACTIVE,
    });

    for (const token of integrationTokens) {
      this.logger.debug(`Sync finished enrollments for workspace ID: ${token.workspaceId}`);
      await this.syncFinishedEnrollmentsForIntegrationToken(token);
    }
  }

  private async syncEnrollmentsInProgressForIntegrationToken(
    integrationToken: IntegrationTokensWorkspace,
    startDate: string = null,
  ) {
    const enrollmentsInProgress = await this.aluraIntegrationService.fetchEnrollmentProgress({
      token: integrationToken.token,
      startAt: startDate,
    } as CoursesProgressFilterDTO);

    this.logger.debug(
      `Synchronizing ${enrollmentsInProgress?.length} enrollments in progress for Alura integration token: ${integrationToken.token}`,
    );

    const mirroredCourses = await this.mirroredCourseRepository.findBy({ workspaceId: integrationToken.workspaceId });
    const mirroredCourseIds = mirroredCourses.map((course) => course.courseId);

    const enrollmentsWithMirroredCourses = enrollmentsInProgress.filter((enrollment) =>
      mirroredCourseIds.includes(enrollment.idCurso.toString()),
    );

    const enrollmentsProvider = enrollmentsWithMirroredCourses.map((enrollment) => ({
      email: enrollment.email,
      mission_id: mirroredCourses.find((course) => course.courseId === enrollment.idCurso.toString()).missionId,
      workspace_id: integrationToken.workspaceId,
      progress: enrollment.percentualProgresso,
    })) as EnrollmentProviderDTO[];

    await this.enrollmentService.syncEnrollment(enrollmentsProvider, integrationToken.workspaceId);
  }

  private async syncFinishedEnrollmentsForIntegrationToken(
    integrationToken: IntegrationTokensWorkspace,
    startDate: string = null,
  ) {
    const completedEnrollments = await this.aluraIntegrationService.fetchCompletedEnrollments({
      token: integrationToken.token,
      startAt: startDate,
    });

    this.logger.debug(
      `Synchronizing ${completedEnrollments?.length} finished enrollments for Alura integration token: ${integrationToken.token}`,
    );

    const mirroredCourses = await this.mirroredCourseRepository.findBy({ workspaceId: integrationToken.workspaceId });
    const mirroredCourseIds = mirroredCourses.map((course) => course.courseId);

    const enrollmentsWithMirroredCourses = completedEnrollments.filter((enrollment) =>
      mirroredCourseIds.includes(enrollment.idCurso.toString()),
    );

    const enrollmentsProvider = enrollmentsWithMirroredCourses.map((enrollment) => ({
      email: enrollment.email,
      mission_id: mirroredCourses.find((course) => course.courseId === enrollment.idCurso.toString()).missionId,
      workspace_id: integrationToken.workspaceId,
      progress: enrollment.percentualConclusao,
      certificate_url: enrollment.urlCertificado,
    })) as EnrollmentProviderDTO[];

    await this.enrollmentService.syncEnrollment(enrollmentsProvider, integrationToken.workspaceId);
  }
}
