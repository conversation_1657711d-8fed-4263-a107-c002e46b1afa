import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { of, throwError } from 'rxjs';
import CourseRepository from '../common/repositories/course.repository';
import { IntegrationTokenWorkspaceRepository } from '../common/repositories/integration-token-workspace.repository';
import { CreateCourseDto } from '../dtos/courses-create.dto';
import { CourseFilterDto } from '../dtos/courses-filter.dto';
import { UpdateCourseDto } from '../dtos/courses-update.dto';
import { Course } from '../entities/course.entity';
import { IntegrationTokensWorkspace } from '../entities/integration_tokens_workspace.entity';
import { AluraIntegrationsService } from './alura-integrations.service';
import { CoursesService } from './courses.service';
import { MissionService } from './konquest-rest/mission.service';
import { MirroredCoursesService } from './mirrored-courses.service';
import { PageOptionsDto } from '@keeps-node-apis/@core';
import { SSOService } from './sso.service';
import { MirroredCourses } from '../entities/mirrored_courses.entity';
import { Chance } from 'chance';
import { ConfigService } from '@nestjs/config';

describe('CoursesService', () => {
  let service: CoursesService;
  let repository: CourseRepository;
  let ssoServiceMock: jest.Mocked<SSOService>;
  let tokenWorkspaceRepositoryMock: jest.Mocked<IntegrationTokenWorkspaceRepository>;
  let mirroredCoursesServiceMock: jest.Mocked<MirroredCoursesService>;
  const chance = new Chance();
  const token =
    'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJSYzRyNmZMREJYWU1NcE00dS10OWhPOC1ob3hxNGhrdlVtcXo1WW9hQURzIn0.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.qDJFMAyaWOTZ2BKGvmp-eLyeFNooDRafl-k5sYhKlJwEhG77jewNgnz-C0bLmGeiOIEKGAh4WSv0z5pUsLee8dl-EJYx9DWA1bEqJDXXQmwF_wHvuqY6KPcXE4VzlY7pGEgl0RCTlUdP82uj4C_VqXNf6o4i4cP7puCu2xmi_0aQK9-ACIkdfKnUt1KEF90jtMPMoSvYr_KiST6_GW9K3xVhm71f1S8k9snPUlnOo07m4fqhW4u9xLPdijIsE9pylwSiYYEiTp0rCqP5-p7IIpsTu2IHXziGR6KrZDuZQ_WKdsX01v4OIImYt0-Llu5R2ijOir2PuZbO6QKkTdR8xg';

  beforeEach(async () => {
    mirroredCoursesServiceMock = {
      getAll: jest.fn(),
      getMirroredCourseByMissionId: jest.fn(),
    } as unknown as jest.Mocked<MirroredCoursesService>;

    ssoServiceMock = {
      getUserAuthorizationToken: jest.fn().mockReturnValue(of({})),
    } as unknown as jest.Mocked<SSOService>;

    tokenWorkspaceRepositoryMock = {
      findTokenByWorkspaceId: jest.fn().mockResolvedValue({
        token: 'wwtk',
      }),
    } as unknown as jest.Mocked<IntegrationTokenWorkspaceRepository>;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn().mockReturnValue({ aluraSingleSignOnUrl: 'http://example.com' }),
          },
        },
        CoursesService,
        {
          provide: AluraIntegrationsService,
          useValue: {},
        },
        {
          provide: getRepositoryToken(Course),
          useValue: {
            save: jest.fn(),
            findAll: jest.fn(),
            findOne: jest.fn(),
            findOneByOrFail: jest.fn(),
            update: jest.fn(),
            softDelete: jest.fn(),
            findOneOrFail: jest.fn(),
            getCategories: jest.fn(),
          },
        },
        {
          provide: MissionService,
          useValue: {
            updateMission: jest.fn(),
          },
        },
        {
          provide: MirroredCoursesService,
          useValue: mirroredCoursesServiceMock,
        },
        {
          provide: SSOService,
          useValue: ssoServiceMock,
        },
        {
          provide: getRepositoryToken(IntegrationTokensWorkspace),
          useValue: tokenWorkspaceRepositoryMock,
        },
      ],
    }).compile();

    service = module.get<CoursesService>(CoursesService);
    repository = module.get<CourseRepository>(getRepositoryToken(Course));
  });

  describe('create', () => {
    it('should create a course', async () => {
      const createCourseDto = {} as CreateCourseDto;
      const mockCourse = { ...createCourseDto, id: 'some-id' };

      (repository.save as jest.Mock).mockResolvedValue(mockCourse);

      const result = await service.create(createCourseDto);

      expect(result).toEqual(mockCourse);
      expect(repository.save).toHaveBeenCalledWith(createCourseDto);
    });
  });

  describe('getAll', () => {
    it('should return all courses', async () => {
      const filters: CourseFilterDto = {} as CourseFilterDto;
      const pageOptions = { page: 0, perPage: 0 } as PageOptionsDto;
      const mockCourses = [{ id: 'some-id' }];

      (repository.findAll as jest.Mock).mockResolvedValue(mockCourses);

      const result = await service.getAll(filters, pageOptions);

      expect(result).toEqual(mockCourses);
      expect(repository.findAll).toHaveBeenCalledWith(filters, pageOptions);
    });
  });

  describe('getOne', () => {
    it('should return a course by id', async () => {
      const mockCourse = { id: 'some-id' };
      (repository.findOne as jest.Mock).mockResolvedValue(mockCourse);
      await service.getOne('some-id');
      expect(repository.findOneOrFail).toHaveBeenCalledWith({ where: { id: 'some-id' } });
    });
  });

  describe('update', () => {
    it('should update a course', async () => {
      const updateCourseDto: UpdateCourseDto = {};
      const mockCourse = { id: 'some-id' };

      (repository.findOneByOrFail as jest.Mock).mockResolvedValue(mockCourse);
      (repository.update as jest.Mock).mockResolvedValue({ ...mockCourse, ...updateCourseDto });

      const result = await service.update('some-id', updateCourseDto);

      expect(result).toEqual({ ...mockCourse, ...updateCourseDto });
      expect(repository.findOneByOrFail).toHaveBeenCalledWith({ id: 'some-id' });
      expect(repository.update).toHaveBeenCalledWith('some-id', updateCourseDto);
    });
  });

  describe('remove', () => {
    it('should remove a course', async () => {
      const mockCourse = { id: 'some-id' };

      (repository.findOneByOrFail as jest.Mock).mockResolvedValue(mockCourse);
      (repository.softDelete as jest.Mock).mockResolvedValue(mockCourse);

      const result = await service.remove('some-id');

      expect(result).toEqual(mockCourse);
      expect(repository.findOneByOrFail).toHaveBeenCalledWith({ id: 'some-id' });
      expect(repository.softDelete).toHaveBeenCalledWith(mockCourse);
    });
  });

  describe('getUrlByMissionId', () => {
    it('should return the course url with SSO token for a user with an  Alura account', async () => {
      const courseIdStub = chance.guid();
      const courseUrlStub = chance.url();
      const mirroredCourseStub = { course: { courseId: courseIdStub, courseUrl: courseUrlStub } } as MirroredCourses;
      mirroredCoursesServiceMock.getMirroredCourseByMissionId.mockResolvedValueOnce(mirroredCourseStub);
      const authorization = '8136584';
      ssoServiceMock.getUserAuthorizationToken.mockResolvedValueOnce({ authorization });
      const { url } = await service.getCourseSSOUrl(courseIdStub, token, 'workspace');

      expect(url).toEqual(`http://example.com?authorization=${authorization}&content=${courseIdStub}`);
    });

    it('should return the course url without SSO token for a user without an Alura account', async () => {
      const courseIdStub = chance.guid();
      const courseUrlStub = chance.url();
      const mirroredCourseStub = { course: { courseId: courseIdStub, courseUrl: courseUrlStub } } as MirroredCourses;
      ssoServiceMock.getUserAuthorizationToken.mockRejectedValueOnce('User not found');
      mirroredCoursesServiceMock.getMirroredCourseByMissionId.mockResolvedValueOnce(mirroredCourseStub);

      const { url } = await service.getCourseSSOUrl('23', token, 'workspace');

      expect(url).toEqual(courseUrlStub);
    });

    describe('getCategories', () => {
      it('should return categories', async () => {
        const mockCategories = [{ category: 'some-category' }];

        repository.getCategories = jest.fn().mockResolvedValue(mockCategories);

        const result = await service.getCategories();

        expect(result).toEqual(mockCategories.map((category) => category.category));
        expect(repository.getCategories).toHaveBeenCalled();
      });
    });
  });
});
