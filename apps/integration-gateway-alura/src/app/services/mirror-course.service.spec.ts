import { ConfigService } from '@nestjs/config';
import MissionRepository from '../common/repositories/mission.repository';
import { MirroredCourses } from '../entities/mirrored_courses.entity';
import { CoursesService } from './courses.service';
import { MissionService } from './konquest-rest/mission.service';
import { MirrorCourseService } from './mirror-course.service';
import { MirroredCoursesService } from './mirrored-courses.service';
import { Chance } from 'chance';
import { Course } from '../entities/course.entity';
import { GlobalAppConfig } from '../config/app.config';
import { MissionCreateDTO } from '../dtos/mission-create.dto';

describe('MirrorCourseService', () => {
  const chance = new Chance();
  const mockAppConfig = {
    groupName: chance.name(),
    providerId: chance.guid(),
    userOwnerIntegrationId: chance.guid(),
  } as GlobalAppConfig;
  let mirrorCoursesService: MirrorCourseService;
  let missionService: jest.Mocked<MissionService>;
  let configService: jest.Mocked<ConfigService>;
  let coursesService: jest.Mocked<CoursesService>;
  let mirroredCoursesService: jest.Mocked<MirroredCoursesService>;
  let missionRepository: jest.Mocked<MissionRepository>;

  beforeEach(async () => {
    missionService = { createMission: jest.fn() } as unknown as jest.Mocked<MissionService>;
    configService = { get: jest.fn().mockReturnValue(mockAppConfig) } as unknown as jest.Mocked<ConfigService>;
    coursesService = { getOne: jest.fn() } as unknown as jest.Mocked<CoursesService>;
    mirroredCoursesService = {
      getByCourseAndWorkspace: jest.fn(),
      create: jest.fn(),
    } as unknown as jest.Mocked<MirroredCoursesService>;
    missionRepository = { save: jest.fn() } as unknown as jest.Mocked<MissionRepository>;

    mirrorCoursesService = new MirrorCourseService(
      configService,
      missionService,
      coursesService,
      mirroredCoursesService,
      missionRepository,
    );
  });

  describe('mirror course', () => {
    it('should return the existing if a course is mirrored a second time', async () => {
      const mirroredCourse = { id: chance.guid() } as MirroredCourses;
      const workspaceId = chance.guid();
      mirroredCoursesService.getByCourseAndWorkspace.mockResolvedValueOnce([mirroredCourse]);

      const result = await mirrorCoursesService.mirrorCourse(mirroredCourse.courseId, workspaceId);

      expect(mirroredCoursesService.getByCourseAndWorkspace).toHaveBeenCalledWith(mirroredCourse.courseId, workspaceId);
      expect(coursesService.getOne).not.toHaveBeenCalled();
      expect(result).toBe(mirroredCourse);
    });

    it('should create the mirrored course with the corresponding mission', async () => {
      const courseId = chance.guid();
      const workspaceId = chance.guid();
      const course = {
        name: chance.name(),
        id: chance.guid(),
        category: chance.name(),
        description: '',
        imageUrl: chance.url(),
        workload: 2,
        courseUrl: chance.url(),
      } as Course;
      const expectedMissionDto: MissionCreateDTO = {
        course_url: course.courseUrl,
        description: course.description,
        duration_time: 120,
        group_name: mockAppConfig.groupName,
        image_url: course.imageUrl,
        language: 'pt-BR',
        mission_category_name: course.category,
        name: course.name,
        provider_id: mockAppConfig.providerId,
        user_creator_id: mockAppConfig.userOwnerIntegrationId,
      };
      mirroredCoursesService.getByCourseAndWorkspace.mockResolvedValueOnce([]);
      coursesService.getOne.mockResolvedValueOnce(course);
      const mission = { id: chance.guid(), development_status: 'DONE' };
      missionService.createMission.mockResolvedValueOnce(mission);

      await mirrorCoursesService.mirrorCourse(courseId, workspaceId);

      expect(missionService.createMission).toHaveBeenCalledWith(expectedMissionDto, workspaceId);
      expect(missionRepository.save).toHaveBeenCalledWith({
        id: mission.id,
        userCreatorId: mockAppConfig.userOwnerIntegrationId,
        developmentStatus: mission.development_status,
      });
      expect(mirroredCoursesService.create).toHaveBeenCalledWith({ courseId, missionId: mission.id, workspaceId });
    });
  });
});
