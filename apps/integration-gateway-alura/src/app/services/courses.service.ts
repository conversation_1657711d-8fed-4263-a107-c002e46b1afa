import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import CourseRepository from '../common/repositories/course.repository';
import { IntegrationTokenWorkspaceRepository } from '../common/repositories/integration-token-workspace.repository';
import { CreateCourseDto } from '../dtos/courses-create.dto';
import { CourseFilterDto } from '../dtos/courses-filter.dto';
import { UpdateCourseDto } from '../dtos/courses-update.dto';
import { Course } from '../entities/course.entity';
import { IntegrationTokensWorkspace } from '../entities/integration_tokens_workspace.entity';
import { AluraIntegrationsService } from './alura-integrations.service';
import { MissionService } from './konquest-rest/mission.service';
import { MirroredCoursesService } from './mirrored-courses.service';
import { PageDto, PageOptionsDto } from '@keeps-node-apis/@core';
import { SSOService } from './sso.service';
import { ConfigService } from '@nestjs/config';
import { GlobalAppConfig } from '../config/app.config';

@Injectable()
export class CoursesService {
  config: GlobalAppConfig;
  private readonly logger: Logger;

  constructor(
    private service: AluraIntegrationsService,
    @InjectRepository(Course)
    private coursesRepository: CourseRepository,
    private missionService: MissionService,
    private mirroredCoursesService: MirroredCoursesService,
    private ssoService: SSOService,
    @InjectRepository(IntegrationTokensWorkspace)
    private integrationTokenWorkspaceRepository: IntegrationTokenWorkspaceRepository,
    configService: ConfigService,
  ) {
    this.config = configService.get<GlobalAppConfig>('config');
    this.logger = new Logger(CoursesService.name);
  }

  async create(createCourseDto: CreateCourseDto): Promise<Course> {
    return this.coursesRepository.save(createCourseDto);
  }

  async getAll(filters: CourseFilterDto, pageOptions: PageOptionsDto): Promise<PageDto<Course[]>> {
    return this.coursesRepository.findAll(filters, pageOptions);
  }

  async getOne(id: string): Promise<Course> {
    return this.coursesRepository.findOneOrFail({ where: { id } });
  }

  async update(id: string, updateCourseDto: UpdateCourseDto) {
    this.coursesRepository.findOneByOrFail({ id });
    return this.coursesRepository.update(id, updateCourseDto);
  }

  async remove(id: string) {
    const course = await this.coursesRepository.findOneByOrFail({ id });
    return this.coursesRepository.softDelete(course);
  }

  async createOrUpdateCourse(course: CreateCourseDto) {
    const courseExists = await this.coursesRepository.findOneBy({ courseId: course.courseId });
    if (courseExists) {
      return this.update(courseExists.id, course);
    }
    return this.create(course);
  }

  async checkCourseChangeStatus(id: string, status: string) {
    const course = await this.coursesRepository.findOneBy({ courseId: id });
    if (course?.status !== status && course?.status == 'DISABLED') {
      const mirroredCourses = await this.mirroredCoursesService.getAllMirroredCoursesByCourseId(id);
      mirroredCourses.forEach((mirroredCourse) => {
        this.missionService.updateMission(
          mirroredCourse.missionId,
          { is_active: false, id: mirroredCourse.missionId },
          mirroredCourse.workspaceId,
        );
      });
    }
  }

  async getCourseSSOUrl(courseId: string, userEmail: string, workspace_id: string): Promise<{ url: string }> {
    const mirroredCourse = await this.mirroredCoursesService.getMirroredCourseByMissionId(courseId, workspace_id);
    const ssoTokenWorkspace = await this.integrationTokenWorkspaceRepository.findTokenByWorkspaceId(workspace_id);

    if (!mirroredCourse) {
      throw new NotFoundException('Unable to retrieve course information');
    }

    if (!ssoTokenWorkspace) {
      throw new NotFoundException('Unable to retrieve workspace information');
    }

    try {
      const aluraAuth = await this.getAluraAuthorization(ssoTokenWorkspace.token, userEmail);
      if (aluraAuth?.authorization) {
        return {
          url: `${this.config.aluraSingleSignOnUrl}?authorization=${aluraAuth.authorization}&content=${mirroredCourse.course.courseId}`,
        };
      }
    } catch (e) {
      this.logger.debug(`Error while fetching SSO authorization token for user ${userEmail}`, e);
    }

    return { url: mirroredCourse?.course.courseUrl };
  }

  async getCategories() {
    return this.coursesRepository.getCategories().then((categories) => categories.map((category) => category.category));
  }

  private getAluraAuthorization(ssoTokenWorkspace: string, userEmail: string) {
    return this.ssoService.getUserAuthorizationToken({
      token: ssoTokenWorkspace,
      email: userEmail,
    });
  }
}
