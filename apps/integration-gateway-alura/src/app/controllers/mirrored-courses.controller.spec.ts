import { Test, TestingModule } from '@nestjs/testing';
import { MirroredCourseFilterDto } from '../dtos/mirrored-courses-filter.dto';
import { MirroredCoursesService } from '../services/mirrored-courses.service';
import MirroredCoursesController from './mirrored-courses.controller';
import { PageDto, PageOptionsDto, Sorting } from '@keeps-node-apis/@core';
import { Chance } from 'chance';
import { MirroredCourseDto } from '../dtos/mirrored-course.dto';
import { UpdateActiveStatusDto } from '../dtos/update-active-status.dto';
import { UpdateActiveStatusBatchDto } from '../dtos/update-active-status-batch.dto';

describe('MirroredCoursesController', () => {
  let controller: MirroredCoursesController;
  let mirroredCoursesServiceMock: jest.Mocked<MirroredCoursesService>;
  const chance = new Chance();

  beforeEach(async () => {
    mirroredCoursesServiceMock = {
      getAll: jest.fn(),
      create: jest.fn().mockResolvedValue({ id: 'mirroredCourse1' }),
      remove: jest.fn(),
      removeByMissionId: jest.fn(),
      updateActiveStatus: jest.fn(),
      batchUpdateActiveStatus: jest.fn(),
    } as unknown as jest.Mocked<MirroredCoursesService>;

    const module: TestingModule = await Test.createTestingModule({
      controllers: [MirroredCoursesController],
      providers: [
        {
          provide: MirroredCoursesService,
          useValue: mirroredCoursesServiceMock,
        },
      ],
    }).compile();

    controller = module.get<MirroredCoursesController>(MirroredCoursesController);
  });

  describe('getAll', () => {
    it('must return all mirrored courses', async () => {
      const mockGetAllResponse: PageDto<MirroredCourseDto> = {
        hasNextPage: false,
        hasPreviousPage: false,
        page: 1,
        total: 2,
        items: [{ id: 'mirroredCourse1' }, { id: 'mirroredCourse2' }] as MirroredCourseDto[],
      };
      mirroredCoursesServiceMock.getAll.mockResolvedValueOnce(mockGetAllResponse);
      const filters = {} as MirroredCourseFilterDto;
      const pageOptions = new PageOptionsDto();
      const workspaceId = chance.guid();
      const sortParams: Sorting = { property: 'name', direction: 'ASC' };

      const response = await controller.getAll(workspaceId, filters, pageOptions, sortParams);

      expect(response).toBe(mockGetAllResponse);
      expect(mirroredCoursesServiceMock.getAll).toHaveBeenCalledTimes(1);
      expect(mirroredCoursesServiceMock.getAll).toHaveBeenCalledWith(filters, pageOptions, sortParams, workspaceId);
    });
  });

  describe('remove', () => {
    it('should call remove method with correct parameters', async () => {
      const id = 'testId';
      const workspaceId = 'workspace123';
      const token = 'Bearer testToken';
      await controller.remove(id, workspaceId, token);
      expect(mirroredCoursesServiceMock.remove).toHaveBeenCalledWith(id, workspaceId, token.replace('Bearer ', ''));
    });
  });

  describe('removeByMissionId', () => {
    it('should call removeByMission id with the correct arguments', async () => {
      const missionId = chance.guid();
      const workspaceId = chance.guid();
      const expectedToken = chance.guid();
      const token = `Bearer ${expectedToken}`;

      await controller.removeByMission(missionId, workspaceId, token);

      expect(mirroredCoursesServiceMock.removeByMissionId).toHaveBeenCalledWith(missionId, workspaceId, expectedToken);
    });
  });

  describe('updateActiveStatus', () => {
    it('should call updateActiveStatus', async () => {
      const workspaceId = chance.guid();
      const courseId = chance.guid();
      const mockPayload: UpdateActiveStatusDto = { courseId, isActive: false };

      await controller.updateActiveStatus(mockPayload, workspaceId);

      expect(mirroredCoursesServiceMock.updateActiveStatus).toHaveBeenCalledWith(mockPayload, workspaceId);
    });
  });

  describe('batchUpdateActiveStatus', () => {
    it('should call batchUpdateActiveStatus', async () => {
      const workspaceId = chance.guid();
      const courseIds = [chance.guid(), chance.guid()];
      const mockPayload: UpdateActiveStatusBatchDto = { courseIds, isActive: false };

      await controller.batchUpdateActiveStatus(mockPayload, workspaceId);

      expect(mirroredCoursesServiceMock.batchUpdateActiveStatus).toHaveBeenCalledWith(mockPayload, workspaceId);
    });
  });
});
