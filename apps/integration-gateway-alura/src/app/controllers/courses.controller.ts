import { Body, Controller, Get, Headers, Param, Post, Query } from '@nestjs/common';
import { CourseFilterDto } from '../dtos/courses-filter.dto';
import { CoursesService } from '../services/courses.service';
import { MirrorCourseService } from '../services/mirror-course.service';
import { PageOptionsDto } from '@keeps-node-apis/@core';

@Controller('courses')
export default class CoursesController {
  constructor(
    private coursesService: CoursesService,
    private mirrorService: MirrorCourseService,
  ) {}

  @Get()
  getAll(@Query() filters: CourseFilterDto, @Query() pageOptions: PageOptionsDto) {
    return this.coursesService.getAll(filters, pageOptions);
  }

  @Get('categories')
  getCategories() {
    return this.coursesService.getCategories();
  }

  @Get(':id')
  getOne(@Param('id') id: string) {
    return this.coursesService.getOne(id);
  }

  @Post(':id/mirror')
  mirror(@Param('id') courseId: string, @Headers('x-client') workspaceId: string) {
    return this.mirrorService.mirrorCourse(courseId, workspaceId);
  }

  @Post('mirror/batch')
  async mirrorBatch(@Body('courseIds') courseIds: string[], @Headers('x-client') workspaceId: string) {
    return await this.mirrorService.mirrorCourseBatch(courseIds, workspaceId);
  }

  @Get(':id/sso-link/:email')
  getUrlByMissionId(
    @Headers('x-client') workspaceId: string,
    @Param('id') missionId: string,
    @Param('email') email: string,
  ) {
    return this.coursesService.getCourseSSOUrl(missionId, email, workspaceId);
  }
}
