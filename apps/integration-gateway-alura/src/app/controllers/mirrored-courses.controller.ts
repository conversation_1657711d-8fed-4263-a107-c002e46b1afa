import { Body, Controller, Delete, Get, Headers, Param, Post, Query } from '@nestjs/common';

import { PageDto, PageOptionsDto, Sorting, SortingParams } from '@keeps-node-apis/@core';
import { BatchDeleteDto } from '../dtos/batch-delete.dto';
import { MirroredCourseDto } from '../dtos/mirrored-course.dto';
import { MirroredCourseFilterDto } from '../dtos/mirrored-courses-filter.dto';
import { MirroredCoursesService } from '../services/mirrored-courses.service';
import { UpdateActiveStatusDto } from '../dtos/update-active-status.dto';
import { UpdateActiveStatusBatchDto } from '../dtos/update-active-status-batch.dto';

@Controller('mirrored-courses')
export default class MirroredCoursesController {
  constructor(private coursesService: MirroredCoursesService) {}

  @Get()
  async getAll(
    @Headers('x-client') workspaceId: string,
    @Query() filters: MirroredCourseFilterDto,
    @Query() pageOptions: PageOptionsDto,
    @SortingParams(['createdAt', 'name', 'category']) sort?: Sorting,
  ): Promise<PageDto<MirroredCourseDto>> {
    return await this.coursesService.getAll(filters, pageOptions, sort, workspaceId);
  }

  @Get('categories')
  getCategories(@Headers('x-client') workspaceId: string) {
    return this.coursesService.getCategories(workspaceId);
  }

  @Get(':id')
  getOne(@Param('id') id: string, @Headers('x-client') workspaceId: string) {
    return this.coursesService.getOne(id, workspaceId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Headers('x-client') workspaceId: string, @Headers('authorization') token: string) {
    return this.coursesService.remove(id, workspaceId, token.replace('Bearer ', ''));
  }

  @Post('delete-batch')
  removeBatch(
    @Body() batchDeleteDto: BatchDeleteDto,
    @Headers('x-client') workspaceId: string,
    @Headers('authorization') token: string,
  ) {
    return this.coursesService.removeBatch(batchDeleteDto, workspaceId, token.replace('Bearer ', ''));
  }

  @Delete('by-mission-id/:id')
  removeByMission(
    @Param('id') id: string,
    @Headers('x-client') workspaceId: string,
    @Headers('authorization') token: string,
  ) {
    return this.coursesService.removeByMissionId(id, workspaceId, token.replace('Bearer ', ''));
  }

  @Post('update-active-status')
  updateActiveStatus(@Body() updateActiveStatusDto: UpdateActiveStatusDto, @Headers('x-client') workspaceId: string) {
    return this.coursesService.updateActiveStatus(updateActiveStatusDto, workspaceId);
  }

  @Post('batch-update-active-status')
  batchUpdateActiveStatus(
    @Body() updateActiveStatusBatchDto: UpdateActiveStatusBatchDto,
    @Headers('x-client') workspaceId: string,
  ) {
    return this.coursesService.batchUpdateActiveStatus(updateActiveStatusBatchDto, workspaceId);
  }
}
