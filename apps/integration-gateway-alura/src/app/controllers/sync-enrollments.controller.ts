import { <PERSON>, Head<PERSON>, HttpC<PERSON>, HttpStatus, Post } from '@nestjs/common';
import { SyncEnrollmentsService } from '../services/sync-enrollments';
import { ApiResponse } from '@nestjs/swagger';

@Controller('sync-enrollments')
export class SyncEnrollmentsController {
  constructor(private syncEnrollmentsService: SyncEnrollmentsService) {}

  @Post()
  @HttpCode(HttpStatus.ACCEPTED)
  @ApiResponse({
    status: HttpStatus.ACCEPTED,
    description:
      'Forces the synchronization of the finished and in progress enrollments of the last 3 months for the workspace',
  })
  forceSynchronization(@Headers('x-client') workspaceId: string) {
    return this.syncEnrollmentsService.forceEnrollmentsSyncForWorkspace(workspaceId);
  }
}
