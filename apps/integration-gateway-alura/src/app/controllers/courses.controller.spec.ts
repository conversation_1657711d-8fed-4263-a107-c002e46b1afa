import { PageOptionsDto } from '@keeps-node-apis/@core';
import { Test, TestingModule } from '@nestjs/testing';
import { CourseFilterDto } from '../dtos/courses-filter.dto';
import { CoursesService } from '../services/courses.service';
import { MirrorCourseService } from '../services/mirror-course.service';
import CoursesController from './courses.controller';

describe('CoursesController', () => {
  let controller: CoursesController;
  let service: CoursesService;
  let mirrorService: MirrorCourseService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CoursesController],
      providers: [
        {
          provide: CoursesService,
          useValue: {
            getAll: jest.fn().mockResolvedValue([{ id: 'course1' }, { id: 'course2' }]),
            create: jest.fn().mockResolvedValue({ id: 'course1' }),
          },
        },
        {
          provide: MirrorCourseService,
          useValue: {
            mirrorCourseBatch: jest.fn().mockResolvedValue({ errors: [] }),
          },
        },
      ],
    }).compile();

    controller = module.get<CoursesController>(CoursesController);
    service = module.get<CoursesService>(CoursesService);
    mirrorService = module.get<MirrorCourseService>(MirrorCourseService);
  });

  describe('getAll', () => {
    it('must return all courses', async () => {
      const filters = {} as CourseFilterDto;
      const pageOptions = { page: 0, perPage: 0 } as PageOptionsDto;
      expect(await controller.getAll(filters, pageOptions)).toEqual([{ id: 'course1' }, { id: 'course2' }]);
      expect(service.getAll).toHaveBeenCalledTimes(1);
      expect(service.getAll).toHaveBeenCalledWith(filters, pageOptions);
    });
  });

  describe('mirrorBatch', () => {
    it('must mirror multiple courses in batch', async () => {
      const courseIds = ['course1', 'course2'];
      const workspaceId = 'workspace123';

      expect(await controller.mirrorBatch(courseIds, workspaceId)).toEqual({ errors: [] });
      expect(mirrorService.mirrorCourseBatch).toHaveBeenCalledTimes(1);
      expect(mirrorService.mirrorCourseBatch).toHaveBeenCalledWith(courseIds, workspaceId);
    });
  });
});
