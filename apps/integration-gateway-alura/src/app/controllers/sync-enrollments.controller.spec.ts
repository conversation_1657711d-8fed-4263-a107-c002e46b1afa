import { SyncEnrollmentsController } from './sync-enrollments.controller';
import { SyncEnrollmentsService } from '../services/sync-enrollments';
import { Chance } from 'chance';

describe('EnrollmentsController', () => {
  let controller: SyncEnrollmentsController;
  let syncEnrollmentsService: jest.Mocked<SyncEnrollmentsService>;
  const chance = new Chance();

  beforeEach(async () => {
    syncEnrollmentsService = {
      forceEnrollmentsSyncForWorkspace: jest.fn(),
    } as unknown as jest.Mocked<SyncEnrollmentsService>;

    controller = new SyncEnrollmentsController(syncEnrollmentsService);
  });

  it('should force the synchronization of the enrollments for the provided workspace', () => {
    const workspaceId = chance.guid();
    controller.forceSynchronization(workspaceId);

    expect(syncEnrollmentsService.forceEnrollmentsSyncForWorkspace).toHaveBeenCalledWith(workspaceId);
  });
});
