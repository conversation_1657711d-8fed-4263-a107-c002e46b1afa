import TokensController from './tokens.controller';
import { AluraIntegrationsService } from '../services/alura-integrations.service';
import { IntegrationTokenDto } from '../dtos/integration-token.dto';
import {
  IntegrationTokenWorkspaceCreateDto,
  IntegrationTokenWorkspaceDto,
} from '../dtos/integration-token-workspace.dto';
import { IntegrationTokenCreateResponseDto } from '../dtos/integration-token-create-response.dto';

describe('TokensController', () => {
  let controller: TokensController;
  let aluraIntegrationServiceMock: jest.Mocked<AluraIntegrationsService>;
  const mockIntegrationTokens = [{ id: 'token1' }, { id: 'token2' }] as IntegrationTokenDto[];
  const mockWorkspaceTokens = [{ id: 'token1' }, { id: 'token2' }] as IntegrationTokenWorkspaceDto[];
  const mockCreateWorkspaceTokensResponse: IntegrationTokenCreateResponseDto = {
    success: [
      {
        token_name: 'sso',
        value: 'mock_token',
        isValid: true,
      },
    ],
    errors: [],
  };

  beforeEach(async () => {
    aluraIntegrationServiceMock = {
      findAllIntegrationTokens: jest.fn().mockResolvedValue(mockIntegrationTokens),
      findAllWorkspaceTokensByWorkspace: jest.fn().mockResolvedValue(mockWorkspaceTokens),
      createWorkspaceToken: jest.fn().mockResolvedValue(mockCreateWorkspaceTokensResponse),
    } as unknown as jest.Mocked<AluraIntegrationsService>;
    controller = new TokensController(aluraIntegrationServiceMock);
  });

  describe('findByWorkspaceId', () => {
    it('deve retornar os tokens de integração do workspace', async () => {
      const result = await controller.findByWorkspaceId('mock_workspace_id');

      expect(result).toBe(mockWorkspaceTokens);
      expect(aluraIntegrationServiceMock.findAllWorkspaceTokensByWorkspace).toHaveBeenCalledWith('mock_workspace_id');
    });
  });

  describe('createWorkspaceTokens', () => {
    it('should create new workspace tokens', async () => {
      const mockWorkspaceId = 'mock_workspace_id';
      const mockPayload = {} as IntegrationTokenWorkspaceCreateDto;

      const result = await controller.createWorkspaceTokens(mockWorkspaceId, mockPayload);

      expect(result).toBe(mockCreateWorkspaceTokensResponse);
      expect(aluraIntegrationServiceMock.createWorkspaceToken).toHaveBeenCalledWith(mockWorkspaceId, mockPayload);
    });
  });

  describe('updateWorkspaceTokens', () => {
    it('should update a workspace integration tokens', async () => {
      const mockWorkspaceId = 'mock_workspace_id';
      const mockPayload = {} as IntegrationTokenWorkspaceCreateDto;

      const result = await controller.updateWorkspaceTokens(mockWorkspaceId, mockPayload);

      expect(result).toBe(mockCreateWorkspaceTokensResponse);
      expect(aluraIntegrationServiceMock.createWorkspaceToken).toHaveBeenCalledWith(mockWorkspaceId, mockPayload);
    });
  });
});
