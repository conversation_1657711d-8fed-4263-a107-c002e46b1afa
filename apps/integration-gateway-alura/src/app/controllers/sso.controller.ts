import { Body, Controller, Post } from '@nestjs/common';
import { SSOService } from '../services/sso.service';
import { SSOInputDTO } from '../dtos/sso-input.dto';

@Controller('sso')
export class SSOController {
  constructor(private ssoService: SSOService) {}

  @Post('authorization-token')
  getAuthorizationToken(@Body() payload: SSOInputDTO) {
    return this.ssoService.getUserAuthorizationToken(payload);
  }
}
