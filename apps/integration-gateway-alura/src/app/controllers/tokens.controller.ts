import { Body, Controller, Get, Headers, Post, Put } from '@nestjs/common';
import { KONQUEST_ADMIN_ROLES, Roles } from '@keeps-node-apis/@core';
import { ApiOkResponse } from '@nestjs/swagger';
import {
  IntegrationTokenWorkspaceCreateDto,
  IntegrationTokenWorkspaceDto,
} from '../dtos/integration-token-workspace.dto';
import { AluraIntegrationsService } from '../services/alura-integrations.service';

@Controller('tokens')
export default class TokensController {
  constructor(private tokensService: AluraIntegrationsService) {}

  @Get('workspace')
  @Roles(KONQUEST_ADMIN_ROLES)
  @ApiOkResponse({ type: IntegrationTokenWorkspaceDto })
  findByWorkspaceId(@Headers('x-client') workspaceId: string) {
    return this.tokensService.findAllWorkspaceTokensByWorkspace(workspaceId);
  }

  @Post('workspace')
  @Roles(KONQUEST_ADMIN_ROLES)
  @ApiOkResponse({ type: IntegrationTokenWorkspaceDto })
  createWorkspaceTokens(@Headers('x-client') workspaceId: string, @Body() body: IntegrationTokenWorkspaceCreateDto) {
    return this.tokensService.createWorkspaceToken(workspaceId, body);
  }

  @Put('workspace')
  @Roles(KONQUEST_ADMIN_ROLES)
  @ApiOkResponse({ type: IntegrationTokenWorkspaceDto })
  updateWorkspaceTokens(@Headers('x-client') workspaceId: string, @Body() body: IntegrationTokenWorkspaceCreateDto) {
    return this.tokensService.createWorkspaceToken(workspaceId, body);
  }
}
