import { Body, Controller, Get, Param, Post } from '@nestjs/common';
import { AluraIntegrationsService, IntegrationTokenType } from '../services/alura-integrations.service';
import { ValidateTokenDTO } from '../dtos/validate-token.dto';
import { ApiSecurity } from '@nestjs/swagger';

@Controller('integrations')
@ApiSecurity('x-client')
@ApiSecurity('Authorization')
export default class IntegrationsController {
  constructor(private integrationService: AluraIntegrationsService) {}

  @Get()
  integrations() {
    return this.integrationService.getIntegrations();
  }

  @Post(':integrationName/validate')
  validateIntegrationToken(
    @Param('integrationName') integrationName: IntegrationTokenType,
    @Body() payload: ValidateTokenDTO,
  ) {
    return this.integrationService.validateIntegrationToken(integrationName, payload);
  }
}
