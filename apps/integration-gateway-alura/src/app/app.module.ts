import { MiddlewareConsumer, Module } from '@nestjs/common';

import { AuthModule, EnforceJsonMiddleware, RestModule, ValidateXClientHeaderMiddleware } from '@keeps-node-apis/@core';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule, TypeOrmModuleOptions } from '@nestjs/typeorm';
import { KeycloakConnectConfig, KeycloakConnectModule } from 'nest-keycloak-connect';
import PROVIDERS from './app.providers';
import { AppConfig, AuthConfig, DatabaseConfig } from './config';
import CoursesController from './controllers/courses.controller';
import IntegrationsController from './controllers/integrations.controller';
import { SSOController } from './controllers/sso.controller';
import TokensController from './controllers/tokens.controller';
import { IntegrationTokensWorkspace } from './entities/integration_tokens_workspace.entity';
import { Workspace } from './entities/workspace.entity';

import MirroredCoursesController from './controllers/mirrored-courses.controller';
import { Course } from './entities/course.entity';
import { MirroredCourses } from './entities/mirrored_courses.entity';
import { Mission } from './entities/mission.entity';
import { SyncEnrollmentsController } from './controllers/sync-enrollments.controller';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [AppConfig, DatabaseConfig, AuthConfig],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        ...configService.get<TypeOrmModuleOptions>('database'),
      }),
    }),
    TypeOrmModule.forFeature([IntegrationTokensWorkspace, Workspace, Course, MirroredCourses, Mission]),
    KeycloakConnectModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<KeycloakConnectConfig>('auth'),
      }),
    }),
    AuthModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        return { authAppId: configService.get('KONQUEST_APPLICATION_ID') };
      },
    }),
    ScheduleModule.forRoot(),
    RestModule,
  ],
  controllers: [
    IntegrationsController,
    SSOController,
    CoursesController,
    TokensController,
    MirroredCoursesController,
    SyncEnrollmentsController,
  ],
  providers: PROVIDERS,
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ValidateXClientHeaderMiddleware).forRoutes(TokensController, IntegrationsController);
    consumer.apply(EnforceJsonMiddleware).forRoutes('*');
  }
}
