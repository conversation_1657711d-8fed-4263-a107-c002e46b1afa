import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';

export class IntegrationTokenDto {
  @ApiProperty({ type: String })
  @Expose()
  id: string;

  @ApiProperty({ type: String })
  @Expose()
  tokenName: string;

  @ApiProperty({ type: Boolean })
  @Expose()
  required: boolean;

  @ApiProperty({ type: String })
  @Expose()
  apiUrl: string;

  @ApiProperty({ type: String })
  @Expose()
  integrationId: string;
}
