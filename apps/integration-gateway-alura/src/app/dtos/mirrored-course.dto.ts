import { MirroredCourses } from '../entities/mirrored_courses.entity';
import { format } from 'date-fns';

export class MirroredCourseDto {
  static fromMirroredCourse(mirroredCourse: MirroredCourses): MirroredCourseDto {
    return {
      name: mirroredCourse.course.name,
      id: mirroredCourse.id,
      courseId: mirroredCourse.courseId,
      createdAt: format(mirroredCourse.created_date, 'yyyy-MM-dd'),
      category: mirroredCourse.course.category,
      link: mirroredCourse.course.courseUrl,
      status:
        mirroredCourse.course.status !== 'PUBLISHED'
          ? mirroredCourse.course.status
          : mirroredCourse.mission.developmentStatus,
      missionId: mirroredCourse.missionId,
      isActive: mirroredCourse.isActive,
    };
  }

  name: string;
  id: string;
  courseId: string;
  createdAt: string;
  category: string;
  link: string;
  status: string;
  missionId: string;
  isActive: boolean;
}
