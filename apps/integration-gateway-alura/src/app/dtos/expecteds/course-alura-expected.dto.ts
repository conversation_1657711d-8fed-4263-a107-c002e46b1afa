import { IsString, IsNumber } from 'class-validator';

export class CourseAluraExpectedDto {
  @IsNumber()
  id: number;

  @IsString()
  nome: string;

  @IsString()
  codigo: string;

  @IsString()
  url: string;

  @IsString()
  imagem: string;

  @IsString()
  estado: string;

  @IsString()
  descricao: string;

  @IsNumber()
  cargaHoraria: number;

  @IsNumber()
  porcentagemMinimaParaConclusao: number;

  @IsString()
  categoria: string;

  @IsString()
  subcategoria: string;

  @IsString()
  dataLancamento: string;
}
