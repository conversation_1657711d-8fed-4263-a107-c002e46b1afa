import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { IsNotEmpty } from 'class-validator';
import { TokenWorkspaceStatusEnum } from '../common/models/integration-token-status.enum';

export class IntegrationTokenWorkspaceDto {
  @ApiProperty({ type: String })
  @Expose()
  id: string;

  @ApiProperty({ type: String })
  @Expose()
  workspaceId: string;

  @ApiProperty({ type: String })
  @Expose()
  integrationToken: string;

  @ApiProperty({ type: Date })
  @Expose()
  lastCheckedAt: Date;

  @ApiProperty({ type: String })
  @Expose()
  status: TokenWorkspaceStatusEnum;

  @ApiProperty({ type: String })
  @Expose()
  token: string;
}

export class IntegrationTokenWorkspaceCreateDto {
  @ApiProperty({ type: String })
  @Expose()
  @IsNotEmpty()
  courses: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsNotEmpty()
  sso: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsNotEmpty()
  enrollment_progress: string;

  @ApiProperty({ type: String })
  @Expose()
  @IsNotEmpty()
  finished_enrollments: string;
}
