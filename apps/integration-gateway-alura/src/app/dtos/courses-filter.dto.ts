import { IsOptional, IsString, IsUUID } from 'class-validator';
import { ToArray } from '@keeps-node-apis/@core';
import { Type } from 'class-transformer';

export class CourseFilterDto {
  @IsOptional()
  created_date_lte: Date;

  @IsOptional()
  created_date_gte: Date;

  @IsOptional()
  @IsUUID()
  integrationId: string;

  @IsOptional()
  @Type(() => Array<string>)
  @ToArray()
  category: string[];

  @IsOptional()
  @Type(() => Array<string>)
  @ToArray()
  status: string[];

  @IsOptional()
  @IsString()
  name: string;
}
