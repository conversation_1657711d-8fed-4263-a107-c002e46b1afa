import { IsDate, IsOptional, IsString, IsUUID } from 'class-validator';
import { ToArray } from '@keeps-node-apis/@core';
import { Type } from 'class-transformer';

export class MirroredCourseFilterDto {
  @IsOptional()
  @IsDate()
  created_date_lte: Date;

  @IsOptional()
  @IsDate()
  created_date_gte: Date;

  @IsOptional()
  @IsUUID()
  integrationId: string;

  @IsOptional()
  @Type(() => Array<string>)
  @ToArray()
  category: string[];

  @IsOptional()
  @Type(() => Array<string>)
  @ToArray()
  status: string[];

  @IsOptional()
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  course_status: string[];

  @IsOptional()
  @IsString()
  courseId: string;
}
