import { IsDateString, IsOptional, IsEmail, IsString } from 'class-validator';
import { CoursesInputDTO } from './courses-input.dto';

export class CoursesCompletedFilterDTO extends CoursesInputDTO {
  @IsDateString()
  @IsOptional()
  startAt?: string;

  @IsDateString()
  @IsOptional()
  completedAt?: string;

  @IsEmail()
  @IsOptional()
  email?: string;

  @IsString()
  @IsOptional()
  enrollment?: string;
}
