import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsString, IsUUID } from 'class-validator';

export class CreateCourseDto {
  @IsNotEmpty()
  courseId: string;

  @IsNotEmpty()
  @IsString()
  name: string;

  @IsNotEmpty()
  @IsUUID()
  integrationId: string;

  @IsNotEmpty()
  @IsString()
  category: string;

  @IsNotEmpty()
  @ApiProperty({ type: Date })
  created_date: Date;

  @IsNotEmpty()
  @IsEnum(['DISABLED', 'PUBLISHED'], { each: true })
  status: 'DISABLED' | 'PUBLISHED';

  @IsNotEmpty()
  @IsString()
  courseUrl: string;

  @IsNotEmpty()
  @IsString()
  imageUrl: string;

  @IsNotEmpty()
  @IsString()
  description: string;

  @IsNotEmpty()
  workload: number;
}
