import { NotAllowedErrorFilter, TypeOrmErrorFilter } from '@keeps-node-apis/@core';
import { Logger, ValidationPipe } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app/app.module';

async function bootstrap() {
  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  // CORS CONFIGURATION
  app.enableCors({
    allowedHeaders: '*',
    origin: '*',
    credentials: true,
  });

  const globalPrefix = 'api';
  app.setGlobalPrefix(globalPrefix);

  // Swagger configuration
  const config = new DocumentBuilder()
    .setTitle('Alura Integration API')
    .setDescription('API documentation for integration with the Alura platform')
    .setVersion('1.0')
    .addTag('alura-integration')
    .addServer('http://localhost:3000', 'Development API')
    .addServer('https://dev.api.exemplo.com', 'Stage API')
    .addServer('https://api.exemplo.com', 'Production API')
    .addApiKey(
      {
        type: 'apiKey',
        name: 'x-client',
        in: 'header',
        description: 'Tenant ID for the request',
      },
      'x-client',
    )
    .build();

  const documentFactory = () => SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('', app, documentFactory);

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  app.useGlobalFilters(new NotAllowedErrorFilter(), new TypeOrmErrorFilter());

  const port = process.env.PORT || 3000;
  await app.listen(port);
  Logger.log(`🚀 Integration Gateway Alura is running on: http://localhost:${port}/${globalPrefix}`);
}

bootstrap();
