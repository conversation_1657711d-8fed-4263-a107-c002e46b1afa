/* eslint-disable */
export default {
  displayName: 'integration-gateway-alura',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/integration-gateway-alura',
  coverageReporters: ['text-summary', ['text', { skipFull: true }], ['lcovonly', { projectRoot: __dirname }]],
  reporters: ['default', ['@casualbot/jest-sonar-reporter', { outputDirectory: 'coverage/integration-gateway-alura' }]],
  collectCoverageFrom: [
    'src/app/**/*.{js,ts}',
    '!src/app/**/*.module.{js,ts}',
    '!src/app/**/index.{js,ts}',
    '!src/app/db/**/*.{js,ts}',
    '!src/app/config/**/*.{js,ts}',
  ],
  coverageThreshold: {
    global: {
      branches: 0,
      functions: 0,
      lines: 0,
      statements: 0,
    },
  },
};
