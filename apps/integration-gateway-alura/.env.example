# DB
DB_HOST=db
DB_PORT=5432
DB_NAME=integration_gateway_alura_dev_db
DB_USER=postgres
DB_PASS=postgres
DB_DIALECT=postgres
DB_DEBUG=true
# SET TRUE ONLY TO LOCAL DATABASE
MIGRATIONS_RUN=false

#FOR <PERSON>ECK SIGNATURES
TOKEN_ALURA_GET_ALL_COURSES=c8996aa897594fae93f0998d3154ca66
TOKEN_ALURA_GET_FINISHED_ENROLLMENTS=16a575253691414d8b11549b21c2d548
TOKEN_ALURA_GET_PROGRESS_ENROLLMENTS=f48ac309d3114a96b18865923fa2457c
TOKEN_ALURA_SSO=4adfa8f52b634156a94d143bee5130fa

# AUTHENTICATION
AUTH_URL=https://iam.keepsdev.com/auth/
AUTH_REALM=keeps-dev
AUTH_REALM_PUBLIC_KEY=
AUTH_DEBUG=false

# USER AUTHORIZATION
MYACCOUNT_API_URL=https://learning-platform-api-stage.keepsdev.com/myaccount-v2
OLD_MYACCOUNT_API_URL=https://learning-platform-api-stage.keepsdev.com/myaccount
KONQUEST_APPLICATION_ID=0abf08ea-d252-4d7c-ab45-ab3f9135c288
MYACCOUNT_SUPER_TOKEN=

# RABBIT
RABBITMQ_URI=amqp://admin:admin@rabbitmq:5672
RABBITMQ_BELL_NOTIFICATIONS_QUEUE=bell
BELL_NOTIFICATIONS_QUEUE_IS_DURABLE=true

# KONQUEST INTEGRATION
KEEPS_SECRET_TOKEN_INTEGRATION=
KONQUEST_URL=https://learning-platform-api-stage.keepsdev.com/konquest

#################################################
# CONTAINERS CONFIG - ONLY DEV ENVIRONMENT
#################################################
# RABBITMQ
RABBITMQ_DEFAULT_USER=admin
RABBITMQ_DEFAULT_PASS=admin

# POSTGRES
POSTGRES_PASSWORD=postgres
