{"name": "integration-gateway-alura", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/integration-gateway-alura/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/integration-gateway-alura", "main": "apps/integration-gateway-alura/src/main.ts", "tsConfig": "apps/integration-gateway-alura/tsconfig.app.json", "assets": ["apps/integration-gateway-alura/src/assets"], "webpackConfig": "apps/integration-gateway-alura/webpack.config.js", "sourceMap": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "options": {"buildTarget": "integration-gateway-alura:build"}, "configurations": {"development": {"buildTarget": "integration-gateway-alura:build:development", "buildTargetOptions": {"sourceMap": true}}, "production": {"buildTarget": "integration-gateway-alura:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/integration-gateway-alura/jest.config.ts"}, "configurations": {"dev": {"ci": true, "codeCoverage": true, "coverageReporters": ["html", "text-summary", "lcov"]}}, "defaultConfiguration": "dev"}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "integration-gateway-alura", "hostUrl": "https://sonar.keepsdev.com", "projectKey": "integration-gateway-alura-api", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "**/db/**, **/assets/**, libs/**", "extra": {"sonar.coverage.exclusions": "apps/integration-gateway-alura/src/app/db/**,apps/integration-gateway-alura/src/app/config/**,apps/integration-gateway-alura/src/**/*.module.ts,apps/integration-gateway-alura/src/main.ts,apps/integration-gateway-alura/src/**/index.ts,apps/integration-gateway-alura/src/**/index.ts,apps/integration-gateway-alura/src/app/common/repositories/**/*.repository.ts", "sonar.cpd.exclusions": "apps/integration-gateway-alura/src/app/db/**,apps/integration-gateway-alura/src/assets/**", "sonar.testExecutionReportPaths": "coverage/integration-gateway-alura/jest-sonar.xml", "sonar.plugins.downloadOnlyRequired": "true"}}}, "build-migration-config": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "isolatedConfig": true, "webpackConfig": "apps/integration-gateway-alura/webpack.config.js", "outputPath": "dist/apps/typeorm-migration", "main": "apps/integration-gateway-alura/src/app/db/typeorm.config.ts", "tsConfig": "apps/integration-gateway-alura/tsconfig.app.json"}}, "typeorm-generate-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/integration-gateway-alura", "commands": ["npx typeorm migration:generate -d ../../dist/apps/typeorm-migration/main.js ./src/app/db/migrations/{args.migration-name}"]}, "dependsOn": ["build-migration-config"]}, "typeorm-run-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/integration-gateway-alura", "commands": ["npx typeorm -d ../../dist/apps/typeorm-migration/main.js migration:run"]}, "dependsOn": ["build-migration-config"]}}}