version: '3.8'

services:
  api:
    image: keeps-integration-gateway-alura:latest
    container_name: keeps-integration-gateway-alura-api
    build:
      context: ../../
      dockerfile: apps/integration-gateway-alura/.docker/dev/Dockerfile
    ports:
      - 3000:3000
    env_file:
      - ./.env
    volumes:
      - ../../:/usr/src/app
    command: ./apps/integration-gateway-alura/.docker/dev/start.sh
    depends_on:
      - db

  db:
    image: keeps-integration-gateway-alura-db
    container_name: keeps-integration-gateway-alura-db
    tty: true
    build:
      context: .
      dockerfile: .docker/postgres/Dockerfile
    env_file:
      - ./.env
    volumes:
      - .docker/db-data:/var/lib/postgresql/data
      - .docker/postgres/:/docker-entrypoint-initdb.d
    ports:
      - 5432:5432

  rabbitmq:
    image: 'rabbitmq:3-management'
    container_name: keeps-rabbitmq
    ports:
      - 5672:5672
      - 15672:15672
    env_file:
      - ./.env
