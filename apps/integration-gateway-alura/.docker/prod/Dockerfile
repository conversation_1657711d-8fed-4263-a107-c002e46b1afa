FROM node:20-alpine AS build

WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install

COPY . .

RUN npm run build --name=integration-gateway-alura


FROM node:20-alpine AS production

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /usr/src/app

COPY package*.json ./

RUN npm install --only=prod

COPY --from=build /usr/src/app/dist/apps/integration-gateway-alura ./dist

CMD ["node", "dist/main"]

EXPOSE 3000
