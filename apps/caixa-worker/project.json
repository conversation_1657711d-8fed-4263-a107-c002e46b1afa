{"name": "caixa-worker", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/caixa-worker/src", "projectType": "application", "tags": [], "targets": {"build": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/caixa-worker", "main": "apps/caixa-worker/src/main.ts", "tsConfig": "apps/caixa-worker/tsconfig.app.json", "assets": ["apps/caixa-worker/src/assets"], "webpackConfig": "apps/caixa-worker/webpack.config.js", "generatePackageJson": true}, "configurations": {"development": {}, "production": {}}}, "serve": {"executor": "@nx/js:node", "defaultConfiguration": "development", "dependsOn": ["build"], "options": {"buildTarget": "caixa-worker:build", "runBuildTargetDependencies": false}, "configurations": {"development": {"buildTarget": "caixa-worker:build:development"}, "production": {"buildTarget": "caixa-worker:build:production"}}}, "lint": {"executor": "@nx/eslint:lint"}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/caixa-worker/jest.config.ts"}, "configurations": {"dev": {"ci": true, "codeCoverage": true, "coverageReporters": ["html", "text-summary", "lcov"]}}, "defaultConfiguration": "dev"}, "sonar": {"executor": "@koliveira15/nx-sonarqube:scan", "options": {"name": "Caixa Worker", "hostUrl": "https://sonar.keepsdev.com", "projectKey": "caixa-worker", "skipTargetDefaults": false, "branches": false, "qualityGate": true, "qualityGateTimeout": "300", "skipImplicitDeps": false, "exclusions": "**/db/**, **/assets/**, libs/**", "extra": {"sonar.coverage.exclusions": "**/db/**, **/assets/**, libs/**,**/*.module.ts,**/index.ts,apps/caixa-worker/src/main.ts", "sonar.testExecutionReportPaths": "coverage/caixa-worker/jest-sonar.xml", "sonar.plugins.downloadOnlyRequired": "true"}}}, "build-migration-config": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "isolatedConfig": true, "webpackConfig": "apps/caixa-worker/webpack.config.js", "outputPath": "dist/apps/typeorm-migration", "main": "apps/caixa-worker/src/app/db/typeorm.config.ts", "tsConfig": "apps/caixa-worker/tsconfig.app.json"}}, "typeorm-generate-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/caixa-worker", "commands": ["echo {args.migration-name} / {args.name}", "npx typeorm migration:generate -d ../../dist/apps/typeorm-migration/main.js ./src/app/db/migrations/{args.migration-name}"]}, "dependsOn": ["build-migration-config"]}, "typeorm-run-migrations": {"executor": "nx:run-commands", "outputs": ["{options.outputPath}"], "options": {"cwd": "apps/caixa-worker", "commands": ["npx typeorm -d ../../dist/apps/typeorm-migration/main.js migration:run"]}, "dependsOn": ["build-migration-config"]}}}