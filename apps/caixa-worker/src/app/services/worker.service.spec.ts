import { WorkerService } from './worker.service';
import { PartnersSyncService } from '../partners';

describe('WorkerService', () => {
  let service: WorkerService;
  let partnersSyncService: jest.Mocked<PartnersSyncService>;

  beforeEach(async () => {
    partnersSyncService = {
      storePartners: jest.fn(),
      syncWithElastic: jest.fn(),
    } as unknown as jest.Mocked<PartnersSyncService>;

    service = new WorkerService(partnersSyncService);
  });

  describe('parsePartners', () => {
    it('should run the partners parse job', async () => {
      await service.parsePartners();

      expect(partnersSyncService.storePartners).toHaveBeenCalled();
    });

    it('should thrown an error when something goes wrong', async () => {
      const error = new Error('Error while reading database');
      partnersSyncService.storePartners.mockRejectedValueOnce(error);

      await expect(service.parsePartners()).rejects.toThrow(error.message);
    });
  });

  describe('syncPartners', () => {
    it('should run the partners synchronization job', async () => {
      await service.syncPartners();

      expect(partnersSyncService.syncWithElastic).toHaveBeenCalled();
    });

    it('should thrown an error when something goes wrong', async () => {
      const error = new Error('Error while reading database');
      partnersSyncService.syncWithElastic.mockRejectedValueOnce(error);

      await expect(service.syncPartners()).rejects.toThrow(error.message);
    });
  });
});
