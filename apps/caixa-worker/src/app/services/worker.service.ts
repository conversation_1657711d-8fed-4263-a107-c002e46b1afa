import { Injectable, Logger } from '@nestjs/common';
// import { <PERSON>ron } from '@nestjs/schedule';
import { PartnersSyncService } from '../partners';

// const EVERY_THURSDAY_3_AM = '0 3 * * 4';
// const EVERY_FRIDAY_3_AM = '0 3 * * 5';

@Injectable()
export class WorkerService {
  private readonly logger = new Logger(WorkerService.name);

  constructor(private readonly partnersSyncService: PartnersSyncService) {}

  // @Cron(EVERY_THURSDAY_3_AM)
  async parsePartners() {
    this.logger.debug('Running partners parse job');
    try {
      await this.partnersSyncService.storePartners();
    } catch (e) {
      this.logger.error('Error while parsing partners', e);
      throw e;
    }
  }

  // @Cron(EVERY_FRIDAY_3_AM)
  async syncPartners() {
    this.logger.debug('Running synchronization job');
    try {
      await this.partnersSyncService.syncWithElastic();
    } catch (e) {
      this.logger.error('Error while synchronizing partners', e);
      throw e;
    }
  }
}
