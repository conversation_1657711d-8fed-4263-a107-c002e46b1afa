import { ElasticSearchClient } from './elastic-search.client';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { Chance } from 'chance';
import { PartnerSyncDto } from '../common/dtos';

describe('ElasticSearchClient', () => {
  let service: ElasticSearchClient;
  let elasticServiceMock: jest.Mocked<ElasticsearchService>;
  const chance = new Chance();
  const index = 'search-caixa-partners';

  beforeEach(async () => {
    elasticServiceMock = {
      bulk: jest.fn(),
      search: jest.fn(),
    } as unknown as jest.Mocked<ElasticsearchService>;
    service = new ElasticSearchClient(elasticServiceMock);
  });

  it('should make bulk updates', async () => {
    const firstPartner = { name: chance.name(), convention_number: chance.integer() } as unknown as PartnerSyncDto;
    const secondPartner = { name: chance.name(), convention_number: chance.integer() } as unknown as PartnerSyncDto;
    const expectedOperations = [
      { update: { _id: firstPartner.convention_number } },
      { doc: firstPartner, doc_as_upsert: true },
      { update: { _id: secondPartner.convention_number } },
      { doc: secondPartner, doc_as_upsert: true },
    ];

    await service.createBatchPartners([firstPartner, secondPartner]);

    expect(elasticServiceMock.bulk).toHaveBeenCalledWith({
      index,
      operations: expectedOperations,
    });
  });
});
