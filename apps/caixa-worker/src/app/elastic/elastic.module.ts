import { Modu<PERSON> } from '@nestjs/common';
import { ElasticsearchModule } from '@nestjs/elasticsearch';
import { ConfigService } from '@nestjs/config';
import { ElasticSearchClient } from './elastic-search.client';

@Module({
  imports: [
    ElasticsearchModule.registerAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        node: configService.get('ELASTICSEARCH_SERVER'),
        auth: {
          username: configService.get('ELASTICSEARCH_USER'),
          password: configService.get('ELASTICSEARCH_PASSWORD'),
        },
        maxRetries: 5,
        requestTimeout: 60 * 1000,
      }),
    }),
  ],
  providers: [ElasticSearchClient],
  exports: [ElasticsearchModule, ElasticSearchClient],
})
export class ElasticModule {}
