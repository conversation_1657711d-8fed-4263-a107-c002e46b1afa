import { QueryDslOperator, QueryDslQueryStringQuery } from '@elastic/elasticsearch/lib/api/types';

export abstract class QueryTermBuilder {
  private static SPECIAL_CHARACTERS_REGEX = /([&|+-=><!(){}[\]^"~*?:\\/])/g;

  static parseFuzzyQueryTerm(term: string): string {
    if (!term) {
      return '';
    }
    const escapedTerm = this.escapeSpecialCharacters(term);
    const tokens = this.tokenizeAndAppendFuzziness(escapedTerm);
    return tokens.join(' ');
  }

  static createQueryStringForTerm(
    term: string,
    fieldName: string,
    boost = 2,
    operator: QueryDslOperator = 'AND',
    analyzer?: string,
  ): QueryDslQueryStringQuery {
    return {
      query: QueryTermBuilder.parseFuzzyQueryTerm(term),
      default_field: fieldName,
      default_operator: operator,
      boost,
      analyzer,
    };
  }

  private static escapeSpecialCharacters(term: string): string {
    return term.replace(QueryTermBuilder.SPECIAL_CHARACTERS_REGEX, '\\$1').trim();
  }

  private static tokenizeAndAppendFuzziness(escapedTerm: string): string[] {
    return escapedTerm.split(/\s+/).map((token) => `${token}~`);
  }
}
