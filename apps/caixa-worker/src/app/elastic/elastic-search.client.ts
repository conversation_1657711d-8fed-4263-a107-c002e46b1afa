import { Injectable } from '@nestjs/common';
import { ElasticsearchService } from '@nestjs/elasticsearch';
import { PartnerSyncDto } from '../common/dtos';

@Injectable()
export class ElasticSearchClient {
  private readonly caixaApiIndex = 'search-caixa-partners';

  constructor(private readonly es: ElasticsearchService) {}

  async createBatchPartners(partners: PartnerSyncDto[]) {
    const operations = this.upsertOperations(partners);
    return await this.es.bulk({ index: this.caixaApiIndex, operations });
  }

  private upsertOperations(partners: PartnerSyncDto[]) {
    const operations = [];
    for (const partner of partners) {
      operations.push({ update: { _id: partner.convention_number } }, { doc: partner, doc_as_upsert: true });
    }
    return operations;
  }
}
