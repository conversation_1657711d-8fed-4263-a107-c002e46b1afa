import { QueryTermBuilder } from './query-term.builder';
import { QueryDslQueryStringQuery } from '@elastic/elasticsearch/lib/api/types';

describe('QueryTermBuilder', () => {
  describe('parseFuzzyQueryTerm', () => {
    it('should create a fuzzy query string composed of fuzziness search tokens, escaping special characters', () => {
      const originalSearch = 'Pão de Queijo & Doce de Leite!';
      const expectedSearch = 'Pão~ de~ Queijo~ \\&~ Doce~ de~ Leite\\!~';

      expect(QueryTermBuilder.parseFuzzyQueryTerm(originalSearch)).toBe(expectedSearch);
    });

    it('should handle nullish values', () => {
      expect(QueryTermBuilder.parseFuzzyQueryTerm('')).toBe('');
      expect(QueryTermBuilder.parseFuzzyQueryTerm(null)).toBe('');
      expect(QueryTermBuilder.parseFuzzyQueryTerm(undefined)).toBe('');
    });
  });

  describe('createQueryStringForTerm', () => {
    it('should create a dsl query string query applying fuzziness to the search term', () => {
      const searchValue = 'Pão de Queijo';
      const expectedResult: QueryDslQueryStringQuery = {
        query: QueryTermBuilder.parseFuzzyQueryTerm(searchValue),
        default_field: 'name',
        default_operator: 'OR',
        boost: 2,
        analyzer: 'folding',
      };

      expect(QueryTermBuilder.createQueryStringForTerm(searchValue, 'name', 2, 'OR', 'folding')).toEqual(
        expectedResult,
      );
    });
  });
});
