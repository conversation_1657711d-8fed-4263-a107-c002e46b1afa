import { MigrationInterface, QueryRunner } from "typeorm";

export class INITIAL1733430908291 implements MigrationInterface {
    name = 'INITIAL1733430908291'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "partners" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "data" json NOT NULL, CONSTRAINT "PK_998645b20820e4ab99aeae03b41" PRIMARY KEY ("id"))`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`DROP TABLE "partners"`);
    }

}
