import { config } from 'dotenv';
import { DataSource, DataSourceOptions } from 'typeorm';
import { entitiesAndMigrations } from './entities-and-migrations';

config();

// Set host to empty or localhost in .env when generating migrations
const host = process.env.DB_HOST || 'localhost';

export const DATASOURCE_CONFIG: DataSourceOptions = {
  type: 'postgres',
  host,
  port: +process.env.DB_PORT || 5432,
  username: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  synchronize: false,
  logging: process.env.DB_DEBUG === 'true',
  migrations: entitiesAndMigrations.migrations,
  entities: entitiesAndMigrations.entities,
  migrationsRun: process.env.MIGRATIONS_RUN === 'true',
  extra: {
    max: 4,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },
};

export default new DataSource(DATASOURCE_CONFIG);
