import { Injectable } from '@nestjs/common';
import { PartnersRepository } from './partners-repository';
import { Partner } from '../../entities/partner.entity';
import { DataSource, Repository } from 'typeorm';
import { PageDto, PageOptionsDto, paginate } from '@keeps-node-apis/@core';
import { plainToInstance } from 'class-transformer';
import { PartnerSyncDto } from '../../common/dtos';

@Injectable()
export class PartnersTypeOrmRepository implements PartnersRepository {
  private readonly repository: Repository<Partner>;

  constructor(private readonly dataSource: DataSource) {
    this.repository = this.dataSource.getRepository(Partner);
  }

  async batchAdd(partners: PartnerSyncDto[]): Promise<Partial<Partner>[]> {
    const entities: Partial<Partner>[] = partners.map((partner) => ({ data: partner }));
    const result = await this.repository.createQueryBuilder().insert().values(entities).execute();
    return result.identifiers;
  }

  async list(params: PageOptionsDto): Promise<PageDto<Partner>> {
    const queryBuilder = this.repository.createQueryBuilder('partner');
    const paginatedResponse = await paginate<Partner>(queryBuilder, params, 'partner');
    return plainToInstance(PageDto<Partner>, { ...paginatedResponse, items: paginatedResponse.items });
  }

  clear(): Promise<void> {
    return this.repository.clear();
  }
}
