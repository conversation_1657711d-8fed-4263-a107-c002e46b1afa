import { Partner } from '../../entities/partner.entity';
import { PageDto, PageOptionsDto } from '@keeps-node-apis/@core';
import { PartnerSyncDto } from '../../common/dtos';

export abstract class PartnersRepository {
  abstract batchAdd(partners: PartnerSyncDto[]): Promise<Partial<Partner>[]>;

  abstract list(params: PageOptionsDto): Promise<PageDto<Partner>>;

  abstract clear(): Promise<void>;
}
