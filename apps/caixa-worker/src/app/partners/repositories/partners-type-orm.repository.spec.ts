import { PartnersTypeOrmRepository } from './partners-type-orm.repository';
import { DataSource, InsertQueryBuilder, InsertResult, Repository } from 'typeorm';
import { Partner } from '../../entities/partner.entity';
import { Chance } from 'chance';
import { PartnerSyncDto } from '../../common/dtos';

describe('PartnersTypeOrmRepository', () => {
  let repository: PartnersTypeOrmRepository;
  let typeOrmRepositoryMock: jest.Mocked<Repository<Partner>>;
  let dataSourceStub: jest.Mocked<DataSource>;
  const chance = new Chance();
  let queryBuilderMock: jest.Mocked<InsertQueryBuilder<Partner>>;

  beforeEach(() => {
    queryBuilderMock = {
      insert: jest.fn().mockReturnThis(),
      execute: jest.fn(),
      values: jest.fn().mockReturnThis(),
      skip: jest.fn().mockReturnThis(),
      take: jest.fn().mockReturnThis(),
      getCount: jest.fn().mockResolvedValue(1),
      getRawAndEntities: jest.fn().mockReturnValue({ entities: [] }),
    } as unknown as jest.Mocked<InsertQueryBuilder<Partner>>;

    typeOrmRepositoryMock = {
      createQueryBuilder: jest.fn().mockReturnValue(queryBuilderMock),
      clear: jest.fn(),
    } as unknown as jest.Mocked<Repository<Partner>>;

    dataSourceStub = {
      getRepository: jest.fn().mockReturnValue(typeOrmRepositoryMock),
    } as unknown as jest.Mocked<DataSource>;

    repository = new PartnersTypeOrmRepository(dataSourceStub);
  });

  it('add multiple entities in batch', async () => {
    const input = [{ name: chance.name() }, { name: chance.name() }, { name: chance.name() }] as PartnerSyncDto[];
    const expectedValues = input.map((value) => ({ data: value }));
    queryBuilderMock.execute.mockResolvedValueOnce({ identifiers: [] } as InsertResult);

    await repository.batchAdd(input);

    expect(queryBuilderMock.insert).toHaveBeenCalled();
    expect(queryBuilderMock.values).toHaveBeenCalledWith(expectedValues);
    expect(queryBuilderMock.execute).toHaveBeenCalled();
  });

  it('should clear the partners table', async () => {
    await repository.clear();

    expect(typeOrmRepositoryMock.clear).toHaveBeenCalled();
  });
});
