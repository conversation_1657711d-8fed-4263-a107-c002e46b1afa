import { Module } from '@nestjs/common';
import { PartnersSyncService } from './services';
import { AbstractPartnersParser, FSPartnersParser } from './parsers';
import { PartnersRepository, PartnersTypeOrmRepository } from './repositories';
import { ElasticModule } from '../elastic';

@Module({
  providers: [
    PartnersSyncService,
    {
      provide: AbstractPartnersParser,
      useClass: FSPartnersParser,
    },
    { provide: PartnersRepository, useClass: PartnersTypeOrmRepository },
  ],
  imports: [ElasticModule],
  exports: [PartnersSyncService],
})
export class PartnersModule {}
