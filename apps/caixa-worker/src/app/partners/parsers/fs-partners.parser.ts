import { AbstractPartnersParser } from './abstract-partners-parser';
import { resolve } from 'path';
import { readFileSync } from 'fs';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class FSPartnersParser extends AbstractPartnersParser {
  private logger = new Logger(FSPartnersParser.name);

  constructor(public configService: ConfigService) {
    super();
  }

  readFile(): Buffer {
    const path = this.configService.get('PARTNERS_FILE_PATH') || '';
    const filePath = resolve(__dirname, path);
    this.logger.debug(`Reading file ${filePath}`);
    try {
      return readFileSync(filePath);
    } catch (e) {
      this.logger.debug(e);
    }
  }
}
