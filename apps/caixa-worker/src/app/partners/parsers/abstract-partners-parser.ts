import readXlsxFile, { Error as ExcelError, Row } from 'read-excel-file/node';
import { PARTNERS_IMPORT_SCHEMA } from './import-schemas';
import { PartnerSyncDto } from '../../common/dtos';

export abstract class AbstractPartnersParser {
  abstract readFile(): Buffer;

  async parseFile(buffer: Buffer): Promise<PartnerSyncDto[]> {
    if (!buffer) {
      return [];
    }
    const { rows, errors } = await readXlsxFile(buffer, {
      schema: PARTNERS_IMPORT_SCHEMA,
      transformData: this.transformPhone,
    });
    if (errors.length) {
      this.handleErrors(errors);
    }
    return rows;
  }

  private transformPhone(data: Row[]): Row[] {
    return data.map((row) => {
      const areaCode = row.at(10) as string;
      const phone = row.at(11) as string;
      if (areaCode !== null && phone) {
        row.splice(11, 1, `(${areaCode}) ${phone}`);
      }
      return row;
    });
  }

  private handleErrors(errors: ExcelError[]) {
    const errorMessage = errors
      .map(({ reason, row, column }) => `Error while processing column ${column} at row ${row} - ${reason}`)
      .join('\n');
    throw new Error(errorMessage);
  }
}
