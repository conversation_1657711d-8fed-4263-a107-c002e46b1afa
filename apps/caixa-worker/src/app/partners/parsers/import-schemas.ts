import { Schema } from 'read-excel-file/types';
import { PartnerSyncDto } from '../../common/dtos';

// We use a function to parse the values in the number column because some registers have a date as value
const numberParser = (value: any): string => value;

export const PARTNERS_IMPORT_SCHEMA: Schema<PartnerSyncDto> = {
  ESTRUTURA_GECAN: {
    prop: 'gecan_structure',
    type: String,
  },
  NOME_AGENCIA: {
    prop: 'agency_name',
    type: String,
  },
  COD_SEV: { prop: 'sev_code', type: String },
  NOME_SEV: { prop: 'sev_name', type: String },
  COD_SR: { prop: 'sr_code', type: String },
  NOME_SR: { prop: 'sr_name', type: String },
  'Status do parceiro': { prop: 'status', type: String },
  'CONVENIO PARCEIRO': { prop: 'convention_number', type: String },
  'NOME PARCEIRO': { prop: 'name', type: String },
  'CNPJ PARCEIRO': { prop: 'cnpj', type: String },
  Telefone: { prop: 'phone', type: String },
  Email: { prop: 'email', type: String },
  NOME_SN: { prop: 'sn_name', type: String },
  'TIPO PARCEIRO': { prop: 'type', type: String },
  TIPO_REDE: { prop: 'network_type', type: String },
  'PORTE UNIDADE': { prop: 'unit_size', type: String },
  PORTE_SR: { prop: 'sr_size', type: String },
  'ENDERECO PARCEIRO': { prop: 'address', type: String },
  NUMERO: { prop: 'number', type: numberParser },
  BAIRRO: { prop: 'district', type: String },
  CEP: { prop: 'zip_code', type: String },
  NOME_MUNIC: { prop: 'city', type: String },
  UF: { prop: 'uf', type: String },
  REGIAO: { prop: 'region', type: String },
  MATRICULA_CONSULTOR: { prop: 'consultant_enrollment', type: String },
  CONSULTOR_CVP: { prop: 'consultant_cvp', type: String },
  EMAIL_CONSULTOR: { prop: 'consultant_email', type: String },
  GERENCIA: { prop: 'manager', type: String },
  GERENTE_CVP: { prop: 'cvp_manager', type: String },
  EMAIL_GERENTE: { prop: 'manager_email', type: String },
  'CONSULTOR MASC': { prop: 'consultant_masc', type: String },
  'CARTEIRA ESTRATEGICA': { prop: 'strategic_wallet', type: String },
  GCN: { prop: 'gcn', type: String },
};
