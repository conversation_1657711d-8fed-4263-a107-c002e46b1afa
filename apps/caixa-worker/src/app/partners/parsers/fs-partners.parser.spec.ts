import { PartnerSyncDto } from '../../common/dtos';

const readFileMock = jest.fn();

jest.mock('read-excel-file/node', () => ({
  __esModule: true,
  default: readFileMock,
}));

import fs from 'fs';
import path from 'path';
import { FSPartnersParser } from './fs-partners.parser';
import { ConfigService } from '@nestjs/config';
import { PARTNERS_IMPORT_SCHEMA } from './import-schemas';
import { Chance } from 'chance';

describe('FSPartnersParser', () => {
  let configServiceMock: jest.Mocked<ConfigService>;
  let parser: FSPartnersParser;
  const filePath = 'file/path/mock-file.xlsx';
  let readFileSyncSpy: jest.SpyInstance;
  let resolveSpy: jest.SpyInstance;
  const mockBuffer = Buffer.from('mock_content');
  const chance = new Chance();

  beforeEach(() => {
    jest.useFakeTimers();
  });

  beforeEach(() => {
    configServiceMock = { get: jest.fn().mockReturnValue(filePath) } as unknown as jest.Mocked<ConfigService>;

    readFileSyncSpy = jest.spyOn(fs, 'readFileSync').mockImplementation(() => mockBuffer);
    resolveSpy = jest.spyOn(path, 'resolve').mockImplementation(() => filePath);

    parser = new FSPartnersParser(configServiceMock);
    jest.advanceTimersByTime(1);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.useRealTimers();
  });

  it('should read the partners spreadsheet', async () => {
    parser.readFile();

    expect(configServiceMock.get).toHaveBeenCalledWith('PARTNERS_FILE_PATH');
    expect(resolveSpy).toHaveBeenCalledWith(expect.any(String), filePath);
    expect(readFileSyncSpy).toHaveBeenCalledWith(filePath);
  });

  it('should parse the previously read file', async () => {
    const expectedResult = [{ name: chance.name() }] as PartnerSyncDto[];
    readFileMock.mockResolvedValueOnce({ rows: expectedResult, errors: [] } as any);
    const buffer = parser.readFile();

    const result = await parser.parseFile(buffer);

    expect(readFileMock).toHaveBeenCalledWith(mockBuffer, {
      schema: PARTNERS_IMPORT_SCHEMA,
      transformData: expect.any(Function),
    });
    expect(result).toBe(expectedResult);
  });

  it('should throw a formatted error', () => {
    readFileMock.mockResolvedValueOnce({
      rows: [],
      errors: [{ reason: 'invalid data', column: 'name', row: 1 }],
    } as any);

    expect(async () => {
      const buffer = parser.readFile();
      await parser.parseFile(buffer);
    }).rejects.toThrow('Error while processing column name at row 1 - invalid data');
  });
});
