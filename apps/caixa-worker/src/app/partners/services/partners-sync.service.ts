import { Injectable, Logger } from '@nestjs/common';
import { AbstractPartnersParser } from '../parsers';
import { PartnersRepository } from '../repositories';
import { ElasticSearchClient } from '../../elastic';
import { PageOptionsDto } from '@keeps-node-apis/@core';
import { plainToInstance } from 'class-transformer';
import { Partner } from '../../entities/partner.entity';
import { BulkResponse } from '@elastic/elasticsearch/lib/api/types';
import { PartnerSyncDto } from '../../common/dtos';

@Injectable()
export class PartnersSyncService {
  private readonly logger = new Logger(PartnersSyncService.name);
  private readonly ITEMS_PER_BATCH = 100;

  private static dtoFromPartners(partners: Partner[]): PartnerSyncDto[] {
    return partners.map((partner) => partner.data);
  }

  constructor(
    private readonly partnersParser: AbstractPartnersParser,
    private readonly partnersRepository: PartnersRepository,
    private readonly elasticSearchClient: ElasticSearchClient,
  ) {}

  async storePartners() {
    const buffer = this.partnersParser.readFile();
    const partners = await this.partnersParser.parseFile(buffer);
    await this.partnersRepository.batchAdd(partners);
    this.logger.debug(`Saved ${partners.length} partner(s) in the database.`);
  }

  async syncWithElastic() {
    await this.syncBatchRecursively();
    await this.clearDatabase();
  }

  private async syncBatchRecursively(page = 1) {
    const params = this.buildPaginationParams(page);
    const { hasNextPage, items, total } = await this.partnersRepository.list(params);

    if (total === 0) {
      this.logger.debug(`No items available for synchronization.`);
      return;
    }

    const remaining = total - this.ITEMS_PER_BATCH * page + items.length;
    const partnerDtoList = PartnersSyncService.dtoFromPartners(items);
    this.logger.debug(`Syncing partners batch with ElasticSearch Index - ${remaining} item(s) remaining.`);
    const response = await this.elasticSearchClient.createBatchPartners(partnerDtoList);
    if (response?.errors) {
      this.handleSyncErrors(response);
    }

    if (!hasNextPage) {
      this.logger.debug(
        `Partners synchronization with ElasticSearch completed successfully! ${total} item(s) synchronized.`,
      );
      return;
    }

    await this.syncBatchRecursively(page + 1);
  }

  private async clearDatabase() {
    this.logger.debug(`Clearing partners from the database.`);
    return this.partnersRepository.clear();
  }

  private buildPaginationParams(currentPage: number) {
    return plainToInstance(PageOptionsDto, { perPage: this.ITEMS_PER_BATCH, page: currentPage });
  }

  private handleSyncErrors(response: BulkResponse) {
    const errors = response.items.map((item) => item.update.error).filter((error) => error !== null);
    this.logger.error(errors);
  }
}
