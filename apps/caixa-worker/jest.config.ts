export default {
  displayName: 'caixa-worker',
  preset: '../../jest.preset.js',
  testEnvironment: 'node',
  transform: {
    '^.+\\.[tj]s$': ['ts-jest', { tsconfig: '<rootDir>/tsconfig.spec.json' }],
  },
  moduleFileExtensions: ['ts', 'js', 'html'],
  coverageDirectory: '../../coverage/caixa-worker',
  coverageReporters: ['text-summary', ['text', { skipFull: true }], ['lcovonly', { projectRoot: __dirname }]],
  reporters: ['default', ['@casualbot/jest-sonar-reporter', { outputDirectory: 'coverage/caixa-worker' }]],
  coverageThreshold: {
    global: {
      branches: 50,
      functions: 50,
      lines: 50,
      statements: 50,
    },
  },
};
