version: '3'

services:
  worker:
    image: caixa-worker:latest
    container_name: caixa-worker-worker
    build:
      context: ../../
      dockerfile: apps/caixa-worker/.docker/dev/Dockerfile
    env_file:
      - ./.env
    volumes:
      - ../../:/usr/src/app
    command: ./apps/caixa-worker/.docker/dev/start.sh
    depends_on:
      - db

  db:
    image: caixa-worker-db
    container_name: caixa-worker-db
    tty: true
    build:
      context: .
      dockerfile: .docker/postgres/Dockerfile
    env_file:
      - ./.env
    volumes:
      - .docker/db-data:/var/lib/postgresql/data
      - .docker/postgres/:/docker-entrypoint-initdb.d
    ports:
      - 5432:5432
