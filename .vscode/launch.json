{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debbug Regulatory Compliance App", "program": "${workspaceFolder}/apps/regulatory-compliance/src/main.ts", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register", "-r", "tsconfig-paths/register"], "sourceMaps": true, "cwd": "${workspaceFolder}/apps/regulatory-compliance", "internalConsoleOptions": "openOnSessionStart", "skipFiles": ["<node_internals>/**"]}, {"type": "node", "request": "launch", "name": "Debbug Notification App", "program": "${workspaceFolder}/apps/notification/src/main.ts", "runtimeArgs": ["--<PERSON><PERSON><PERSON>", "-r", "ts-node/register", "-r", "tsconfig-paths/register"], "sourceMaps": true, "cwd": "${workspaceFolder}/apps/notification", "internalConsoleOptions": "openOnSessionStart", "skipFiles": ["<node_internals>/**"]}, {"name": "Attach to NestJS API", "port": 9229, "request": "attach", "skipFiles": ["<node_internals>/**"], "type": "node", "cwd": "${workspaceFolder}/apps/regulatory-compliance"}, {"type": "node-terminal", "name": "Run Script: start notification", "request": "launch", "command": "npm run start --name=notification", "cwd": "${workspaceFolder}"}]}