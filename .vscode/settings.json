{"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "editor.formatOnPaste": true, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[html]": {"editor.defaultFormatter": "vscode.html-language-features"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[css]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "cSpell.words": ["<PERSON><PERSON>", "keycloak", "<PERSON><PERSON><PERSON>", "rawbody", "typeorm"], "prettier.requireConfig": true, "markiscodecoverage.searchCriteria": "artifacts/coverage/lcov.info", "prettier.configPath": ".prettier<PERSON>", "jestrunner.codeLensSelector": "**/*.{test,spec,int-spec}.{js,jsx,ts,tsx}"}