{"recommendations": ["nrwl.angular-console", "alexcvzz.vscode-sqlite", "esbenp.prettier-vscode", "humao.rest-client", "vscode-icons-team.vscode-icons", "dbaeumer.vscode-eslint", "donjayamanne.githistory", "eamodio.gitlens", "firsttris.vscode-jest-runner", "redhat.vscode-yaml", "snyk-security.snyk-vulnerability-scanner", "ms-vsliveshare.vsliveshare", "ms-vscode-remote.remote-containers", "plex.vscode-protolint"]}